const mongoose = require('mongoose');
require('dotenv').config();

const checkDatabaseThumbnails = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    
    // Get all videos and check their thumbnails
    const videos = await db.collection('studyvideos').find().toArray();
    
    console.log(`\n📹 Found ${videos.length} videos in database:`);
    console.log('=' .repeat(80));
    
    let videosWithThumbnails = 0;
    let videosWithoutThumbnails = 0;
    
    videos.forEach((video, index) => {
      console.log(`\n${index + 1}. "${video.title || 'No title'}"`);
      console.log(`   - ID: ${video._id}`);
      console.log(`   - Subject: ${video.subject || 'No subject'}`);
      console.log(`   - Class: ${video.className || 'No class'}`);
      console.log(`   - Level: ${video.level || 'No level'}`);
      
      if (video.thumbnail && video.thumbnail.trim() !== '') {
        console.log(`   - ✅ HAS THUMBNAIL: ${video.thumbnail}`);
        console.log(`   - Thumbnail type: ${typeof video.thumbnail}`);
        console.log(`   - Thumbnail length: ${video.thumbnail.length} chars`);
        
        // Check if it's a URL
        if (video.thumbnail.startsWith('http')) {
          console.log(`   - 🌐 URL thumbnail: ${video.thumbnail.substring(0, 50)}...`);
        } else if (video.thumbnail.startsWith('data:')) {
          console.log(`   - 📄 Base64 thumbnail: ${video.thumbnail.substring(0, 50)}...`);
        } else {
          console.log(`   - 🔍 Other format: ${video.thumbnail.substring(0, 50)}...`);
        }
        
        videosWithThumbnails++;
      } else {
        console.log(`   - ❌ NO THUMBNAIL`);
        videosWithoutThumbnails++;
      }
      
      console.log(`   - VideoID: ${video.videoID || 'No videoID'}`);
      console.log(`   - VideoURL: ${video.videoUrl ? 'Has URL' : 'No URL'}`);
    });
    
    console.log('\n' + '=' .repeat(80));
    console.log(`📊 SUMMARY:`);
    console.log(`   - Total videos: ${videos.length}`);
    console.log(`   - Videos with thumbnails: ${videosWithThumbnails}`);
    console.log(`   - Videos without thumbnails: ${videosWithoutThumbnails}`);
    console.log(`   - Thumbnail coverage: ${videos.length > 0 ? Math.round((videosWithThumbnails / videos.length) * 100) : 0}%`);
    
    if (videosWithThumbnails > 0) {
      console.log('\n🎯 RECOMMENDATION: Your database thumbnails should be used!');
      console.log('   The getThumbnailUrl function has been updated to prioritize database thumbnails.');
    } else {
      console.log('\n⚠️  WARNING: No thumbnails found in database!');
      console.log('   You may need to add thumbnails to your videos.');
    }
    
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
};

checkDatabaseThumbnails();
