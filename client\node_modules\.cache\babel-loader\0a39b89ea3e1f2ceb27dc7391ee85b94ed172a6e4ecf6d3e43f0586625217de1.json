{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\NotificationBell.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { createPortal } from 'react-dom';\nimport { TbBell, TbBellRinging, TbCheck, TbX, TbSettings, TbTrash } from 'react-icons/tb';\nimport { getUserNotifications, getUnreadNotificationCount, markNotificationAsRead, markAllNotificationsAsRead } from '../../apicalls/notifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotificationBell = ({\n  className = ''\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [dropdownPosition, setDropdownPosition] = useState({\n    top: 0,\n    right: 0\n  });\n  const dropdownRef = useRef(null);\n  const buttonRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target) && buttonRef.current && !buttonRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Update dropdown position when opened\n  useEffect(() => {\n    if (isOpen && buttonRef.current) {\n      const rect = buttonRef.current.getBoundingClientRect();\n      setDropdownPosition({\n        top: rect.bottom + 8,\n        right: window.innerWidth - rect.right\n      });\n    }\n  }, [isOpen]);\n\n  // Fetch unread count periodically\n  useEffect(() => {\n    const fetchUnreadCount = async () => {\n      try {\n        const response = await getUnreadNotificationCount();\n        if (response.success) {\n          setUnreadCount(response.data.unreadCount);\n        }\n      } catch (error) {\n        console.error('Error fetching unread count:', error);\n      }\n    };\n    fetchUnreadCount();\n    const interval = setInterval(fetchUnreadCount, 30000); // Every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Fetch notifications when dropdown opens\n  useEffect(() => {\n    if (isOpen && notifications.length === 0) {\n      fetchNotifications();\n    }\n  }, [isOpen]);\n  const fetchNotifications = async (pageNum = 1, reset = false) => {\n    if (loading) return;\n    setLoading(true);\n    try {\n      const response = await getUserNotifications({\n        page: pageNum,\n        limit: 10\n      });\n      if (response.success) {\n        const newNotifications = response.data.notifications;\n        if (reset || pageNum === 1) {\n          setNotifications(newNotifications);\n        } else {\n          setNotifications(prev => [...prev, ...newNotifications]);\n        }\n        setHasMore(newNotifications.length === 10);\n        setPage(pageNum);\n        setUnreadCount(response.data.unreadCount);\n      }\n    } catch (error) {\n      console.error('Error fetching notifications:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleMarkAsRead = async notificationId => {\n    try {\n      const response = await markNotificationAsRead(notificationId);\n      if (response.success) {\n        setNotifications(prev => prev.map(notif => notif._id === notificationId ? {\n          ...notif,\n          isRead: true\n        } : notif));\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n  const handleMarkAllAsRead = async () => {\n    try {\n      const response = await markAllNotificationsAsRead();\n      if (response.success) {\n        setNotifications(prev => prev.map(notif => ({\n          ...notif,\n          isRead: true\n        })));\n        setUnreadCount(0);\n      }\n    } catch (error) {\n      console.error('Error marking all as read:', error);\n    }\n  };\n  const getNotificationIcon = type => {\n    switch (type) {\n      case 'new_exam':\n        return '🎯';\n      case 'new_study_material':\n        return '📚';\n      case 'forum_question_posted':\n        return '❓';\n      case 'forum_answer_received':\n        return '💡';\n      case 'level_up':\n        return '🎉';\n      case 'achievement_unlocked':\n        return '🏆';\n      default:\n        return '📢';\n    }\n  };\n  const formatTimeAgo = date => {\n    const now = new Date();\n    const notifDate = new Date(date);\n    const diffInMinutes = Math.floor((now - notifDate) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    return notifDate.toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative ${className}`,\n    ref: dropdownRef,\n    style: {\n      zIndex: 10000\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      ref: buttonRef,\n      onClick: e => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsOpen(!isOpen);\n      },\n      className: \"notification-bell-button relative p-2 text-gray-700 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50\",\n      style: {\n        zIndex: 10001\n      },\n      children: [unreadCount > 0 ? /*#__PURE__*/_jsxDEV(TbBellRinging, {\n        className: \"w-5 h-5\",\n        style: {\n          color: '#ef4444'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TbBell, {\n        className: \"w-5 h-5\",\n        style: {\n          color: '#374151'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"notification-badge\",\n        style: {\n          position: 'absolute',\n          bottom: '-4px',\n          left: '50%',\n          transform: 'translateX(-50%)',\n          backgroundColor: '#ef4444',\n          color: '#ffffff',\n          fontSize: '10px',\n          fontWeight: '700',\n          minWidth: '20px',\n          width: '20px',\n          height: '20px',\n          borderRadius: '50%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          border: '2px solid #ffffff',\n          boxShadow: '0 2px 8px rgba(239, 68, 68, 0.6)',\n          zIndex: 1000\n        },\n        children: unreadCount > 99 ? '99+' : unreadCount\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/createPortal( /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: dropdownRef,\n      className: \"notification-dropdown w-96 bg-white rounded-2xl shadow-2xl border border-gray-200 max-h-[500px] overflow-hidden backdrop-blur-sm\",\n      style: {\n        position: 'fixed',\n        top: dropdownPosition.top,\n        right: dropdownPosition.right,\n        zIndex: 99999,\n        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'\n      },\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-100 flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbBell, {\n            className: \"w-5 h-5 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-bold text-gray-800\",\n            children: \"Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 17\n          }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold\",\n            children: unreadCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleMarkAllAsRead,\n            className: \"text-sm text-blue-600 hover:text-blue-700 font-medium px-2 py-1 rounded hover:bg-blue-100 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-4 h-4 inline mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 21\n            }, this), \"Mark all read\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsOpen(false),\n            className: \"text-gray-400 hover:text-gray-600 p-1 rounded hover:bg-gray-100 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-h-80 overflow-y-auto\",\n        children: [loading && notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 19\n          }, this), \"Loading notifications...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 17\n        }, this) : notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8 text-center text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(TbBell, {\n            className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No notifications yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 17\n        }, this) : notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `notification-item group relative transition-all duration-200 ${!notification.isRead ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500' : 'hover:bg-gray-50'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 cursor-pointer\",\n            onClick: () => {\n              if (!notification.isRead) {\n                handleMarkAsRead(notification._id);\n              }\n              if (notification.actionUrl) {\n                window.location.href = notification.actionUrl;\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-10 h-10 rounded-full flex items-center justify-center text-lg flex-shrink-0 ${!notification.isRead ? 'bg-blue-100' : 'bg-gray-100'}`,\n                children: getNotificationIcon(notification.type)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `font-semibold text-sm line-clamp-1 ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`,\n                      children: notification.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 text-xs mt-1 line-clamp-2\",\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-400 text-xs\",\n                        children: formatTimeAgo(notification.createdAt)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 327,\n                        columnNumber: 33\n                      }, this), !notification.isRead && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-bold\",\n                        children: \"New\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 331,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              handleMarkAsRead(notification._id);\n              // Remove from local state\n              setNotifications(prev => prev.filter(n => n._id !== notification._id));\n            },\n            className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-gray-200 text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 21\n          }, this)]\n        }, notification._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 19\n        }, this)), hasMore && notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => fetchNotifications(page + 1),\n          disabled: loading,\n          className: \"w-full p-3 text-center text-blue-600 hover:bg-gray-50 text-sm font-medium disabled:opacity-50\",\n          children: loading ? 'Loading...' : 'Load more'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 9\n    }, this), document.body)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s(NotificationBell, \"cJnZm03Lhk+vvILmV/Q3blIgOI8=\");\n_c = NotificationBell;\nexport default NotificationBell;\n\n// Add CSS animations to replace Framer Motion\nconst styles = `\n.notification-bell-button {\n  transition: transform 0.2s ease;\n}\n\n.notification-bell-button:hover {\n  transform: scale(1.05);\n}\n\n.notification-bell-button:active {\n  transform: scale(0.95);\n}\n\n.notification-badge {\n  animation: badgeAppear 0.3s ease-out;\n}\n\n.notification-dropdown {\n  animation: dropdownAppear 0.3s ease-out;\n}\n\n.notification-item {\n  animation: itemSlideIn 0.3s ease-out;\n}\n\n@keyframes badgeAppear {\n  from {\n    transform: scale(0);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n@keyframes dropdownAppear {\n  from {\n    opacity: 0;\n    transform: translateY(-10px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n@keyframes itemSlideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.textContent = styles;\n  document.head.appendChild(styleSheet);\n}\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "createPortal", "TbBell", "TbBellRinging", "TbCheck", "TbX", "TbSettings", "TbTrash", "getUserNotifications", "getUnreadNotificationCount", "markNotificationAsRead", "markAllNotificationsAsRead", "jsxDEV", "_jsxDEV", "NotificationBell", "className", "_s", "isOpen", "setIsOpen", "notifications", "setNotifications", "unreadCount", "setUnreadCount", "loading", "setLoading", "page", "setPage", "hasMore", "setHasMore", "dropdownPosition", "setDropdownPosition", "top", "right", "dropdownRef", "buttonRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "rect", "getBoundingClientRect", "bottom", "window", "innerWidth", "fetchUnreadCount", "response", "success", "data", "error", "console", "interval", "setInterval", "clearInterval", "length", "fetchNotifications", "pageNum", "reset", "limit", "newNotifications", "prev", "handleMarkAsRead", "notificationId", "map", "notif", "_id", "isRead", "Math", "max", "handleMarkAllAsRead", "getNotificationIcon", "type", "formatTimeAgo", "date", "now", "Date", "notifDate", "diffInMinutes", "floor", "diffInHours", "diffInDays", "toLocaleDateString", "ref", "style", "zIndex", "children", "onClick", "e", "preventDefault", "stopPropagation", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "left", "transform", "backgroundColor", "fontSize", "fontWeight", "min<PERSON><PERSON><PERSON>", "width", "height", "borderRadius", "display", "alignItems", "justifyContent", "border", "boxShadow", "notification", "actionUrl", "location", "href", "title", "message", "createdAt", "filter", "n", "disabled", "body", "_c", "styles", "styleSheet", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/NotificationBell.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { createPortal } from 'react-dom';\nimport {\n  TbBell,\n  TbBellRinging,\n  TbCheck,\n  TbX,\n  TbSettings,\n  TbTrash\n} from 'react-icons/tb';\nimport { \n  getUserNotifications, \n  getUnreadNotificationCount,\n  markNotificationAsRead,\n  markAllNotificationsAsRead \n} from '../../apicalls/notifications';\n\nconst NotificationBell = ({ className = '' }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 });\n  const dropdownRef = useRef(null);\n  const buttonRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target) &&\n          buttonRef.current && !buttonRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Update dropdown position when opened\n  useEffect(() => {\n    if (isOpen && buttonRef.current) {\n      const rect = buttonRef.current.getBoundingClientRect();\n      setDropdownPosition({\n        top: rect.bottom + 8,\n        right: window.innerWidth - rect.right\n      });\n    }\n  }, [isOpen]);\n\n  // Fetch unread count periodically\n  useEffect(() => {\n    const fetchUnreadCount = async () => {\n      try {\n        const response = await getUnreadNotificationCount();\n        if (response.success) {\n          setUnreadCount(response.data.unreadCount);\n        }\n      } catch (error) {\n        console.error('Error fetching unread count:', error);\n      }\n    };\n\n    fetchUnreadCount();\n    const interval = setInterval(fetchUnreadCount, 30000); // Every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Fetch notifications when dropdown opens\n  useEffect(() => {\n    if (isOpen && notifications.length === 0) {\n      fetchNotifications();\n    }\n  }, [isOpen]);\n\n  const fetchNotifications = async (pageNum = 1, reset = false) => {\n    if (loading) return;\n    \n    setLoading(true);\n    try {\n      const response = await getUserNotifications({\n        page: pageNum,\n        limit: 10\n      });\n      \n      if (response.success) {\n        const newNotifications = response.data.notifications;\n        \n        if (reset || pageNum === 1) {\n          setNotifications(newNotifications);\n        } else {\n          setNotifications(prev => [...prev, ...newNotifications]);\n        }\n        \n        setHasMore(newNotifications.length === 10);\n        setPage(pageNum);\n        setUnreadCount(response.data.unreadCount);\n      }\n    } catch (error) {\n      console.error('Error fetching notifications:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleMarkAsRead = async (notificationId) => {\n    try {\n      const response = await markNotificationAsRead(notificationId);\n      if (response.success) {\n        setNotifications(prev => \n          prev.map(notif => \n            notif._id === notificationId \n              ? { ...notif, isRead: true }\n              : notif\n          )\n        );\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n\n  const handleMarkAllAsRead = async () => {\n    try {\n      const response = await markAllNotificationsAsRead();\n      if (response.success) {\n        setNotifications(prev => \n          prev.map(notif => ({ ...notif, isRead: true }))\n        );\n        setUnreadCount(0);\n      }\n    } catch (error) {\n      console.error('Error marking all as read:', error);\n    }\n  };\n\n  const getNotificationIcon = (type) => {\n    switch (type) {\n      case 'new_exam':\n        return '🎯';\n      case 'new_study_material':\n        return '📚';\n      case 'forum_question_posted':\n        return '❓';\n      case 'forum_answer_received':\n        return '💡';\n      case 'level_up':\n        return '🎉';\n      case 'achievement_unlocked':\n        return '🏆';\n      default:\n        return '📢';\n    }\n  };\n\n  const formatTimeAgo = (date) => {\n    const now = new Date();\n    const notifDate = new Date(date);\n    const diffInMinutes = Math.floor((now - notifDate) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    \n    return notifDate.toLocaleDateString();\n  };\n\n  return (\n    <div className={`relative ${className}`} ref={dropdownRef} style={{ zIndex: 10000 }}>\n      {/* Bell Icon */}\n      <button\n        ref={buttonRef}\n        onClick={(e) => {\n          e.preventDefault();\n          e.stopPropagation();\n          setIsOpen(!isOpen);\n        }}\n        className=\"notification-bell-button relative p-2 text-gray-700 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50\"\n        style={{ zIndex: 10001 }}\n      >\n        {unreadCount > 0 ? (\n          <TbBellRinging\n            className=\"w-5 h-5\"\n            style={{ color: '#ef4444' }}\n          />\n        ) : (\n          <TbBell\n            className=\"w-5 h-5\"\n            style={{ color: '#374151' }}\n          />\n        )}\n\n        {/* Unread count badge - positioned below the bell */}\n        {unreadCount > 0 && (\n          <span\n            className=\"notification-badge\"\n            style={{\n              position: 'absolute',\n              bottom: '-4px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              backgroundColor: '#ef4444',\n              color: '#ffffff',\n              fontSize: '10px',\n              fontWeight: '700',\n              minWidth: '20px',\n              width: '20px',\n              height: '20px',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              border: '2px solid #ffffff',\n              boxShadow: '0 2px 8px rgba(239, 68, 68, 0.6)',\n              zIndex: 1000\n            }}\n          >\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* Dropdown Portal */}\n      {isOpen && createPortal(\n        <div\n          ref={dropdownRef}\n          className=\"notification-dropdown w-96 bg-white rounded-2xl shadow-2xl border border-gray-200 max-h-[500px] overflow-hidden backdrop-blur-sm\"\n            style={{\n              position: 'fixed',\n              top: dropdownPosition.top,\n              right: dropdownPosition.right,\n              zIndex: 99999,\n              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'\n            }}\n            onClick={(e) => e.stopPropagation()}\n          >\n            {/* Header */}\n            <div className=\"p-4 border-b border-gray-100 flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50\">\n              <div className=\"flex items-center space-x-2\">\n                <TbBell className=\"w-5 h-5 text-blue-600\" />\n                <h3 className=\"font-bold text-gray-800\">Notifications</h3>\n                {unreadCount > 0 && (\n                  <span className=\"bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold\">\n                    {unreadCount}\n                  </span>\n                )}\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                {unreadCount > 0 && (\n                  <button\n                    onClick={handleMarkAllAsRead}\n                    className=\"text-sm text-blue-600 hover:text-blue-700 font-medium px-2 py-1 rounded hover:bg-blue-100 transition-colors\"\n                  >\n                    <TbCheck className=\"w-4 h-4 inline mr-1\" />\n                    Mark all read\n                  </button>\n                )}\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"text-gray-400 hover:text-gray-600 p-1 rounded hover:bg-gray-100 transition-colors\"\n                >\n                  <TbX className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Notifications List */}\n            <div className=\"max-h-80 overflow-y-auto\">\n              {loading && notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  <div className=\"animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2\"></div>\n                  Loading notifications...\n                </div>\n              ) : notifications.length === 0 ? (\n                <div className=\"p-8 text-center text-gray-500\">\n                  <TbBell className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                  <p>No notifications yet</p>\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <div\n                    key={notification._id}\n                    className={`notification-item group relative transition-all duration-200 ${\n                      !notification.isRead\n                        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500'\n                        : 'hover:bg-gray-50'\n                    }`}\n                  >\n                    <div\n                      className=\"p-4 cursor-pointer\"\n                      onClick={() => {\n                        if (!notification.isRead) {\n                          handleMarkAsRead(notification._id);\n                        }\n                        if (notification.actionUrl) {\n                          window.location.href = notification.actionUrl;\n                        }\n                      }}\n                    >\n                      <div className=\"flex items-start gap-3\">\n                        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg flex-shrink-0 ${\n                          !notification.isRead ? 'bg-blue-100' : 'bg-gray-100'\n                        }`}>\n                          {getNotificationIcon(notification.type)}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-start justify-between\">\n                            <div className=\"flex-1\">\n                              <p className={`font-semibold text-sm line-clamp-1 ${\n                                !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                              }`}>\n                                {notification.title}\n                              </p>\n                              <p className=\"text-gray-600 text-xs mt-1 line-clamp-2\">\n                                {notification.message}\n                              </p>\n                              <div className=\"flex items-center justify-between mt-2\">\n                                <p className=\"text-gray-400 text-xs\">\n                                  {formatTimeAgo(notification.createdAt)}\n                                </p>\n                                {!notification.isRead && (\n                                  <span className=\"bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-bold\">\n                                    New\n                                  </span>\n                                )}\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Individual close button */}\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleMarkAsRead(notification._id);\n                        // Remove from local state\n                        setNotifications(prev => prev.filter(n => n._id !== notification._id));\n                      }}\n                      className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-gray-200 text-gray-400 hover:text-gray-600\"\n                    >\n                      <TbX className=\"w-3 h-3\" />\n                    </button>\n                  </div>\n                ))\n              )}\n              \n              {/* Load More */}\n              {hasMore && notifications.length > 0 && (\n                <button\n                  onClick={() => fetchNotifications(page + 1)}\n                  disabled={loading}\n                  className=\"w-full p-3 text-center text-blue-600 hover:bg-gray-50 text-sm font-medium disabled:opacity-50\"\n                >\n                  {loading ? 'Loading...' : 'Load more'}\n                </button>\n              )}\n            </div>\n          </div>,\n        document.body\n      )}\n    </div>\n  );\n};\n\nexport default NotificationBell;\n\n// Add CSS animations to replace Framer Motion\nconst styles = `\n.notification-bell-button {\n  transition: transform 0.2s ease;\n}\n\n.notification-bell-button:hover {\n  transform: scale(1.05);\n}\n\n.notification-bell-button:active {\n  transform: scale(0.95);\n}\n\n.notification-badge {\n  animation: badgeAppear 0.3s ease-out;\n}\n\n.notification-dropdown {\n  animation: dropdownAppear 0.3s ease-out;\n}\n\n.notification-item {\n  animation: itemSlideIn 0.3s ease-out;\n}\n\n@keyframes badgeAppear {\n  from {\n    transform: scale(0);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n@keyframes dropdownAppear {\n  from {\n    opacity: 0;\n    transform: translateY(-10px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n@keyframes itemSlideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.textContent = styles;\n  document.head.appendChild(styleSheet);\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,QAAQ,WAAW;AACxC,SACEC,MAAM,EACNC,aAAa,EACbC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,OAAO,QACF,gBAAgB;AACvB,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,0BAA0B,QACrB,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC;IAAEiC,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC,CAAC;EAC9E,MAAMC,WAAW,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkC,SAAS,GAAGlC,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACAD,SAAS,CAAC,MAAM;IACd,MAAMoC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,IAClEL,SAAS,CAACG,OAAO,IAAI,CAACH,SAAS,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAClErB,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAEDsB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApC,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,IAAIiB,SAAS,CAACG,OAAO,EAAE;MAC/B,MAAMM,IAAI,GAAGT,SAAS,CAACG,OAAO,CAACO,qBAAqB,CAAC,CAAC;MACtDd,mBAAmB,CAAC;QAClBC,GAAG,EAAEY,IAAI,CAACE,MAAM,GAAG,CAAC;QACpBb,KAAK,EAAEc,MAAM,CAACC,UAAU,GAAGJ,IAAI,CAACX;MAClC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;;EAEZ;EACAlB,SAAS,CAAC,MAAM;IACd,MAAMiD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMxC,0BAA0B,CAAC,CAAC;QACnD,IAAIwC,QAAQ,CAACC,OAAO,EAAE;UACpB5B,cAAc,CAAC2B,QAAQ,CAACE,IAAI,CAAC9B,WAAW,CAAC;QAC3C;MACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDJ,gBAAgB,CAAC,CAAC;IAClB,MAAMM,QAAQ,GAAGC,WAAW,CAACP,gBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEvD,OAAO,MAAMQ,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvD,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,IAAIE,aAAa,CAACsC,MAAM,KAAK,CAAC,EAAE;MACxCC,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACzC,MAAM,CAAC,CAAC;EAEZ,MAAMyC,kBAAkB,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,EAAEC,KAAK,GAAG,KAAK,KAAK;IAC/D,IAAIrC,OAAO,EAAE;IAEbC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMyB,QAAQ,GAAG,MAAMzC,oBAAoB,CAAC;QAC1CiB,IAAI,EAAEkC,OAAO;QACbE,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,IAAIZ,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMY,gBAAgB,GAAGb,QAAQ,CAACE,IAAI,CAAChC,aAAa;QAEpD,IAAIyC,KAAK,IAAID,OAAO,KAAK,CAAC,EAAE;UAC1BvC,gBAAgB,CAAC0C,gBAAgB,CAAC;QACpC,CAAC,MAAM;UACL1C,gBAAgB,CAAC2C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,gBAAgB,CAAC,CAAC;QAC1D;QAEAlC,UAAU,CAACkC,gBAAgB,CAACL,MAAM,KAAK,EAAE,CAAC;QAC1C/B,OAAO,CAACiC,OAAO,CAAC;QAChBrC,cAAc,CAAC2B,QAAQ,CAACE,IAAI,CAAC9B,WAAW,CAAC;MAC3C;IACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwC,gBAAgB,GAAG,MAAOC,cAAc,IAAK;IACjD,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMvC,sBAAsB,CAACuD,cAAc,CAAC;MAC7D,IAAIhB,QAAQ,CAACC,OAAO,EAAE;QACpB9B,gBAAgB,CAAC2C,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACC,KAAK,IACZA,KAAK,CAACC,GAAG,KAAKH,cAAc,GACxB;UAAE,GAAGE,KAAK;UAAEE,MAAM,EAAE;QAAK,CAAC,GAC1BF,KACN,CACF,CAAC;QACD7C,cAAc,CAACyC,IAAI,IAAIO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,IAAI,GAAG,CAAC,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF,CAAC;EAED,MAAMoB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAMtC,0BAA0B,CAAC,CAAC;MACnD,IAAIsC,QAAQ,CAACC,OAAO,EAAE;QACpB9B,gBAAgB,CAAC2C,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACC,KAAK,KAAK;UAAE,GAAGA,KAAK;UAAEE,MAAM,EAAE;QAAK,CAAC,CAAC,CAChD,CAAC;QACD/C,cAAc,CAAC,CAAC,CAAC;MACnB;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMqB,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,oBAAoB;QACvB,OAAO,IAAI;MACb,KAAK,uBAAuB;QAC1B,OAAO,GAAG;MACZ,KAAK,uBAAuB;QAC1B,OAAO,IAAI;MACb,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,sBAAsB;QACzB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMC,aAAa,GAAIC,IAAI,IAAK;IAC9B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IAChC,MAAMI,aAAa,GAAGV,IAAI,CAACW,KAAK,CAAC,CAACJ,GAAG,GAAGE,SAAS,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjE,IAAIC,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;IACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAQ,GAAEA,aAAc,OAAM;IAEtD,MAAME,WAAW,GAAGZ,IAAI,CAACW,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIE,WAAW,GAAG,EAAE,EAAE,OAAQ,GAAEA,WAAY,OAAM;IAElD,MAAMC,UAAU,GAAGb,IAAI,CAACW,KAAK,CAACC,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAQ,GAAEA,UAAW,OAAM;IAE/C,OAAOJ,SAAS,CAACK,kBAAkB,CAAC,CAAC;EACvC,CAAC;EAED,oBACEvE,OAAA;IAAKE,SAAS,EAAG,YAAWA,SAAU,EAAE;IAACsE,GAAG,EAAEpD,WAAY;IAACqD,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAM,CAAE;IAAAC,QAAA,gBAElF3E,OAAA;MACEwE,GAAG,EAAEnD,SAAU;MACfuD,OAAO,EAAGC,CAAC,IAAK;QACdA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;QACnB1E,SAAS,CAAC,CAACD,MAAM,CAAC;MACpB,CAAE;MACFF,SAAS,EAAC,uHAAuH;MACjIuE,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAM,CAAE;MAAAC,QAAA,GAExBnE,WAAW,GAAG,CAAC,gBACdR,OAAA,CAACV,aAAa;QACZY,SAAS,EAAC,SAAS;QACnBuE,KAAK,EAAE;UAAEO,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,gBAEFpF,OAAA,CAACX,MAAM;QACLa,SAAS,EAAC,SAAS;QACnBuE,KAAK,EAAE;UAAEO,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF,EAGA5E,WAAW,GAAG,CAAC,iBACdR,OAAA;QACEE,SAAS,EAAC,oBAAoB;QAC9BuE,KAAK,EAAE;UACLY,QAAQ,EAAE,UAAU;UACpBrD,MAAM,EAAE,MAAM;UACdsD,IAAI,EAAE,KAAK;UACXC,SAAS,EAAE,kBAAkB;UAC7BC,eAAe,EAAE,SAAS;UAC1BR,KAAK,EAAE,SAAS;UAChBS,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,kCAAkC;UAC7CzB,MAAM,EAAE;QACV,CAAE;QAAAC,QAAA,EAEDnE,WAAW,GAAG,EAAE,GAAG,KAAK,GAAGA;MAAW;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,EAGRhF,MAAM,iBAAIhB,YAAY,eACrBY,OAAA;MACEwE,GAAG,EAAEpD,WAAY;MACjBlB,SAAS,EAAC,kIAAkI;MAC1IuE,KAAK,EAAE;QACLY,QAAQ,EAAE,OAAO;QACjBnE,GAAG,EAAEF,gBAAgB,CAACE,GAAG;QACzBC,KAAK,EAAEH,gBAAgB,CAACG,KAAK;QAC7BuD,MAAM,EAAE,KAAK;QACbyB,SAAS,EAAE;MACb,CAAE;MACFvB,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACE,eAAe,CAAC,CAAE;MAAAJ,QAAA,gBAGpC3E,OAAA;QAAKE,SAAS,EAAC,2GAA2G;QAAAyE,QAAA,gBACxH3E,OAAA;UAAKE,SAAS,EAAC,6BAA6B;UAAAyE,QAAA,gBAC1C3E,OAAA,CAACX,MAAM;YAACa,SAAS,EAAC;UAAuB;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CpF,OAAA;YAAIE,SAAS,EAAC,yBAAyB;YAAAyE,QAAA,EAAC;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACzD5E,WAAW,GAAG,CAAC,iBACdR,OAAA;YAAME,SAAS,EAAC,gEAAgE;YAAAyE,QAAA,EAC7EnE;UAAW;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNpF,OAAA;UAAKE,SAAS,EAAC,6BAA6B;UAAAyE,QAAA,GACzCnE,WAAW,GAAG,CAAC,iBACdR,OAAA;YACE4E,OAAO,EAAEjB,mBAAoB;YAC7BzD,SAAS,EAAC,6GAA6G;YAAAyE,QAAA,gBAEvH3E,OAAA,CAACT,OAAO;cAACW,SAAS,EAAC;YAAqB;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACDpF,OAAA;YACE4E,OAAO,EAAEA,CAAA,KAAMvE,SAAS,CAAC,KAAK,CAAE;YAChCH,SAAS,EAAC,mFAAmF;YAAAyE,QAAA,eAE7F3E,OAAA,CAACR,GAAG;cAACU,SAAS,EAAC;YAAS;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpF,OAAA;QAAKE,SAAS,EAAC,0BAA0B;QAAAyE,QAAA,GACtCjE,OAAO,IAAIJ,aAAa,CAACsC,MAAM,KAAK,CAAC,gBACpC5C,OAAA;UAAKE,SAAS,EAAC,+BAA+B;UAAAyE,QAAA,gBAC5C3E,OAAA;YAAKE,SAAS,EAAC;UAA8F;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4BAEtH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GACJ9E,aAAa,CAACsC,MAAM,KAAK,CAAC,gBAC5B5C,OAAA;UAAKE,SAAS,EAAC,+BAA+B;UAAAyE,QAAA,gBAC5C3E,OAAA,CAACX,MAAM;YAACa,SAAS,EAAC;UAAmC;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDpF,OAAA;YAAA2E,QAAA,EAAG;UAAoB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,GAEN9E,aAAa,CAAC+C,GAAG,CAAE+C,YAAY,iBAC7BpG,OAAA;UAEEE,SAAS,EAAG,gEACV,CAACkG,YAAY,CAAC5C,MAAM,GAChB,yEAAyE,GACzE,kBACL,EAAE;UAAAmB,QAAA,gBAEH3E,OAAA;YACEE,SAAS,EAAC,oBAAoB;YAC9B0E,OAAO,EAAEA,CAAA,KAAM;cACb,IAAI,CAACwB,YAAY,CAAC5C,MAAM,EAAE;gBACxBL,gBAAgB,CAACiD,YAAY,CAAC7C,GAAG,CAAC;cACpC;cACA,IAAI6C,YAAY,CAACC,SAAS,EAAE;gBAC1BpE,MAAM,CAACqE,QAAQ,CAACC,IAAI,GAAGH,YAAY,CAACC,SAAS;cAC/C;YACF,CAAE;YAAA1B,QAAA,eAEF3E,OAAA;cAAKE,SAAS,EAAC,wBAAwB;cAAAyE,QAAA,gBACrC3E,OAAA;gBAAKE,SAAS,EAAG,iFACf,CAACkG,YAAY,CAAC5C,MAAM,GAAG,aAAa,GAAG,aACxC,EAAE;gBAAAmB,QAAA,EACAf,mBAAmB,CAACwC,YAAY,CAACvC,IAAI;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACNpF,OAAA;gBAAKE,SAAS,EAAC,gBAAgB;gBAAAyE,QAAA,eAC7B3E,OAAA;kBAAKE,SAAS,EAAC,kCAAkC;kBAAAyE,QAAA,eAC/C3E,OAAA;oBAAKE,SAAS,EAAC,QAAQ;oBAAAyE,QAAA,gBACrB3E,OAAA;sBAAGE,SAAS,EAAG,sCACb,CAACkG,YAAY,CAAC5C,MAAM,GAAG,eAAe,GAAG,eAC1C,EAAE;sBAAAmB,QAAA,EACAyB,YAAY,CAACI;oBAAK;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACJpF,OAAA;sBAAGE,SAAS,EAAC,yCAAyC;sBAAAyE,QAAA,EACnDyB,YAAY,CAACK;oBAAO;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACJpF,OAAA;sBAAKE,SAAS,EAAC,wCAAwC;sBAAAyE,QAAA,gBACrD3E,OAAA;wBAAGE,SAAS,EAAC,uBAAuB;wBAAAyE,QAAA,EACjCb,aAAa,CAACsC,YAAY,CAACM,SAAS;sBAAC;wBAAAzB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC,EACH,CAACgB,YAAY,CAAC5C,MAAM,iBACnBxD,OAAA;wBAAME,SAAS,EAAC,iEAAiE;wBAAAyE,QAAA,EAAC;sBAElF;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpF,OAAA;YACE4E,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACE,eAAe,CAAC,CAAC;cACnB5B,gBAAgB,CAACiD,YAAY,CAAC7C,GAAG,CAAC;cAClC;cACAhD,gBAAgB,CAAC2C,IAAI,IAAIA,IAAI,CAACyD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrD,GAAG,KAAK6C,YAAY,CAAC7C,GAAG,CAAC,CAAC;YACxE,CAAE;YACFrD,SAAS,EAAC,6IAA6I;YAAAyE,QAAA,eAEvJ3E,OAAA,CAACR,GAAG;cAACU,SAAS,EAAC;YAAS;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA,GA9DJgB,YAAY,CAAC7C,GAAG;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+DlB,CACN,CACF,EAGAtE,OAAO,IAAIR,aAAa,CAACsC,MAAM,GAAG,CAAC,iBAClC5C,OAAA;UACE4E,OAAO,EAAEA,CAAA,KAAM/B,kBAAkB,CAACjC,IAAI,GAAG,CAAC,CAAE;UAC5CiG,QAAQ,EAAEnG,OAAQ;UAClBR,SAAS,EAAC,+FAA+F;UAAAyE,QAAA,EAExGjE,OAAO,GAAG,YAAY,GAAG;QAAW;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACRzD,QAAQ,CAACmF,IACX,CAAC;EAAA;IAAA7B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjF,EAAA,CApWIF,gBAAgB;AAAA8G,EAAA,GAAhB9G,gBAAgB;AAsWtB,eAAeA,gBAAgB;;AAE/B;AACA,MAAM+G,MAAM,GAAI;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOrF,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMsF,UAAU,GAAGtF,QAAQ,CAACuF,aAAa,CAAC,OAAO,CAAC;EAClDD,UAAU,CAACE,WAAW,GAAGH,MAAM;EAC/BrF,QAAQ,CAACyF,IAAI,CAACC,WAAW,CAACJ,UAAU,CAAC;AACvC;AAAC,IAAAF,EAAA;AAAAO,YAAA,CAAAP,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}