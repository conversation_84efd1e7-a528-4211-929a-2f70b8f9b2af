{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo, useRef } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { getVideoComments, addVideoComment, addCommentReply, likeComment, deleteVideoComment } from \"../../../apicalls/videoComments\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { message } from \"antd\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { MdVerified } from 'react-icons/md';\nimport VideoGrid from './VideoGrid';\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst IconComponents = {\n  FaPlayCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\u25B6\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 23\n  }, this),\n  FaGraduationCap: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83C\\uDF93\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 26\n  }, this),\n  FaTimes: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 18\n  }, this),\n  FaExpand: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 19\n  }, this),\n  FaCompress: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 21\n  }, this),\n  TbVideo: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83D\\uDCF9\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 18\n  }, this),\n  TbInfoCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u2139\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 23\n  }, this),\n  TbAlertTriangle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u26A0\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 26\n  }, this),\n  TbFilter: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 19\n  }, this),\n  TbSortAscending: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2191\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 26\n  }, this),\n  TbSearch: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 19\n  }, this),\n  TbX: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 14\n  }, this),\n  TbDownload: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u21BB\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 21\n  }, this)\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\nfunction VideoLessons() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili,\n    getClassName,\n    getSubjectName\n  } = useLanguage();\n  const dispatch = useDispatch();\n\n  // Inline CSS fixes for mobile issues\n  const inlineStyles = `\n    /* Mobile Layout Fixes */\n    @media (max-width: 768px) {\n      /* Reduce Bell Icon Size */\n      .notification-bell-button .w-5,\n      .notification-bell-button .h-5 {\n        width: 14px !important;\n        height: 14px !important;\n      }\n\n      /* All header and sidebar styles removed - using ProtectedRoute only */\n      .video-lessons-container {\n        padding-top: 16px !important;\n      }\n    }\n\n    /* Center Quiz Marking Modal */\n    .ant-modal,\n    .quiz-modal,\n    .marking-modal,\n    .result-modal,\n    .quiz-result-modal {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      top: 0 !important;\n      padding-top: 0 !important;\n    }\n\n    .ant-modal-content,\n    .quiz-modal-content,\n    .marking-modal-content,\n    .result-modal-content {\n      margin: 0 auto !important;\n      position: relative !important;\n      top: auto !important;\n      transform: none !important;\n    }\n\n    .ant-modal-wrap {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      min-height: 100vh !important;\n    }\n\n    /* Specific Quiz Result Modal Centering */\n    .quiz-result-overlay,\n    .quiz-marking-overlay {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      position: fixed !important;\n      top: 0 !important;\n      left: 0 !important;\n      width: 100vw !important;\n      height: 100vh !important;\n      z-index: 10000 !important;\n      padding: 20px !important;\n      box-sizing: border-box !important;\n    }\n  `;\n\n  // Add styles to document head\n  React.useEffect(() => {\n    const styleElement = document.createElement('style');\n    styleElement.textContent = inlineStyles;\n    document.head.appendChild(styleElement);\n    return () => {\n      document.head.removeChild(styleElement);\n    };\n  }, []);\n\n  // State management with localStorage persistence\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState((user === null || user === void 0 ? void 0 : user.level) || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(() => {\n    // Restore from localStorage or use user's class as default\n    return localStorage.getItem('video-lessons-selected-class') || (user === null || user === void 0 ? void 0 : user.class) || \"all\";\n  });\n  const [selectedSubject, setSelectedSubject] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-selected-subject') || \"all\";\n  });\n  const [searchTerm, setSearchTerm] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-search-term') || \"\";\n  });\n  const [sortBy, setSortBy] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-sort-by') || \"newest\";\n  });\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [videosPerPage, setVideosPerPage] = useState(12);\n  const [totalVideos, setTotalVideos] = useState(0);\n\n  // Comments state - store comments per video\n  const [videoComments, setVideoComments] = useState({});\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [editingComment, setEditingComment] = useState(null);\n  const [editCommentText, setEditCommentText] = useState(\"\");\n\n  // Get comments for current video\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const currentVideo = paginatedVideos[currentVideoIndex];\n    if (!currentVideo) return [];\n\n    // Try both id and _id fields\n    const videoId = currentVideo.id || currentVideo._id;\n    return videoComments[videoId] || [];\n  };\n\n  // Set comments for current video\n  const setCurrentVideoComments = comments => {\n    if (currentVideoIndex === null) return;\n    const currentVideo = paginatedVideos[currentVideoIndex];\n    if (!currentVideo) return;\n\n    // Use the same videoId logic as getCurrentVideoComments\n    const videoId = currentVideo.id || currentVideo._id;\n    setVideoComments(prev => ({\n      ...prev,\n      [videoId]: comments\n    }));\n  };\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n      const filters = {\n        level: selectedLevel,\n        className: \"all\",\n        // Get all classes for the level\n        subject: \"all\",\n        // Get all subjects for the level\n        content: \"videos\"\n      };\n      const response = await getStudyMaterial(filters);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        const videoData = response.data.data || [];\n        setVideos(videoData);\n\n        // Load comments for all videos\n        await loadAllVideoComments(videoData);\n      } else {\n        var _response$data2;\n        setError((response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video => {\n        var _video$title, _video$subject, _video$topic;\n        return ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchLower)) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchLower)) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    // Update total count\n    setTotalVideos(sorted.length);\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Paginated videos\n  const paginatedVideos = useMemo(() => {\n    const startIndex = (currentPage - 1) * videosPerPage;\n    const endIndex = startIndex + videosPerPage;\n    return filteredAndSortedVideos.slice(startIndex, endIndex);\n  }, [filteredAndSortedVideos, currentPage, videosPerPage]);\n\n  // Pagination calculations\n  const totalPages = Math.ceil(totalVideos / videosPerPage);\n  const startItem = (currentPage - 1) * videosPerPage + 1;\n  const endItem = Math.min(currentPage * videosPerPage, totalVideos);\n\n  // Pagination handlers\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    setCurrentVideoIndex(null); // Close any open video when changing pages\n  };\n\n  const handlePageSizeChange = newSize => {\n    setVideosPerPage(newSize);\n    setCurrentPage(1); // Reset to first page when changing page size\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = paginatedVideos[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Load comments for this video if not already loaded\n    const videoId = (video === null || video === void 0 ? void 0 : video.id) || (video === null || video === void 0 ? void 0 : video._id);\n    if (videoId && !videoComments[videoId]) {\n      loadVideoComments(videoId);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = video => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Handle filter changes with localStorage persistence\n  const handleClassChange = value => {\n    setSelectedClass(value);\n    localStorage.setItem('video-lessons-selected-class', value);\n  };\n  const handleSubjectChange = value => {\n    setSelectedSubject(value);\n    localStorage.setItem('video-lessons-selected-subject', value);\n  };\n  const handleSearchChange = value => {\n    setSearchTerm(value);\n    localStorage.setItem('video-lessons-search-term', value);\n  };\n  const handleSortChange = value => {\n    setSortBy(value);\n    localStorage.setItem('video-lessons-sort-by', value);\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      setSelectedLevel(user.level);\n    }\n    // Only set user's class as default if no saved preference exists\n    if (user !== null && user !== void 0 && user.class && !localStorage.getItem('video-lessons-selected-class')) {\n      handleClassChange(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    handleSearchChange(\"\");\n  };\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n  const handleClearAll = () => {\n    handleSearchChange(\"\");\n    handleSubjectChange(\"all\");\n    handleClassChange(\"all\");\n    handleSortChange(\"newest\");\n    fetchVideos();\n  };\n\n  // Load comments for all videos\n  const loadAllVideoComments = async videoList => {\n    try {\n      console.log('📹 Loading comments for all videos:', videoList.length);\n      const commentsMap = {};\n\n      // Load comments for each video\n      for (const video of videoList) {\n        const videoId = video.id || video._id;\n        if (videoId) {\n          try {\n            const response = await getVideoComments(videoId);\n            if (response.success) {\n              commentsMap[videoId] = response.data.comments;\n              console.log(`📝 Loaded ${response.data.comments.length} comments for video ${videoId}`);\n            }\n          } catch (error) {\n            console.error(`Error loading comments for video ${videoId}:`, error);\n          }\n        }\n      }\n      setVideoComments(commentsMap);\n      console.log('✅ All video comments loaded:', commentsMap);\n    } catch (error) {\n      console.error(\"Error loading all video comments:\", error);\n    }\n  };\n\n  // Load comments for current video\n  const loadVideoComments = async videoId => {\n    try {\n      const response = await getVideoComments(videoId);\n      if (response.success) {\n        setVideoComments(prev => ({\n          ...prev,\n          [videoId]: response.data.comments\n        }));\n      }\n    } catch (error) {\n      console.error(\"Error loading comments:\", error);\n    }\n  };\n\n  // Comment functions\n  const handleAddComment = async () => {\n    if (newComment.trim()) {\n      const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n      if (!currentVideo) return;\n      try {\n        console.log('📹 Current video object:', currentVideo);\n        console.log('📹 Video keys:', Object.keys(currentVideo || {}));\n        console.log('📹 Video id field:', currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo.id);\n        console.log('📹 Video _id field:', currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo._id);\n\n        // Use _id if id doesn't exist\n        const videoId = currentVideo.id || currentVideo._id;\n        const commentData = {\n          videoId: videoId,\n          text: newComment.trim()\n        };\n        console.log('📝 Sending video comment:', commentData);\n        console.log('📝 Comment data keys:', Object.keys(commentData));\n        console.log('📝 videoId value:', videoId, '(type:', typeof videoId, ')');\n        console.log('📝 text value:', newComment.trim(), '(type:', typeof newComment.trim(), ')');\n        const response = await addVideoComment(commentData);\n        if (response.success) {\n          // Add comment to local state immediately for better UX\n          const comment = {\n            _id: response.data._id,\n            text: response.data.text,\n            author: response.data.author,\n            avatar: response.data.avatar,\n            createdAt: response.data.createdAt,\n            replies: [],\n            likes: 0,\n            likedBy: []\n          };\n          const currentComments = getCurrentVideoComments();\n          setCurrentVideoComments([comment, ...currentComments]);\n          setNewComment(\"\");\n          message.success(\"Comment added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add comment\");\n        }\n      } catch (error) {\n        console.error(\"Error adding comment:\", error);\n        message.error(\"Failed to add comment\");\n      }\n    }\n  };\n  const handleAddReply = async commentId => {\n    if (replyText.trim()) {\n      try {\n        const response = await addCommentReply(commentId, {\n          text: replyText.trim()\n        });\n        if (response.success) {\n          // Update local state with the new reply\n          const currentComments = getCurrentVideoComments();\n          const updatedComments = currentComments.map(comment => comment._id === commentId || comment.id === commentId ? {\n            ...comment,\n            replies: response.data.replies\n          } : comment);\n          setCurrentVideoComments(updatedComments);\n          setReplyText(\"\");\n          setReplyingTo(null);\n          message.success(\"Reply added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add reply\");\n        }\n      } catch (error) {\n        console.error(\"Error adding reply:\", error);\n        message.error(\"Failed to add reply\");\n      }\n    }\n  };\n  const handleLikeComment = async (commentId, isReply = false, replyId = null) => {\n    try {\n      const response = await likeComment(commentId, {\n        isReply,\n        replyId\n      });\n      if (response.success) {\n        // Update local state with the updated comment\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.map(comment => comment._id === commentId || comment.id === commentId ? response.data : comment);\n        setCurrentVideoComments(updatedComments);\n      } else {\n        message.error(response.message || \"Failed to update like\");\n      }\n    } catch (error) {\n      console.error(\"Error updating like:\", error);\n      message.error(\"Failed to update like\");\n    }\n  };\n  const handleDeleteComment = async commentId => {\n    try {\n      const response = await deleteVideoComment(commentId);\n      if (response.success) {\n        // Remove comment from local state\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.filter(comment => comment._id !== commentId && comment.id !== commentId);\n        setCurrentVideoComments(updatedComments);\n        message.success(\"Comment deleted successfully!\");\n      } else {\n        message.error(response.message || \"Failed to delete comment\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting comment:\", error);\n      message.error(\"Failed to delete comment\");\n    }\n  };\n  const handleEditComment = comment => {\n    setEditingComment(comment._id || comment.id);\n    setEditCommentText(comment.text);\n  };\n  const handleSaveEditComment = async () => {\n    if (!editCommentText.trim()) {\n      message.error(\"Comment cannot be empty\");\n      return;\n    }\n    try {\n      // TODO: Add API call to update comment\n      // const response = await updateVideoComment(editingComment, { text: editCommentText.trim() });\n\n      // For now, update local state\n      const currentComments = getCurrentVideoComments();\n      const updatedComments = currentComments.map(comment => {\n        if ((comment._id || comment.id) === editingComment) {\n          return {\n            ...comment,\n            text: editCommentText.trim()\n          };\n        }\n        return comment;\n      });\n      setCurrentVideoComments(updatedComments);\n      setEditingComment(null);\n      setEditCommentText(\"\");\n      message.success(\"Comment updated successfully!\");\n    } catch (error) {\n      console.error(\"Error updating comment:\", error);\n      message.error(\"Failed to update comment\");\n    }\n  };\n  const handleCancelEdit = () => {\n    setEditingComment(null);\n    setEditCommentText(\"\");\n  };\n  const formatTimeAgo = timestamp => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n\n  // Render loading state\n  const renderLoadingState = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 709,\n    columnNumber: 5\n  }, this);\n\n  // Render error state\n  const renderErrorState = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-state\",\n    children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n      className: \"error-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 718,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 719,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 720,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: fetchVideos,\n      className: \"retry-btn\",\n      children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 717,\n    columnNumber: 5\n  }, this);\n\n  // Render empty state\n  const renderEmptyState = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n      className: \"empty-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 730,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"suggestion\",\n      children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 733,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 729,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 17\n              }, this), isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedClass,\n              onChange: e => handleClassChange(e.target.value),\n              className: \"control-select class-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 17\n              }, this), availableClasses.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cls,\n                children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${cls}` : `Class ${cls}` : `Form ${cls}`\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 17\n              }, this), \"Subject\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 769,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => handleSubjectChange(e.target.value),\n              className: \"control-select subject-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this), availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSortAscending, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this), \"Sort\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => handleSortChange(e.target.value),\n              className: \"control-select sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"oldest\",\n                children: \"Oldest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Title A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subject\",\n                children: \"Subject A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos by title, subject, or topic...\",\n              value: searchTerm,\n              onChange: e => handleSearchChange(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 15\n            }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearSearch,\n              className: \"clear-search-btn\",\n              children: [/*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 19\n              }, this), \"Clear Search\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: \"refresh-btn\",\n            children: [/*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 15\n            }, this), \"Refresh All\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 807,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 743,\n        columnNumber: 9\n      }, this), loading && renderLoadingState(), !loading && error && renderErrorState(), !loading && !error && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [totalVideos > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-container pagination-top\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination-info\",\n            children: [\"Showing \", startItem, \"-\", endItem, \" of \", totalVideos, \" videos\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pagination-btn\",\n              onClick: () => handlePageChange(currentPage - 1),\n              disabled: currentPage === 1,\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 19\n            }, this), Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `pagination-btn ${currentPage === pageNum ? 'active' : ''}`,\n                onClick: () => handlePageChange(pageNum),\n                children: pageNum\n              }, pageNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 23\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pagination-btn\",\n              onClick: () => handlePageChange(currentPage + 1),\n              disabled: currentPage === totalPages,\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-size-selector\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Videos per page:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: videosPerPage,\n              onChange: e => handlePageSizeChange(Number(e.target.value)),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: 6,\n                children: \"6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 12,\n                children: \"12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 24,\n                children: \"24\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 48,\n                children: \"48\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 885,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(VideoGrid, {\n          paginatedVideos: paginatedVideos,\n          currentVideoIndex: currentVideoIndex,\n          handleShowVideo: handleShowVideo,\n          getThumbnailUrl: getThumbnailUrl,\n          getSubjectName: getSubjectName,\n          selectedLevel: selectedLevel,\n          isKiswahili: isKiswahili,\n          setVideoRef: setVideoRef,\n          setVideoError: setVideoError,\n          videoError: videoError,\n          setCurrentVideoIndex: setCurrentVideoIndex,\n          commentsExpanded: commentsExpanded,\n          setCommentsExpanded: setCommentsExpanded,\n          getCurrentVideoComments: getCurrentVideoComments,\n          newComment: newComment,\n          setNewComment: setNewComment,\n          handleAddComment: handleAddComment,\n          handleLikeComment: handleLikeComment,\n          handleDeleteComment: handleDeleteComment,\n          formatTimeAgo: formatTimeAgo,\n          user: user\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 900,\n          columnNumber: 13\n        }, this), totalVideos > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination-info\",\n            children: [\"Showing \", startItem, \"-\", endItem, \" of \", totalVideos, \" videos\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pagination-btn\",\n              onClick: () => handlePageChange(currentPage - 1),\n              disabled: currentPage === 1,\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 11\n            }, this), Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `pagination-btn ${currentPage === pageNum ? 'active' : ''}`,\n                onClick: () => handlePageChange(pageNum),\n                children: pageNum\n              }, pageNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 15\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pagination-btn\",\n              onClick: () => handlePageChange(currentPage + 1),\n              disabled: currentPage === totalPages,\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-size-selector\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Videos per page:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: videosPerPage,\n              onChange: e => handlePageSizeChange(Number(e.target.value)),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: 6,\n                children: \"6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 12,\n                children: \"12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 24,\n                children: \"24\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 48,\n                children: \"48\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 974,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 928,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 741,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 738,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoLessons, \"Zk+0bppZbcO0jP44IY3zOF3eYB8=\", false, function () {\n  return [useSelector, useLanguage, useDispatch];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "useRef", "motion", "AnimatePresence", "getStudyMaterial", "getVideoComments", "addVideoComment", "addCommentReply", "likeComment", "deleteVideoComment", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "message", "primarySubjects", "primaryKiswahiliSubjects", "secondarySubjects", "advanceSubjects", "useLanguage", "MdVerified", "VideoGrid", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "IconComponents", "FaPlayCircle", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FaGraduationCap", "FaTimes", "FaExpand", "FaCompress", "TbVideo", "TbInfoCircle", "TbAlertTriangle", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbSearch", "TbX", "TbDownload", "VideoLessons", "_s", "user", "state", "t", "isKiswahili", "getClassName", "getSubjectName", "dispatch", "inlineStyles", "styleElement", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "videos", "setVideos", "loading", "setLoading", "error", "setError", "selectedLevel", "setSelectedLevel", "level", "selectedClass", "setSelectedClass", "localStorage", "getItem", "class", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "currentPage", "setCurrentPage", "videosPerPage", "setVideosPerPage", "totalVideos", "setTotalVideos", "videoComments", "setVideoComments", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "replyText", "setReplyText", "showComments", "setShowComments", "commentsExpanded", "setCommentsExpanded", "editingComment", "setEditingComment", "editCommentText", "setEditCommentText", "getCurrentVideoComments", "currentVideo", "paginatedVideos", "videoId", "id", "_id", "setCurrentVideoComments", "comments", "prev", "availableClasses", "availableSubjects", "fetchVideos", "_response$data", "filters", "className", "subject", "content", "response", "data", "success", "videoData", "loadAllVideoComments", "_response$data2", "console", "filteredAndSortedVideos", "filtered", "filter", "video", "videoClass", "trim", "searchLower", "toLowerCase", "_video$title", "_video$subject", "_video$topic", "title", "includes", "topic", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "length", "log", "startIndex", "endIndex", "slice", "totalPages", "Math", "ceil", "startItem", "endItem", "min", "handlePageChange", "page", "handlePageSizeChange", "newSize", "handleShowVideo", "index", "loadVideoComments", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "pause", "toggleVideoExpansion", "fetch", "encodeURIComponent", "method", "headers", "ok", "Error", "status", "json", "getThumbnailUrl", "thumbnail", "videoID", "match", "handleClassChange", "value", "setItem", "handleSubjectChange", "handleSearchChange", "handleSortChange", "handleClearSearch", "handleRefresh", "handleClearAll", "videoList", "commentsMap", "handleAddComment", "Object", "keys", "commentData", "text", "comment", "author", "avatar", "replies", "likes", "<PERSON><PERSON><PERSON>", "currentComments", "handleAddReply", "commentId", "updatedComments", "map", "handleLikeComment", "isReply", "replyId", "handleDeleteComment", "handleEditComment", "handleSaveEditComment", "handleCancelEdit", "formatTimeAgo", "timestamp", "now", "time", "diffInSeconds", "floor", "toLocaleDateString", "renderLoadingState", "renderErrorState", "onClick", "renderEmptyState", "onChange", "e", "target", "cls", "type", "placeholder", "disabled", "Array", "from", "_", "i", "pageNum", "Number", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo, useRef } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { getVideoComments, addVideoComment, addCommentReply, likeComment, deleteVideoComment } from \"../../../apicalls/videoComments\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { message } from \"antd\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { MdVerified } from 'react-icons/md';\nimport VideoGrid from './VideoGrid';\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nconst IconComponents = {\n  FaPlayCircle: () => <span style={{fontSize: '24px'}}>▶️</span>,\n  FaGraduationCap: () => <span style={{fontSize: '24px'}}>🎓</span>,\n  FaTimes: () => <span style={{fontSize: '18px'}}>✕</span>,\n  FaExpand: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  FaCompress: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  TbVideo: () => <span style={{fontSize: '24px'}}>📹</span>,\n  TbInfoCircle: () => <span style={{fontSize: '16px'}}>ℹ️</span>,\n  TbAlertTriangle: () => <span style={{fontSize: '16px'}}>⚠️</span>,\n  TbFilter: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbSortAscending: () => <span style={{fontSize: '18px'}}>↑</span>,\n  TbSearch: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbX: () => <span style={{fontSize: '16px'}}>✕</span>,\n  TbDownload: () => <span style={{fontSize: '18px'}}>↻</span>\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili, getClassName, getSubjectName } = useLanguage();\n  const dispatch = useDispatch();\n\n  // Inline CSS fixes for mobile issues\n  const inlineStyles = `\n    /* Mobile Layout Fixes */\n    @media (max-width: 768px) {\n      /* Reduce Bell Icon Size */\n      .notification-bell-button .w-5,\n      .notification-bell-button .h-5 {\n        width: 14px !important;\n        height: 14px !important;\n      }\n\n      /* All header and sidebar styles removed - using ProtectedRoute only */\n      .video-lessons-container {\n        padding-top: 16px !important;\n      }\n    }\n\n    /* Center Quiz Marking Modal */\n    .ant-modal,\n    .quiz-modal,\n    .marking-modal,\n    .result-modal,\n    .quiz-result-modal {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      top: 0 !important;\n      padding-top: 0 !important;\n    }\n\n    .ant-modal-content,\n    .quiz-modal-content,\n    .marking-modal-content,\n    .result-modal-content {\n      margin: 0 auto !important;\n      position: relative !important;\n      top: auto !important;\n      transform: none !important;\n    }\n\n    .ant-modal-wrap {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      min-height: 100vh !important;\n    }\n\n    /* Specific Quiz Result Modal Centering */\n    .quiz-result-overlay,\n    .quiz-marking-overlay {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      position: fixed !important;\n      top: 0 !important;\n      left: 0 !important;\n      width: 100vw !important;\n      height: 100vh !important;\n      z-index: 10000 !important;\n      padding: 20px !important;\n      box-sizing: border-box !important;\n    }\n  `;\n\n  // Add styles to document head\n  React.useEffect(() => {\n    const styleElement = document.createElement('style');\n    styleElement.textContent = inlineStyles;\n    document.head.appendChild(styleElement);\n\n    return () => {\n      document.head.removeChild(styleElement);\n    };\n  }, []);\n\n  // State management with localStorage persistence\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(() => {\n    // Restore from localStorage or use user's class as default\n    return localStorage.getItem('video-lessons-selected-class') || user?.class || \"all\";\n  });\n  const [selectedSubject, setSelectedSubject] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-selected-subject') || \"all\";\n  });\n  const [searchTerm, setSearchTerm] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-search-term') || \"\";\n  });\n  const [sortBy, setSortBy] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-sort-by') || \"newest\";\n  });\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [videosPerPage, setVideosPerPage] = useState(12);\n  const [totalVideos, setTotalVideos] = useState(0);\n\n  // Comments state - store comments per video\n  const [videoComments, setVideoComments] = useState({});\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [editingComment, setEditingComment] = useState(null);\n  const [editCommentText, setEditCommentText] = useState(\"\");\n\n  // Get comments for current video\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const currentVideo = paginatedVideos[currentVideoIndex];\n    if (!currentVideo) return [];\n\n    // Try both id and _id fields\n    const videoId = currentVideo.id || currentVideo._id;\n    return videoComments[videoId] || [];\n  };\n\n  // Set comments for current video\n  const setCurrentVideoComments = (comments) => {\n    if (currentVideoIndex === null) return;\n    const currentVideo = paginatedVideos[currentVideoIndex];\n    if (!currentVideo) return;\n\n    // Use the same videoId logic as getCurrentVideoComments\n    const videoId = currentVideo.id || currentVideo._id;\n    setVideoComments(prev => ({\n      ...prev,\n      [videoId]: comments\n    }));\n  };\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: \"all\", // Get all classes for the level\n        subject: \"all\", // Get all subjects for the level\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response?.data?.success) {\n        const videoData = response.data.data || [];\n        setVideos(videoData);\n\n        // Load comments for all videos\n        await loadAllVideoComments(videoData);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n\n\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower) ||\n        video.topic?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    // Update total count\n    setTotalVideos(sorted.length);\n\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Paginated videos\n  const paginatedVideos = useMemo(() => {\n    const startIndex = (currentPage - 1) * videosPerPage;\n    const endIndex = startIndex + videosPerPage;\n    return filteredAndSortedVideos.slice(startIndex, endIndex);\n  }, [filteredAndSortedVideos, currentPage, videosPerPage]);\n\n  // Pagination calculations\n  const totalPages = Math.ceil(totalVideos / videosPerPage);\n  const startItem = (currentPage - 1) * videosPerPage + 1;\n  const endItem = Math.min(currentPage * videosPerPage, totalVideos);\n\n  // Pagination handlers\n  const handlePageChange = (page) => {\n    setCurrentPage(page);\n    setCurrentVideoIndex(null); // Close any open video when changing pages\n  };\n\n  const handlePageSizeChange = (newSize) => {\n    setVideosPerPage(newSize);\n    setCurrentPage(1); // Reset to first page when changing page size\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = paginatedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Load comments for this video if not already loaded\n    const videoId = video?.id || video?._id;\n    if (videoId && !videoComments[videoId]) {\n      loadVideoComments(videoId);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Handle filter changes with localStorage persistence\n  const handleClassChange = (value) => {\n    setSelectedClass(value);\n    localStorage.setItem('video-lessons-selected-class', value);\n  };\n\n  const handleSubjectChange = (value) => {\n    setSelectedSubject(value);\n    localStorage.setItem('video-lessons-selected-subject', value);\n  };\n\n  const handleSearchChange = (value) => {\n    setSearchTerm(value);\n    localStorage.setItem('video-lessons-search-term', value);\n  };\n\n  const handleSortChange = (value) => {\n    setSortBy(value);\n    localStorage.setItem('video-lessons-sort-by', value);\n  };\n\n  useEffect(() => {\n    if (user?.level) {\n      setSelectedLevel(user.level);\n    }\n    // Only set user's class as default if no saved preference exists\n    if (user?.class && !localStorage.getItem('video-lessons-selected-class')) {\n      handleClassChange(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    handleSearchChange(\"\");\n  };\n\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n\n  const handleClearAll = () => {\n    handleSearchChange(\"\");\n    handleSubjectChange(\"all\");\n    handleClassChange(\"all\");\n    handleSortChange(\"newest\");\n    fetchVideos();\n  };\n\n  // Load comments for all videos\n  const loadAllVideoComments = async (videoList) => {\n    try {\n      console.log('📹 Loading comments for all videos:', videoList.length);\n      const commentsMap = {};\n\n      // Load comments for each video\n      for (const video of videoList) {\n        const videoId = video.id || video._id;\n        if (videoId) {\n          try {\n            const response = await getVideoComments(videoId);\n            if (response.success) {\n              commentsMap[videoId] = response.data.comments;\n              console.log(`📝 Loaded ${response.data.comments.length} comments for video ${videoId}`);\n            }\n          } catch (error) {\n            console.error(`Error loading comments for video ${videoId}:`, error);\n          }\n        }\n      }\n\n      setVideoComments(commentsMap);\n      console.log('✅ All video comments loaded:', commentsMap);\n    } catch (error) {\n      console.error(\"Error loading all video comments:\", error);\n    }\n  };\n\n  // Load comments for current video\n  const loadVideoComments = async (videoId) => {\n    try {\n      const response = await getVideoComments(videoId);\n      if (response.success) {\n        setVideoComments(prev => ({\n          ...prev,\n          [videoId]: response.data.comments\n        }));\n      }\n    } catch (error) {\n      console.error(\"Error loading comments:\", error);\n    }\n  };\n\n  // Comment functions\n  const handleAddComment = async () => {\n    if (newComment.trim()) {\n      const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n      if (!currentVideo) return;\n\n      try {\n        console.log('📹 Current video object:', currentVideo);\n        console.log('📹 Video keys:', Object.keys(currentVideo || {}));\n        console.log('📹 Video id field:', currentVideo?.id);\n        console.log('📹 Video _id field:', currentVideo?._id);\n\n        // Use _id if id doesn't exist\n        const videoId = currentVideo.id || currentVideo._id;\n\n        const commentData = {\n          videoId: videoId,\n          text: newComment.trim()\n        };\n\n        console.log('📝 Sending video comment:', commentData);\n        console.log('📝 Comment data keys:', Object.keys(commentData));\n        console.log('📝 videoId value:', videoId, '(type:', typeof videoId, ')');\n        console.log('📝 text value:', newComment.trim(), '(type:', typeof newComment.trim(), ')');\n\n        const response = await addVideoComment(commentData);\n\n        if (response.success) {\n          // Add comment to local state immediately for better UX\n          const comment = {\n            _id: response.data._id,\n            text: response.data.text,\n            author: response.data.author,\n            avatar: response.data.avatar,\n            createdAt: response.data.createdAt,\n            replies: [],\n            likes: 0,\n            likedBy: []\n          };\n          const currentComments = getCurrentVideoComments();\n          setCurrentVideoComments([comment, ...currentComments]);\n          setNewComment(\"\");\n          message.success(\"Comment added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add comment\");\n        }\n      } catch (error) {\n        console.error(\"Error adding comment:\", error);\n        message.error(\"Failed to add comment\");\n      }\n    }\n  };\n\n\n\n  const handleAddReply = async (commentId) => {\n    if (replyText.trim()) {\n      try {\n        const response = await addCommentReply(commentId, {\n          text: replyText.trim()\n        });\n\n        if (response.success) {\n          // Update local state with the new reply\n          const currentComments = getCurrentVideoComments();\n          const updatedComments = currentComments.map(comment =>\n            comment._id === commentId || comment.id === commentId\n              ? { ...comment, replies: response.data.replies }\n              : comment\n          );\n          setCurrentVideoComments(updatedComments);\n          setReplyText(\"\");\n          setReplyingTo(null);\n          message.success(\"Reply added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add reply\");\n        }\n      } catch (error) {\n        console.error(\"Error adding reply:\", error);\n        message.error(\"Failed to add reply\");\n      }\n    }\n  };\n\n  const handleLikeComment = async (commentId, isReply = false, replyId = null) => {\n    try {\n      const response = await likeComment(commentId, {\n        isReply,\n        replyId\n      });\n\n      if (response.success) {\n        // Update local state with the updated comment\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.map(comment =>\n          comment._id === commentId || comment.id === commentId\n            ? response.data\n            : comment\n        );\n        setCurrentVideoComments(updatedComments);\n      } else {\n        message.error(response.message || \"Failed to update like\");\n      }\n    } catch (error) {\n      console.error(\"Error updating like:\", error);\n      message.error(\"Failed to update like\");\n    }\n  };\n\n  const handleDeleteComment = async (commentId) => {\n    try {\n      const response = await deleteVideoComment(commentId);\n\n      if (response.success) {\n        // Remove comment from local state\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.filter(comment =>\n          comment._id !== commentId && comment.id !== commentId\n        );\n        setCurrentVideoComments(updatedComments);\n        message.success(\"Comment deleted successfully!\");\n      } else {\n        message.error(response.message || \"Failed to delete comment\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting comment:\", error);\n      message.error(\"Failed to delete comment\");\n    }\n  };\n\n  const handleEditComment = (comment) => {\n    setEditingComment(comment._id || comment.id);\n    setEditCommentText(comment.text);\n  };\n\n  const handleSaveEditComment = async () => {\n    if (!editCommentText.trim()) {\n      message.error(\"Comment cannot be empty\");\n      return;\n    }\n\n    try {\n      // TODO: Add API call to update comment\n      // const response = await updateVideoComment(editingComment, { text: editCommentText.trim() });\n\n      // For now, update local state\n      const currentComments = getCurrentVideoComments();\n      const updatedComments = currentComments.map(comment => {\n        if ((comment._id || comment.id) === editingComment) {\n          return { ...comment, text: editCommentText.trim() };\n        }\n        return comment;\n      });\n      setCurrentVideoComments(updatedComments);\n\n      setEditingComment(null);\n      setEditCommentText(\"\");\n      message.success(\"Comment updated successfully!\");\n    } catch (error) {\n      console.error(\"Error updating comment:\", error);\n      message.error(\"Failed to update comment\");\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setEditingComment(null);\n    setEditCommentText(\"\");\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n\n  // Render loading state\n  const renderLoadingState = () => (\n    <div className=\"loading-state\">\n      <div className=\"loading-spinner\"></div>\n      <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n    </div>\n  );\n\n  // Render error state\n  const renderErrorState = () => (\n    <div className=\"error-state\">\n      <TbAlertTriangle className=\"error-icon\" />\n      <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n      <p>{error}</p>\n      <button onClick={fetchVideos} className=\"retry-btn\">\n        {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n      </button>\n    </div>\n  );\n\n  // Render empty state\n  const renderEmptyState = () => (\n    <div className=\"empty-state\">\n      <FaGraduationCap className=\"empty-icon\" />\n      <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n      <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n      <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n    </div>\n  );\n\n  return (\n    <div className=\"video-lessons-container\">\n      {/* Header removed - using ProtectedRoute header only */}\n\n      <div className=\"video-lessons-content\">\n        {/* Enhanced Filters and Controls */}\n        <div className=\"video-controls\">\n          <div className=\"controls-row\">\n            {/* Class Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                {isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class'}\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => handleClassChange(e.target.value)}\n                className=\"control-select class-select\"\n              >\n                <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                      (isKiswahili ? `Darasa la ${cls}` : `Class ${cls}`) :\n                      `Form ${cls}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => handleSubjectChange(e.target.value)}\n                className=\"control-select subject-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSortAscending />\n                Sort\n              </label>\n              <select\n                value={sortBy}\n                onChange={(e) => handleSortChange(e.target.value)}\n                className=\"control-select sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Search Row */}\n          <div className=\"search-row\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title, subject, or topic...\"\n                value={searchTerm}\n                onChange={(e) => handleSearchChange(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                  Clear Search\n                </button>\n              )}\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh All\n            </button>\n          </div>\n        </div>\n\n        {/* Main Content Area */}\n        {loading && renderLoadingState()}\n        {!loading && error && renderErrorState()}\n        {!loading && !error && (\n          <>\n            {/* Top Pagination Controls */}\n            {totalVideos > 0 && (\n              <div className=\"pagination-container pagination-top\">\n                <div className=\"pagination-info\">\n                  Showing {startItem}-{endItem} of {totalVideos} videos\n                </div>\n\n                <div className=\"pagination-controls\">\n                  <button\n                    className=\"pagination-btn\"\n                    onClick={() => handlePageChange(currentPage - 1)}\n                    disabled={currentPage === 1}\n                  >\n                    Previous\n                  </button>\n\n                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                    let pageNum;\n                    if (totalPages <= 5) {\n                      pageNum = i + 1;\n                    } else if (currentPage <= 3) {\n                      pageNum = i + 1;\n                    } else if (currentPage >= totalPages - 2) {\n                      pageNum = totalPages - 4 + i;\n                    } else {\n                      pageNum = currentPage - 2 + i;\n                    }\n\n                    return (\n                      <button\n                        key={pageNum}\n                        className={`pagination-btn ${currentPage === pageNum ? 'active' : ''}`}\n                        onClick={() => handlePageChange(pageNum)}\n                      >\n                        {pageNum}\n                      </button>\n                    );\n                  })}\n\n                  <button\n                    className=\"pagination-btn\"\n                    onClick={() => handlePageChange(currentPage + 1)}\n                    disabled={currentPage === totalPages}\n                  >\n                    Next\n                  </button>\n                </div>\n\n                <div className=\"page-size-selector\">\n                  <span>Videos per page:</span>\n                  <select\n                    value={videosPerPage}\n                    onChange={(e) => handlePageSizeChange(Number(e.target.value))}\n                  >\n                    <option value={6}>6</option>\n                    <option value={12}>12</option>\n                    <option value={24}>24</option>\n                    <option value={48}>48</option>\n                  </select>\n                </div>\n              </div>\n            )}\n\n            <VideoGrid\n              paginatedVideos={paginatedVideos}\n              currentVideoIndex={currentVideoIndex}\n              handleShowVideo={handleShowVideo}\n              getThumbnailUrl={getThumbnailUrl}\n              getSubjectName={getSubjectName}\n              selectedLevel={selectedLevel}\n              isKiswahili={isKiswahili}\n              setVideoRef={setVideoRef}\n              setVideoError={setVideoError}\n              videoError={videoError}\n              setCurrentVideoIndex={setCurrentVideoIndex}\n              commentsExpanded={commentsExpanded}\n              setCommentsExpanded={setCommentsExpanded}\n              getCurrentVideoComments={getCurrentVideoComments}\n              newComment={newComment}\n              setNewComment={setNewComment}\n              handleAddComment={handleAddComment}\n              handleLikeComment={handleLikeComment}\n              handleDeleteComment={handleDeleteComment}\n              formatTimeAgo={formatTimeAgo}\n              user={user}\n            />\n\n\n\n    {/* Pagination Controls */}\n    {totalVideos > 0 && (\n      <div className=\"pagination-container\">\n        <div className=\"pagination-info\">\n          Showing {startItem}-{endItem} of {totalVideos} videos\n        </div>\n\n        <div className=\"pagination-controls\">\n          <button\n            className=\"pagination-btn\"\n            onClick={() => handlePageChange(currentPage - 1)}\n            disabled={currentPage === 1}\n          >\n            Previous\n          </button>\n\n          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n            let pageNum;\n            if (totalPages <= 5) {\n              pageNum = i + 1;\n            } else if (currentPage <= 3) {\n              pageNum = i + 1;\n            } else if (currentPage >= totalPages - 2) {\n              pageNum = totalPages - 4 + i;\n            } else {\n              pageNum = currentPage - 2 + i;\n            }\n\n            return (\n              <button\n                key={pageNum}\n                className={`pagination-btn ${currentPage === pageNum ? 'active' : ''}`}\n                onClick={() => handlePageChange(pageNum)}\n              >\n                {pageNum}\n              </button>\n            );\n          })}\n\n          <button\n            className=\"pagination-btn\"\n            onClick={() => handlePageChange(currentPage + 1)}\n            disabled={currentPage === totalPages}\n          >\n            Next\n          </button>\n        </div>\n\n        <div className=\"page-size-selector\">\n          <span>Videos per page:</span>\n          <select\n            value={videosPerPage}\n            onChange={(e) => handlePageSizeChange(Number(e.target.value))}\n          >\n            <option value={6}>6</option>\n            <option value={12}>12</option>\n            <option value={24}>24</option>\n            <option value={48}>48</option>\n          </select>\n        </div>\n      </div>\n    )}\n  </>\n)}\n</div>\n</div>\n);\n}\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAChF,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,WAAW,EAAEC,kBAAkB,QAAQ,iCAAiC;AACrI,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAC1H,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,SAAS,MAAM,aAAa;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,YAAY,EAAEA,CAAA,kBAAMJ,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9DC,eAAe,EAAEA,CAAA,kBAAMZ,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACjEE,OAAO,EAAEA,CAAA,kBAAMb,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACxDG,QAAQ,EAAEA,CAAA,kBAAMd,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDI,UAAU,EAAEA,CAAA,kBAAMf,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC3DK,OAAO,EAAEA,CAAA,kBAAMhB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDM,YAAY,EAAEA,CAAA,kBAAMjB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9DO,eAAe,EAAEA,CAAA,kBAAMlB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACjEQ,QAAQ,EAAEA,CAAA,kBAAMnB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DS,eAAe,EAAEA,CAAA,kBAAMpB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAChEU,QAAQ,EAAEA,CAAA,kBAAMrB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DW,GAAG,EAAEA,CAAA,kBAAMtB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACpDY,UAAU,EAAEA,CAAA,kBAAMvB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAC5D,CAAC;;AAED;AACA,MAAM;EACJP,YAAY;EACZQ,eAAe;EACfC,OAAO;EACPC,QAAQ;EACRC,UAAU;EACVC,OAAO;EACPG,QAAQ;EACRC,eAAe;EACfC,QAAQ;EACRC,GAAG;EACHC,UAAU;EACVL,eAAe;EACfD;AACF,CAAC,GAAGd,cAAc;AAElB,SAASqB,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGtC,WAAW,CAAEuC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAe,CAAC,GAAGnC,WAAW,CAAC,CAAC;EACtE,MAAMoC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM8C,YAAY,GAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;EAED;EACA5D,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAM2D,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACpDF,YAAY,CAACG,WAAW,GAAGJ,YAAY;IACvCE,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,YAAY,CAAC;IAEvC,OAAO,MAAM;MACXC,QAAQ,CAACG,IAAI,CAACE,WAAW,CAACN,YAAY,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqE,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,KAAK,EAAEC,QAAQ,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,CAAAoD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,KAAK,KAAI,SAAS,CAAC;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,MAAM;IACvD;IACA,OAAO8E,YAAY,CAACC,OAAO,CAAC,8BAA8B,CAAC,KAAI3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,KAAK,KAAI,KAAK;EACrF,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlF,QAAQ,CAAC,MAAM;IAC3D;IACA,OAAO8E,YAAY,CAACC,OAAO,CAAC,gCAAgC,CAAC,IAAI,KAAK;EACxE,CAAC,CAAC;EACF,MAAM,CAACI,UAAU,EAAEC,aAAa,CAAC,GAAGpF,QAAQ,CAAC,MAAM;IACjD;IACA,OAAO8E,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,IAAI,EAAE;EAChE,CAAC,CAAC;EACF,MAAM,CAACM,MAAM,EAAEC,SAAS,CAAC,GAAGtF,QAAQ,CAAC,MAAM;IACzC;IACA,OAAO8E,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,IAAI,QAAQ;EAClE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACQ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACyF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2F,eAAe,EAAEC,kBAAkB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+F,QAAQ,EAAEC,WAAW,CAAC,GAAGhG,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAACiG,WAAW,EAAEC,cAAc,CAAC,GAAGlG,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmG,aAAa,EAAEC,gBAAgB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqG,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC,CAAC,CAAC;;EAEjD;EACA,MAAM,CAACuG,aAAa,EAAEC,gBAAgB,CAAC,GAAGxG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACyG,UAAU,EAAEC,aAAa,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2G,UAAU,EAAEC,aAAa,CAAC,GAAG5G,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC6G,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+G,YAAY,EAAEC,eAAe,CAAC,GAAGhH,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmH,cAAc,EAAEC,iBAAiB,CAAC,GAAGpH,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqH,eAAe,EAAEC,kBAAkB,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAMuH,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIhC,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAMiC,YAAY,GAAGC,eAAe,CAAClC,iBAAiB,CAAC;IACvD,IAAI,CAACiC,YAAY,EAAE,OAAO,EAAE;;IAE5B;IACA,MAAME,OAAO,GAAGF,YAAY,CAACG,EAAE,IAAIH,YAAY,CAACI,GAAG;IACnD,OAAOrB,aAAa,CAACmB,OAAO,CAAC,IAAI,EAAE;EACrC,CAAC;;EAED;EACA,MAAMG,uBAAuB,GAAIC,QAAQ,IAAK;IAC5C,IAAIvC,iBAAiB,KAAK,IAAI,EAAE;IAChC,MAAMiC,YAAY,GAAGC,eAAe,CAAClC,iBAAiB,CAAC;IACvD,IAAI,CAACiC,YAAY,EAAE;;IAEnB;IACA,MAAME,OAAO,GAAGF,YAAY,CAACG,EAAE,IAAIH,YAAY,CAACI,GAAG;IACnDpB,gBAAgB,CAACuB,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACL,OAAO,GAAGI;IACb,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAG7H,OAAO,CAAC,MAAM;IACrC,IAAIsE,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACpH,IAAIA,aAAa,KAAK,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9D,IAAIA,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMwD,iBAAiB,GAAG9H,OAAO,CAAC,MAAM;IACtC,IAAIsE,aAAa,KAAK,SAAS,EAAE,OAAOvD,eAAe;IACvD,IAAIuD,aAAa,KAAK,mBAAmB,EAAE,OAAOtD,wBAAwB;IAC1E,IAAIsD,aAAa,KAAK,WAAW,EAAE,OAAOrD,iBAAiB;IAC3D,IAAIqD,aAAa,KAAK,SAAS,EAAE,OAAOpD,eAAe;IACvD,OAAO,EAAE;EACX,CAAC,EAAE,CAACoD,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMyD,WAAW,GAAGhI,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAiI,cAAA;MACF7D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdd,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMoH,OAAO,GAAG;QACdzD,KAAK,EAAEF,aAAa;QACpB4D,SAAS,EAAE,KAAK;QAAE;QAClBC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMjI,gBAAgB,CAAC6H,OAAO,CAAC;MAEhD,IAAII,QAAQ,aAARA,QAAQ,gBAAAL,cAAA,GAARK,QAAQ,CAAEC,IAAI,cAAAN,cAAA,eAAdA,cAAA,CAAgBO,OAAO,EAAE;QAC3B,MAAMC,SAAS,GAAGH,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE;QAC1CrE,SAAS,CAACuE,SAAS,CAAC;;QAEpB;QACA,MAAMC,oBAAoB,CAACD,SAAS,CAAC;MACvC,CAAC,MAAM;QAAA,IAAAE,eAAA;QACLrE,QAAQ,CAAC,CAAAgE,QAAQ,aAARA,QAAQ,wBAAAK,eAAA,GAARL,QAAQ,CAAEC,IAAI,cAAAI,eAAA,uBAAdA,eAAA,CAAgB5H,OAAO,KAAI,wBAAwB,CAAC;QAC7DmD,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjBZ,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC0D,aAAa,EAAEf,QAAQ,CAAC,CAAC;;EAE7B;EACA,MAAMqF,uBAAuB,GAAG5I,OAAO,CAAC,MAAM;IAG5C,IAAI6I,QAAQ,GAAG7E,MAAM;;IAErB;IACA6E,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACvE,KAAK,KAAKF,aAAa,CAAC;;IAElE;IACA,IAAIG,aAAa,KAAK,KAAK,EAAE;MAC3BoE,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAI;QAClC;QACA,MAAMC,UAAU,GAAGD,KAAK,CAACb,SAAS,IAAIa,KAAK,CAAClE,KAAK;QACjD,OAAOmE,UAAU,KAAKvE,aAAa;MACrC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIK,eAAe,KAAK,KAAK,EAAE;MAC7B+D,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACZ,OAAO,KAAKrD,eAAe,CAAC;IACxE;;IAEA;IACA,IAAIE,UAAU,CAACiE,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAGlE,UAAU,CAACmE,WAAW,CAAC,CAAC;MAC5CN,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK;QAAA,IAAAK,YAAA,EAAAC,cAAA,EAAAC,YAAA;QAAA,OAC9B,EAAAF,YAAA,GAAAL,KAAK,CAACQ,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaD,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAG,cAAA,GAChDN,KAAK,CAACZ,OAAO,cAAAkB,cAAA,uBAAbA,cAAA,CAAeF,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAI,YAAA,GAClDP,KAAK,CAACU,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaH,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC;MAAA,CAClD,CAAC;IACH;;IAEA;IACA,MAAMQ,MAAM,GAAG,CAAC,GAAGb,QAAQ,CAAC,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,QAAQ3E,MAAM;QACZ,KAAK,QAAQ;UACX,OAAO,IAAI4E,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,QAAQ;UACX,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,OAAO;UACV,OAAO,CAACH,CAAC,CAACL,KAAK,IAAI,EAAE,EAAES,aAAa,CAACH,CAAC,CAACN,KAAK,IAAI,EAAE,CAAC;QACrD,KAAK,SAAS;UACZ,OAAO,CAACK,CAAC,CAACzB,OAAO,IAAI,EAAE,EAAE6B,aAAa,CAACH,CAAC,CAAC1B,OAAO,IAAI,EAAE,CAAC;QACzD;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;;IAEF;IACAhC,cAAc,CAACuD,MAAM,CAACO,MAAM,CAAC;IAE7BtB,OAAO,CAACuB,GAAG,CAAC,0BAA0B,EAAER,MAAM,CAACO,MAAM,CAAC;IACtD,IAAIP,MAAM,CAACO,MAAM,GAAG,CAAC,EAAE;MACrBtB,OAAO,CAACuB,GAAG,CAAC,2BAA2B,EAAER,MAAM,CAAC,CAAC,CAAC,CAAC;IACrD;IAEA,OAAOA,MAAM;EACf,CAAC,EAAE,CAAC1F,MAAM,EAAEgB,UAAU,EAAEE,MAAM,EAAEZ,aAAa,EAAEG,aAAa,EAAEK,eAAe,CAAC,CAAC;;EAE/E;EACA,MAAMwC,eAAe,GAAGtH,OAAO,CAAC,MAAM;IACpC,MAAMmK,UAAU,GAAG,CAACrE,WAAW,GAAG,CAAC,IAAIE,aAAa;IACpD,MAAMoE,QAAQ,GAAGD,UAAU,GAAGnE,aAAa;IAC3C,OAAO4C,uBAAuB,CAACyB,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC5D,CAAC,EAAE,CAACxB,uBAAuB,EAAE9C,WAAW,EAAEE,aAAa,CAAC,CAAC;;EAEzD;EACA,MAAMsE,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACtE,WAAW,GAAGF,aAAa,CAAC;EACzD,MAAMyE,SAAS,GAAG,CAAC3E,WAAW,GAAG,CAAC,IAAIE,aAAa,GAAG,CAAC;EACvD,MAAM0E,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC7E,WAAW,GAAGE,aAAa,EAAEE,WAAW,CAAC;;EAElE;EACA,MAAM0E,gBAAgB,GAAIC,IAAI,IAAK;IACjC9E,cAAc,CAAC8E,IAAI,CAAC;IACpBxF,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED,MAAMyF,oBAAoB,GAAIC,OAAO,IAAK;IACxC9E,gBAAgB,CAAC8E,OAAO,CAAC;IACzBhF,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMiF,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMlC,KAAK,GAAGzB,eAAe,CAAC2D,KAAK,CAAC;IAEpC5F,oBAAoB,CAAC4F,KAAK,CAAC;IAC3B1F,mBAAmB,CAAC,CAAC0F,KAAK,CAAC,CAAC;IAC5BxF,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAM4B,OAAO,GAAG,CAAAwB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEvB,EAAE,MAAIuB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEtB,GAAG;IACvC,IAAIF,OAAO,IAAI,CAACnB,aAAa,CAACmB,OAAO,CAAC,EAAE;MACtC2D,iBAAiB,CAAC3D,OAAO,CAAC;IAC5B;;IAEA;IACA,IAAIwB,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEoC,QAAQ,KAAKpC,KAAK,CAACoC,QAAQ,CAAC3B,QAAQ,CAAC,eAAe,CAAC,IAAIT,KAAK,CAACoC,QAAQ,CAAC3B,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAM4B,SAAS,GAAG,MAAMC,iBAAiB,CAACtC,KAAK,CAACoC,QAAQ,CAAC;QACzDpC,KAAK,CAACuC,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAOhH,KAAK,EAAE;QACduE,OAAO,CAAC4C,IAAI,CAAC,8CAA8C,CAAC;QAC5DxC,KAAK,CAACuC,cAAc,GAAGvC,KAAK,CAACoC,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BjG,mBAAmB,CAAC,EAAE,CAAC;IACvBF,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC6F,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCjG,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAM6F,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAAC3B,QAAQ,CAAC,eAAe,CAAC,IAAI2B,QAAQ,CAAC3B,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAMnB,QAAQ,GAAG,MAAMsD,KAAK,CAAE,6DAA4DC,kBAAkB,CAACT,QAAQ,CAAE,EAAC,EAAE;UACxHU,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACzD,QAAQ,CAAC0D,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsB3D,QAAQ,CAAC4D,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAM3D,IAAI,GAAG,MAAMD,QAAQ,CAAC6D,IAAI,CAAC,CAAC;QAElC,IAAI5D,IAAI,CAACC,OAAO,IAAID,IAAI,CAAC8C,SAAS,EAAE;UAClCzC,OAAO,CAACuB,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAO5B,IAAI,CAAC8C,SAAS;QACvB,CAAC,MAAM;UACLzC,OAAO,CAAC4C,IAAI,CAAC,+CAA+C,EAAEjD,IAAI,CAAC;UACnE,OAAO6C,QAAQ;QACjB;MACF,CAAC,CAAC,OAAO/G,KAAK,EAAE;QACduE,OAAO,CAACvE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAO+G,QAAQ;MACjB;IACF;IAEA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAIpD,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACqD,SAAS,EAAE;MACnB,OAAOrD,KAAK,CAACqD,SAAS;IACxB;IAEA,IAAIrD,KAAK,CAACsD,OAAO,IAAI,CAACtD,KAAK,CAACsD,OAAO,CAAC7C,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAIjC,OAAO,GAAGwB,KAAK,CAACsD,OAAO;MAC3B,IAAI9E,OAAO,CAACiC,QAAQ,CAAC,aAAa,CAAC,IAAIjC,OAAO,CAACiC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAM8C,KAAK,GAAG/E,OAAO,CAAC+E,KAAK,CAAC,oDAAoD,CAAC;QACjF/E,OAAO,GAAG+E,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG/E,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IAEA,OAAO,0BAA0B;EACnC,CAAC;;EAED;EACAzH,SAAS,CAAC,MAAM;IACdiI,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMwE,iBAAiB,GAAIC,KAAK,IAAK;IACnC9H,gBAAgB,CAAC8H,KAAK,CAAC;IACvB7H,YAAY,CAAC8H,OAAO,CAAC,8BAA8B,EAAED,KAAK,CAAC;EAC7D,CAAC;EAED,MAAME,mBAAmB,GAAIF,KAAK,IAAK;IACrCzH,kBAAkB,CAACyH,KAAK,CAAC;IACzB7H,YAAY,CAAC8H,OAAO,CAAC,gCAAgC,EAAED,KAAK,CAAC;EAC/D,CAAC;EAED,MAAMG,kBAAkB,GAAIH,KAAK,IAAK;IACpCvH,aAAa,CAACuH,KAAK,CAAC;IACpB7H,YAAY,CAAC8H,OAAO,CAAC,2BAA2B,EAAED,KAAK,CAAC;EAC1D,CAAC;EAED,MAAMI,gBAAgB,GAAIJ,KAAK,IAAK;IAClCrH,SAAS,CAACqH,KAAK,CAAC;IAChB7H,YAAY,CAAC8H,OAAO,CAAC,uBAAuB,EAAED,KAAK,CAAC;EACtD,CAAC;EAED1M,SAAS,CAAC,MAAM;IACd,IAAImD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuB,KAAK,EAAE;MACfD,gBAAgB,CAACtB,IAAI,CAACuB,KAAK,CAAC;IAC9B;IACA;IACA,IAAIvB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,KAAK,IAAI,CAACF,YAAY,CAACC,OAAO,CAAC,8BAA8B,CAAC,EAAE;MACxE2H,iBAAiB,CAACtJ,IAAI,CAAC4B,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM4J,iBAAiB,GAAGA,CAAA,KAAM;IAC9BF,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA/E,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMgF,cAAc,GAAGA,CAAA,KAAM;IAC3BJ,kBAAkB,CAAC,EAAE,CAAC;IACtBD,mBAAmB,CAAC,KAAK,CAAC;IAC1BH,iBAAiB,CAAC,KAAK,CAAC;IACxBK,gBAAgB,CAAC,QAAQ,CAAC;IAC1B7E,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMU,oBAAoB,GAAG,MAAOuE,SAAS,IAAK;IAChD,IAAI;MACFrE,OAAO,CAACuB,GAAG,CAAC,qCAAqC,EAAE8C,SAAS,CAAC/C,MAAM,CAAC;MACpE,MAAMgD,WAAW,GAAG,CAAC,CAAC;;MAEtB;MACA,KAAK,MAAMlE,KAAK,IAAIiE,SAAS,EAAE;QAC7B,MAAMzF,OAAO,GAAGwB,KAAK,CAACvB,EAAE,IAAIuB,KAAK,CAACtB,GAAG;QACrC,IAAIF,OAAO,EAAE;UACX,IAAI;YACF,MAAMc,QAAQ,GAAG,MAAMhI,gBAAgB,CAACkH,OAAO,CAAC;YAChD,IAAIc,QAAQ,CAACE,OAAO,EAAE;cACpB0E,WAAW,CAAC1F,OAAO,CAAC,GAAGc,QAAQ,CAACC,IAAI,CAACX,QAAQ;cAC7CgB,OAAO,CAACuB,GAAG,CAAE,aAAY7B,QAAQ,CAACC,IAAI,CAACX,QAAQ,CAACsC,MAAO,uBAAsB1C,OAAQ,EAAC,CAAC;YACzF;UACF,CAAC,CAAC,OAAOnD,KAAK,EAAE;YACduE,OAAO,CAACvE,KAAK,CAAE,oCAAmCmD,OAAQ,GAAE,EAAEnD,KAAK,CAAC;UACtE;QACF;MACF;MAEAiC,gBAAgB,CAAC4G,WAAW,CAAC;MAC7BtE,OAAO,CAACuB,GAAG,CAAC,8BAA8B,EAAE+C,WAAW,CAAC;IAC1D,CAAC,CAAC,OAAO7I,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;;EAED;EACA,MAAM8G,iBAAiB,GAAG,MAAO3D,OAAO,IAAK;IAC3C,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMhI,gBAAgB,CAACkH,OAAO,CAAC;MAChD,IAAIc,QAAQ,CAACE,OAAO,EAAE;QACpBlC,gBAAgB,CAACuB,IAAI,KAAK;UACxB,GAAGA,IAAI;UACP,CAACL,OAAO,GAAGc,QAAQ,CAACC,IAAI,CAACX;QAC3B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAM8I,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI5G,UAAU,CAAC2C,IAAI,CAAC,CAAC,EAAE;MACrB,MAAM5B,YAAY,GAAGuB,uBAAuB,CAACxD,iBAAiB,CAAC;MAC/D,IAAI,CAACiC,YAAY,EAAE;MAEnB,IAAI;QACFsB,OAAO,CAACuB,GAAG,CAAC,0BAA0B,EAAE7C,YAAY,CAAC;QACrDsB,OAAO,CAACuB,GAAG,CAAC,gBAAgB,EAAEiD,MAAM,CAACC,IAAI,CAAC/F,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9DsB,OAAO,CAACuB,GAAG,CAAC,oBAAoB,EAAE7C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,EAAE,CAAC;QACnDmB,OAAO,CAACuB,GAAG,CAAC,qBAAqB,EAAE7C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEI,GAAG,CAAC;;QAErD;QACA,MAAMF,OAAO,GAAGF,YAAY,CAACG,EAAE,IAAIH,YAAY,CAACI,GAAG;QAEnD,MAAM4F,WAAW,GAAG;UAClB9F,OAAO,EAAEA,OAAO;UAChB+F,IAAI,EAAEhH,UAAU,CAAC2C,IAAI,CAAC;QACxB,CAAC;QAEDN,OAAO,CAACuB,GAAG,CAAC,2BAA2B,EAAEmD,WAAW,CAAC;QACrD1E,OAAO,CAACuB,GAAG,CAAC,uBAAuB,EAAEiD,MAAM,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC;QAC9D1E,OAAO,CAACuB,GAAG,CAAC,mBAAmB,EAAE3C,OAAO,EAAE,QAAQ,EAAE,OAAOA,OAAO,EAAE,GAAG,CAAC;QACxEoB,OAAO,CAACuB,GAAG,CAAC,gBAAgB,EAAE5D,UAAU,CAAC2C,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO3C,UAAU,CAAC2C,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;QAEzF,MAAMZ,QAAQ,GAAG,MAAM/H,eAAe,CAAC+M,WAAW,CAAC;QAEnD,IAAIhF,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMgF,OAAO,GAAG;YACd9F,GAAG,EAAEY,QAAQ,CAACC,IAAI,CAACb,GAAG;YACtB6F,IAAI,EAAEjF,QAAQ,CAACC,IAAI,CAACgF,IAAI;YACxBE,MAAM,EAAEnF,QAAQ,CAACC,IAAI,CAACkF,MAAM;YAC5BC,MAAM,EAAEpF,QAAQ,CAACC,IAAI,CAACmF,MAAM;YAC5B1D,SAAS,EAAE1B,QAAQ,CAACC,IAAI,CAACyB,SAAS;YAClC2D,OAAO,EAAE,EAAE;YACXC,KAAK,EAAE,CAAC;YACRC,OAAO,EAAE;UACX,CAAC;UACD,MAAMC,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;UACjDM,uBAAuB,CAAC,CAAC6F,OAAO,EAAE,GAAGM,eAAe,CAAC,CAAC;UACtDtH,aAAa,CAAC,EAAE,CAAC;UACjBzF,OAAO,CAACyH,OAAO,CAAC,6BAA6B,CAAC;QAChD,CAAC,MAAM;UACLzH,OAAO,CAACsD,KAAK,CAACiE,QAAQ,CAACvH,OAAO,IAAI,uBAAuB,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOsD,KAAK,EAAE;QACduE,OAAO,CAACvE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CtD,OAAO,CAACsD,KAAK,CAAC,uBAAuB,CAAC;MACxC;IACF;EACF,CAAC;EAID,MAAM0J,cAAc,GAAG,MAAOC,SAAS,IAAK;IAC1C,IAAIrH,SAAS,CAACuC,IAAI,CAAC,CAAC,EAAE;MACpB,IAAI;QACF,MAAMZ,QAAQ,GAAG,MAAM9H,eAAe,CAACwN,SAAS,EAAE;UAChDT,IAAI,EAAE5G,SAAS,CAACuC,IAAI,CAAC;QACvB,CAAC,CAAC;QAEF,IAAIZ,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMsF,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;UACjD,MAAM4G,eAAe,GAAGH,eAAe,CAACI,GAAG,CAACV,OAAO,IACjDA,OAAO,CAAC9F,GAAG,KAAKsG,SAAS,IAAIR,OAAO,CAAC/F,EAAE,KAAKuG,SAAS,GACjD;YAAE,GAAGR,OAAO;YAAEG,OAAO,EAAErF,QAAQ,CAACC,IAAI,CAACoF;UAAQ,CAAC,GAC9CH,OACN,CAAC;UACD7F,uBAAuB,CAACsG,eAAe,CAAC;UACxCrH,YAAY,CAAC,EAAE,CAAC;UAChBF,aAAa,CAAC,IAAI,CAAC;UACnB3F,OAAO,CAACyH,OAAO,CAAC,2BAA2B,CAAC;QAC9C,CAAC,MAAM;UACLzH,OAAO,CAACsD,KAAK,CAACiE,QAAQ,CAACvH,OAAO,IAAI,qBAAqB,CAAC;QAC1D;MACF,CAAC,CAAC,OAAOsD,KAAK,EAAE;QACduE,OAAO,CAACvE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CtD,OAAO,CAACsD,KAAK,CAAC,qBAAqB,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAM8J,iBAAiB,GAAG,MAAAA,CAAOH,SAAS,EAAEI,OAAO,GAAG,KAAK,EAAEC,OAAO,GAAG,IAAI,KAAK;IAC9E,IAAI;MACF,MAAM/F,QAAQ,GAAG,MAAM7H,WAAW,CAACuN,SAAS,EAAE;QAC5CI,OAAO;QACPC;MACF,CAAC,CAAC;MAEF,IAAI/F,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMsF,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;QACjD,MAAM4G,eAAe,GAAGH,eAAe,CAACI,GAAG,CAACV,OAAO,IACjDA,OAAO,CAAC9F,GAAG,KAAKsG,SAAS,IAAIR,OAAO,CAAC/F,EAAE,KAAKuG,SAAS,GACjD1F,QAAQ,CAACC,IAAI,GACbiF,OACN,CAAC;QACD7F,uBAAuB,CAACsG,eAAe,CAAC;MAC1C,CAAC,MAAM;QACLlN,OAAO,CAACsD,KAAK,CAACiE,QAAQ,CAACvH,OAAO,IAAI,uBAAuB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CtD,OAAO,CAACsD,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;EAED,MAAMiK,mBAAmB,GAAG,MAAON,SAAS,IAAK;IAC/C,IAAI;MACF,MAAM1F,QAAQ,GAAG,MAAM5H,kBAAkB,CAACsN,SAAS,CAAC;MAEpD,IAAI1F,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMsF,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;QACjD,MAAM4G,eAAe,GAAGH,eAAe,CAAC/E,MAAM,CAACyE,OAAO,IACpDA,OAAO,CAAC9F,GAAG,KAAKsG,SAAS,IAAIR,OAAO,CAAC/F,EAAE,KAAKuG,SAC9C,CAAC;QACDrG,uBAAuB,CAACsG,eAAe,CAAC;QACxClN,OAAO,CAACyH,OAAO,CAAC,+BAA+B,CAAC;MAClD,CAAC,MAAM;QACLzH,OAAO,CAACsD,KAAK,CAACiE,QAAQ,CAACvH,OAAO,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CtD,OAAO,CAACsD,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMkK,iBAAiB,GAAIf,OAAO,IAAK;IACrCtG,iBAAiB,CAACsG,OAAO,CAAC9F,GAAG,IAAI8F,OAAO,CAAC/F,EAAE,CAAC;IAC5CL,kBAAkB,CAACoG,OAAO,CAACD,IAAI,CAAC;EAClC,CAAC;EAED,MAAMiB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACrH,eAAe,CAAC+B,IAAI,CAAC,CAAC,EAAE;MAC3BnI,OAAO,CAACsD,KAAK,CAAC,yBAAyB,CAAC;MACxC;IACF;IAEA,IAAI;MACF;MACA;;MAEA;MACA,MAAMyJ,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;MACjD,MAAM4G,eAAe,GAAGH,eAAe,CAACI,GAAG,CAACV,OAAO,IAAI;QACrD,IAAI,CAACA,OAAO,CAAC9F,GAAG,IAAI8F,OAAO,CAAC/F,EAAE,MAAMR,cAAc,EAAE;UAClD,OAAO;YAAE,GAAGuG,OAAO;YAAED,IAAI,EAAEpG,eAAe,CAAC+B,IAAI,CAAC;UAAE,CAAC;QACrD;QACA,OAAOsE,OAAO;MAChB,CAAC,CAAC;MACF7F,uBAAuB,CAACsG,eAAe,CAAC;MAExC/G,iBAAiB,CAAC,IAAI,CAAC;MACvBE,kBAAkB,CAAC,EAAE,CAAC;MACtBrG,OAAO,CAACyH,OAAO,CAAC,+BAA+B,CAAC;IAClD,CAAC,CAAC,OAAOnE,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CtD,OAAO,CAACsD,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMoK,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvH,iBAAiB,CAAC,IAAI,CAAC;IACvBE,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMsH,aAAa,GAAIC,SAAS,IAAK;IACnC,MAAMC,GAAG,GAAG,IAAI7E,IAAI,CAAC,CAAC;IACtB,MAAM8E,IAAI,GAAG,IAAI9E,IAAI,CAAC4E,SAAS,CAAC;IAChC,MAAMG,aAAa,GAAGtE,IAAI,CAACuE,KAAK,CAAC,CAACH,GAAG,GAAGC,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEtE,IAAI,CAACuE,KAAK,CAACD,aAAa,GAAG,EAAE,CAAE,OAAM;IACzE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEtE,IAAI,CAACuE,KAAK,CAACD,aAAa,GAAG,IAAI,CAAE,OAAM;IAC5E,IAAIA,aAAa,GAAG,MAAM,EAAE,OAAQ,GAAEtE,IAAI,CAACuE,KAAK,CAACD,aAAa,GAAG,KAAK,CAAE,OAAM;IAC9E,OAAOD,IAAI,CAACG,kBAAkB,CAAC,CAAC;EAClC,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,kBACzBzN,OAAA;IAAK2G,SAAS,EAAC,eAAe;IAAApG,QAAA,gBAC5BP,OAAA;MAAK2G,SAAS,EAAC;IAAiB;MAAAnG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvCX,OAAA;MAAAO,QAAA,EAAIsB,WAAW,GAAG,mBAAmB,GAAG;IAAmB;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7D,CACN;;EAED;EACA,MAAM+M,gBAAgB,GAAGA,CAAA,kBACvB1N,OAAA;IAAK2G,SAAS,EAAC,aAAa;IAAApG,QAAA,gBAC1BP,OAAA,CAACkB,eAAe;MAACyF,SAAS,EAAC;IAAY;MAAAnG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1CX,OAAA;MAAAO,QAAA,EAAKsB,WAAW,GAAG,2BAA2B,GAAG;IAAsB;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC7EX,OAAA;MAAAO,QAAA,EAAIsC;IAAK;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACdX,OAAA;MAAQ2N,OAAO,EAAEnH,WAAY;MAACG,SAAS,EAAC,WAAW;MAAApG,QAAA,EAChDsB,WAAW,GAAG,aAAa,GAAG;IAAW;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;;EAED;EACA,MAAMiN,gBAAgB,GAAGA,CAAA,kBACvB5N,OAAA;IAAK2G,SAAS,EAAC,aAAa;IAAApG,QAAA,gBAC1BP,OAAA,CAACY,eAAe;MAAC+F,SAAS,EAAC;IAAY;MAAAnG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1CX,OAAA;MAAAO,QAAA,EAAKsB,WAAW,GAAG,6BAA6B,GAAG;IAAiB;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC1EX,OAAA;MAAAO,QAAA,EAAIsB,WAAW,GAAG,kEAAkE,GAAG;IAA4D;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxJX,OAAA;MAAG2G,SAAS,EAAC,YAAY;MAAApG,QAAA,EAAEsB,WAAW,GAAG,yCAAyC,GAAG;IAA6C;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpI,CACN;EAED,oBACEX,OAAA;IAAK2G,SAAS,EAAC,yBAAyB;IAAApG,QAAA,eAGtCP,OAAA;MAAK2G,SAAS,EAAC,uBAAuB;MAAApG,QAAA,gBAEpCP,OAAA;QAAK2G,SAAS,EAAC,gBAAgB;QAAApG,QAAA,gBAC7BP,OAAA;UAAK2G,SAAS,EAAC,cAAc;UAAApG,QAAA,gBAE3BP,OAAA;YAAK2G,SAAS,EAAC,eAAe;YAAApG,QAAA,gBAC5BP,OAAA;cAAO2G,SAAS,EAAC,eAAe;cAAApG,QAAA,gBAC9BP,OAAA,CAACmB,QAAQ;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACXkB,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACRX,OAAA;cACEiL,KAAK,EAAE/H,aAAc;cACrB2K,QAAQ,EAAGC,CAAC,IAAK9C,iBAAiB,CAAC8C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;cACnDtE,SAAS,EAAC,6BAA6B;cAAApG,QAAA,gBAEvCP,OAAA;gBAAQiL,KAAK,EAAC,KAAK;gBAAA1K,QAAA,EAAEsB,WAAW,GAAG,eAAe,GAAG;cAAa;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,EAC3E2F,gBAAgB,CAACoG,GAAG,CAAEsB,GAAG,iBACxBhO,OAAA;gBAAkBiL,KAAK,EAAE+C,GAAI;gBAAAzN,QAAA,EAC1BwC,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClElB,WAAW,GAAI,aAAYmM,GAAI,EAAC,GAAI,SAAQA,GAAI,EAAC,GACjD,QAAOA,GAAI;cAAC,GAHJA,GAAG;gBAAAxN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIR,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNX,OAAA;YAAK2G,SAAS,EAAC,eAAe;YAAApG,QAAA,gBAC5BP,OAAA;cAAO2G,SAAS,EAAC,eAAe;cAAApG,QAAA,gBAC9BP,OAAA,CAACmB,QAAQ;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEiL,KAAK,EAAE1H,eAAgB;cACvBsK,QAAQ,EAAGC,CAAC,IAAK3C,mBAAmB,CAAC2C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;cACrDtE,SAAS,EAAC,+BAA+B;cAAApG,QAAA,gBAEzCP,OAAA;gBAAQiL,KAAK,EAAC,KAAK;gBAAA1K,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxC4F,iBAAiB,CAACmG,GAAG,CAAE9F,OAAO,iBAC7B5G,OAAA;gBAAsBiL,KAAK,EAAErE,OAAQ;gBAAArG,QAAA,EAClCqG;cAAO,GADGA,OAAO;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNX,OAAA;YAAK2G,SAAS,EAAC,eAAe;YAAApG,QAAA,gBAC5BP,OAAA;cAAO2G,SAAS,EAAC,eAAe;cAAApG,QAAA,gBAC9BP,OAAA,CAACoB,eAAe;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEiL,KAAK,EAAEtH,MAAO;cACdkK,QAAQ,EAAGC,CAAC,IAAKzC,gBAAgB,CAACyC,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;cAClDtE,SAAS,EAAC,4BAA4B;cAAApG,QAAA,gBAEtCP,OAAA;gBAAQiL,KAAK,EAAC,QAAQ;gBAAA1K,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CX,OAAA;gBAAQiL,KAAK,EAAC,QAAQ;gBAAA1K,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CX,OAAA;gBAAQiL,KAAK,EAAC,OAAO;gBAAA1K,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCX,OAAA;gBAAQiL,KAAK,EAAC,SAAS;gBAAA1K,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNX,OAAA;UAAK2G,SAAS,EAAC,YAAY;UAAApG,QAAA,gBACzBP,OAAA;YAAK2G,SAAS,EAAC,kBAAkB;YAAApG,QAAA,gBAC/BP,OAAA,CAACqB,QAAQ;cAACsF,SAAS,EAAC;YAAa;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCX,OAAA;cACEiO,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DjD,KAAK,EAAExH,UAAW;cAClBoK,QAAQ,EAAGC,CAAC,IAAK1C,kBAAkB,CAAC0C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;cACpDtE,SAAS,EAAC;YAAc;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EACD8C,UAAU,iBACTzD,OAAA;cAAQ2N,OAAO,EAAErC,iBAAkB;cAAC3E,SAAS,EAAC,kBAAkB;cAAApG,QAAA,gBAC9DP,OAAA,CAACsB,GAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENX,OAAA;YAAQ2N,OAAO,EAAEpC,aAAc;YAAC5E,SAAS,EAAC,aAAa;YAAApG,QAAA,gBACrDP,OAAA,CAACuB,UAAU;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLgC,OAAO,IAAI8K,kBAAkB,CAAC,CAAC,EAC/B,CAAC9K,OAAO,IAAIE,KAAK,IAAI6K,gBAAgB,CAAC,CAAC,EACvC,CAAC/K,OAAO,IAAI,CAACE,KAAK,iBACjB7C,OAAA,CAAAE,SAAA;QAAAK,QAAA,GAEGoE,WAAW,GAAG,CAAC,iBACd3E,OAAA;UAAK2G,SAAS,EAAC,qCAAqC;UAAApG,QAAA,gBAClDP,OAAA;YAAK2G,SAAS,EAAC,iBAAiB;YAAApG,QAAA,GAAC,UACvB,EAAC2I,SAAS,EAAC,GAAC,EAACC,OAAO,EAAC,MAAI,EAACxE,WAAW,EAAC,SAChD;UAAA;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENX,OAAA;YAAK2G,SAAS,EAAC,qBAAqB;YAAApG,QAAA,gBAClCP,OAAA;cACE2G,SAAS,EAAC,gBAAgB;cAC1BgH,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAAC9E,WAAW,GAAG,CAAC,CAAE;cACjD4J,QAAQ,EAAE5J,WAAW,KAAK,CAAE;cAAAhE,QAAA,EAC7B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAERyN,KAAK,CAACC,IAAI,CAAC;cAAE3F,MAAM,EAAEM,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEL,UAAU;YAAE,CAAC,EAAE,CAACuF,CAAC,EAAEC,CAAC,KAAK;cACzD,IAAIC,OAAO;cACX,IAAIzF,UAAU,IAAI,CAAC,EAAE;gBACnByF,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAIhK,WAAW,IAAI,CAAC,EAAE;gBAC3BiK,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAIhK,WAAW,IAAIwE,UAAU,GAAG,CAAC,EAAE;gBACxCyF,OAAO,GAAGzF,UAAU,GAAG,CAAC,GAAGwF,CAAC;cAC9B,CAAC,MAAM;gBACLC,OAAO,GAAGjK,WAAW,GAAG,CAAC,GAAGgK,CAAC;cAC/B;cAEA,oBACEvO,OAAA;gBAEE2G,SAAS,EAAG,kBAAiBpC,WAAW,KAAKiK,OAAO,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACvEb,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAACmF,OAAO,CAAE;gBAAAjO,QAAA,EAExCiO;cAAO,GAJHA,OAAO;gBAAAhO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CAAC;YAEb,CAAC,CAAC,eAEFX,OAAA;cACE2G,SAAS,EAAC,gBAAgB;cAC1BgH,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAAC9E,WAAW,GAAG,CAAC,CAAE;cACjD4J,QAAQ,EAAE5J,WAAW,KAAKwE,UAAW;cAAAxI,QAAA,EACtC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENX,OAAA;YAAK2G,SAAS,EAAC,oBAAoB;YAAApG,QAAA,gBACjCP,OAAA;cAAAO,QAAA,EAAM;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7BX,OAAA;cACEiL,KAAK,EAAExG,aAAc;cACrBoJ,QAAQ,EAAGC,CAAC,IAAKvE,oBAAoB,CAACkF,MAAM,CAACX,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAC,CAAE;cAAA1K,QAAA,gBAE9DP,OAAA;gBAAQiL,KAAK,EAAE,CAAE;gBAAA1K,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5BX,OAAA;gBAAQiL,KAAK,EAAE,EAAG;gBAAA1K,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BX,OAAA;gBAAQiL,KAAK,EAAE,EAAG;gBAAA1K,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BX,OAAA;gBAAQiL,KAAK,EAAE,EAAG;gBAAA1K,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDX,OAAA,CAACF,SAAS;UACRiG,eAAe,EAAEA,eAAgB;UACjClC,iBAAiB,EAAEA,iBAAkB;UACrC4F,eAAe,EAAEA,eAAgB;UACjCmB,eAAe,EAAEA,eAAgB;UACjC7I,cAAc,EAAEA,cAAe;UAC/BgB,aAAa,EAAEA,aAAc;UAC7BlB,WAAW,EAAEA,WAAY;UACzByC,WAAW,EAAEA,WAAY;UACzBF,aAAa,EAAEA,aAAc;UAC7BD,UAAU,EAAEA,UAAW;UACvBL,oBAAoB,EAAEA,oBAAqB;UAC3CyB,gBAAgB,EAAEA,gBAAiB;UACnCC,mBAAmB,EAAEA,mBAAoB;UACzCK,uBAAuB,EAAEA,uBAAwB;UACjDd,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7B2G,gBAAgB,EAAEA,gBAAiB;UACnCgB,iBAAiB,EAAEA,iBAAkB;UACrCG,mBAAmB,EAAEA,mBAAoB;UACzCI,aAAa,EAAEA,aAAc;UAC7BxL,IAAI,EAAEA;QAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,EAKTgE,WAAW,GAAG,CAAC,iBACd3E,OAAA;UAAK2G,SAAS,EAAC,sBAAsB;UAAApG,QAAA,gBACnCP,OAAA;YAAK2G,SAAS,EAAC,iBAAiB;YAAApG,QAAA,GAAC,UACvB,EAAC2I,SAAS,EAAC,GAAC,EAACC,OAAO,EAAC,MAAI,EAACxE,WAAW,EAAC,SAChD;UAAA;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENX,OAAA;YAAK2G,SAAS,EAAC,qBAAqB;YAAApG,QAAA,gBAClCP,OAAA;cACE2G,SAAS,EAAC,gBAAgB;cAC1BgH,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAAC9E,WAAW,GAAG,CAAC,CAAE;cACjD4J,QAAQ,EAAE5J,WAAW,KAAK,CAAE;cAAAhE,QAAA,EAC7B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAERyN,KAAK,CAACC,IAAI,CAAC;cAAE3F,MAAM,EAAEM,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEL,UAAU;YAAE,CAAC,EAAE,CAACuF,CAAC,EAAEC,CAAC,KAAK;cACzD,IAAIC,OAAO;cACX,IAAIzF,UAAU,IAAI,CAAC,EAAE;gBACnByF,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAIhK,WAAW,IAAI,CAAC,EAAE;gBAC3BiK,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAIhK,WAAW,IAAIwE,UAAU,GAAG,CAAC,EAAE;gBACxCyF,OAAO,GAAGzF,UAAU,GAAG,CAAC,GAAGwF,CAAC;cAC9B,CAAC,MAAM;gBACLC,OAAO,GAAGjK,WAAW,GAAG,CAAC,GAAGgK,CAAC;cAC/B;cAEA,oBACEvO,OAAA;gBAEE2G,SAAS,EAAG,kBAAiBpC,WAAW,KAAKiK,OAAO,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACvEb,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAACmF,OAAO,CAAE;gBAAAjO,QAAA,EAExCiO;cAAO,GAJHA,OAAO;gBAAAhO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CAAC;YAEb,CAAC,CAAC,eAEFX,OAAA;cACE2G,SAAS,EAAC,gBAAgB;cAC1BgH,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAAC9E,WAAW,GAAG,CAAC,CAAE;cACjD4J,QAAQ,EAAE5J,WAAW,KAAKwE,UAAW;cAAAxI,QAAA,EACtC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENX,OAAA;YAAK2G,SAAS,EAAC,oBAAoB;YAAApG,QAAA,gBACjCP,OAAA;cAAAO,QAAA,EAAM;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7BX,OAAA;cACEiL,KAAK,EAAExG,aAAc;cACrBoJ,QAAQ,EAAGC,CAAC,IAAKvE,oBAAoB,CAACkF,MAAM,CAACX,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAC,CAAE;cAAA1K,QAAA,gBAE9DP,OAAA;gBAAQiL,KAAK,EAAE,CAAE;gBAAA1K,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5BX,OAAA;gBAAQiL,KAAK,EAAE,EAAG;gBAAA1K,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BX,OAAA;gBAAQiL,KAAK,EAAE,EAAG;gBAAA1K,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BX,OAAA;gBAAQiL,KAAK,EAAE,EAAG;gBAAA1K,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,eACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEN;AAACc,EAAA,CAj7BQD,YAAY;EAAA,QACFpC,WAAW,EAC6BQ,WAAW,EACnDT,WAAW;AAAA;AAAAuP,EAAA,GAHrBlN,YAAY;AAm7BrB,eAAeA,YAAY;AAAC,IAAAkN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}