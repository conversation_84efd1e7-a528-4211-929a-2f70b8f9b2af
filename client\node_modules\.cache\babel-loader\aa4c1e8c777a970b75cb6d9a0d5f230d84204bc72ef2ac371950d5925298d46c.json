{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.user && state.user.user) {\n        console.log('✅ Found user in state.user.user');\n        return state.user.user;\n      }\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n  console.log('👤 Current user data:', user);\n  console.log('👤 User name fields:', {\n    name: user === null || user === void 0 ? void 0 : user.name,\n    firstName: user === null || user === void 0 ? void 0 : user.firstName,\n    lastName: user === null || user === void 0 ? void 0 : user.lastName,\n    username: user === null || user === void 0 ? void 0 : user.username,\n    level: user === null || user === void 0 ? void 0 : user.level,\n    class: user === null || user === void 0 ? void 0 : user.class\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    console.log('🔍 CHECKING THUMBNAIL FOR VIDEO:', video.title);\n    console.log('🔍 video.thumbnail (database):', video.thumbnail);\n\n    // PRIORITY 1: Use database thumbnail if it exists and is valid\n    if (video.thumbnail && typeof video.thumbnail === 'string' && video.thumbnail.trim() !== '') {\n      const dbThumbnail = video.thumbnail.trim();\n      console.log(`✅ USING DATABASE THUMBNAIL:`, dbThumbnail);\n      return dbThumbnail;\n    }\n\n    // PRIORITY 2: Check other thumbnail fields as backup\n    const otherThumbnailFields = ['thumbnailUrl', 'image', 'imageUrl', 'poster', 'posterUrl', 'cover', 'coverImage', 'previewImage', 'videoThumbnail', 'thumb', 'preview'];\n    for (const field of otherThumbnailFields) {\n      if (video[field] && typeof video[field] === 'string' && video[field].trim() !== '') {\n        const thumbnailUrl = video[field].trim();\n        console.log(`✅ FOUND BACKUP THUMBNAIL in field \"${field}\":`, thumbnailUrl);\n        return thumbnailUrl;\n      }\n    }\n\n    // PRIORITY 3: Generate YouTube thumbnail if it's a YouTube video\n    if (video.videoID && (video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be') || video.videoID.length === 11)) {\n      let videoId = video.videoID;\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      const youtubeThumb = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n      console.log(`✅ GENERATED YOUTUBE THUMBNAIL:`, youtubeThumb);\n      return youtubeThumb;\n    }\n\n    // PRIORITY 4: Educational video placeholder as final fallback\n    console.log('❌ NO THUMBNAIL FOUND - using educational video placeholder');\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n  const formatTimeAgo = timestamp => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.username) || (user === null || user === void 0 ? void 0 : user.displayName) || 'Student';\n    const fullName = user !== null && user !== void 0 && user.firstName && user !== null && user !== void 0 && user.lastName ? `${user.firstName} ${user.lastName}` : userName;\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user === null || user === void 0 ? void 0 : user.name);\n    console.log('  - user?.firstName:', user === null || user === void 0 ? void 0 : user.firstName);\n    console.log('  - user?.lastName:', user === null || user === void 0 ? void 0 : user.lastName);\n    console.log('  - user?.username:', user === null || user === void 0 ? void 0 : user.username);\n    console.log('  - user?.displayName:', user === null || user === void 0 ? void 0 : user.displayName);\n    console.log('  - Final fullName:', fullName);\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userId: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userRole: user === null || user === void 0 ? void 0 : user.role,\n      isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user === null || user === void 0 ? void 0 : user.firstName,\n        lastName: user === null || user === void 0 ? void 0 : user.lastName,\n        username: user === null || user === void 0 ? void 0 : user.username,\n        email: user === null || user === void 0 ? void 0 : user.email,\n        role: user === null || user === void 0 ? void 0 : user.role,\n        avatar: (user === null || user === void 0 ? void 0 : user.avatar) || (user === null || user === void 0 ? void 0 : user.profilePicture)\n      }\n    };\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend using the correct API\n    try {\n      const videoId = video._id || video.id;\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          videoId: videoId,\n          text: newComment.trim(),\n          userId: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n          author: fullName,\n          avatar: (user === null || user === void 0 ? void 0 : user.avatar) || (user === null || user === void 0 ? void 0 : user.profilePicture) || fullName.charAt(0).toUpperCase(),\n          userLevel: (user === null || user === void 0 ? void 0 : user.level) || 'primary',\n          userClass: (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className)\n        })\n      });\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      const videoId = video._id || video.id;\n      setComments(prev => ({\n        ...prev,\n        [videoId]: [...(prev[videoId] || []), comment]\n      }));\n    }\n    setNewComment('');\n  };\n  const handleLikeComment = commentId => {\n    if (!(user !== null && user !== void 0 && user._id) && !(user !== null && user !== void 0 && user.id) || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    const userId = (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id);\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).map(comment => {\n        if (comment.id === commentId || comment._id === commentId) {\n          var _comment$likedBy;\n          const isLiked = (_comment$likedBy = comment.likedBy) === null || _comment$likedBy === void 0 ? void 0 : _comment$likedBy.includes(userId);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked ? comment.likedBy.filter(id => id !== userId) : [...(comment.likedBy || []), userId]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n  const handleDeleteComment = commentId => {\n    if (currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).filter(comment => comment.id !== commentId && comment._id !== commentId)\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n    setCommentsExpanded(false);\n    setNewComment('');\n    const videoId = video._id || video.id;\n\n    // Comments are now loaded automatically via useEffect\n    // If comments aren't loaded yet, load them now\n    if (!comments[videoId]) {\n      loadCommentsForVideo(videoId);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🎥 Attempting to fetch videos from database...');\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        var _response, _response2;\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n        if ((_response = response) !== null && _response !== void 0 && _response.success && (_response2 = response) !== null && _response2 !== void 0 && _response2.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n          if (videos.length > 0) {\n            var _videos$;\n            console.log('📹 Sample video data:', videos[0]);\n            console.log('🖼️ Sample video thumbnail:', (_videos$ = videos[0]) === null || _videos$ === void 0 ? void 0 : _videos$.thumbnail);\n            console.log('🔍 All video fields:', Object.keys(videos[0]));\n          }\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - imageUrl:', videos[0].imageUrl);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n            console.log('  - thumb:', videos[0].thumb);\n            console.log('  - preview:', videos[0].preview);\n\n            // Check a few more videos to see if any have thumbnails\n            console.log('🔍 CHECKING MORE VIDEOS FOR THUMBNAILS:');\n            videos.slice(0, 5).forEach((video, index) => {\n              console.log(`Video ${index + 1} (${video.title}):`, {\n                thumbnail: video.thumbnail,\n                thumbnailUrl: video.thumbnailUrl,\n                image: video.image,\n                imageUrl: video.imageUrl,\n                poster: video.poster,\n                thumb: video.thumb,\n                preview: video.preview\n              });\n            });\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          var _response3, _response3$data, _response4, _response4$data;\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n          if ((_response3 = response) !== null && _response3 !== void 0 && (_response3$data = _response3.data) !== null && _response3$data !== void 0 && _response3$data.success && (_response4 = response) !== null && _response4 !== void 0 && (_response4$data = _response4.data) !== null && _response4$data !== void 0 && _response4$data.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          var _response5, _response5$data, _response6, _response6$data;\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n          if ((_response5 = response) !== null && _response5 !== void 0 && (_response5$data = _response5.data) !== null && _response5$data !== void 0 && _response5$data.success && (_response6 = response) !== null && _response6 !== void 0 && (_response6$data = _response6.data) !== null && _response6$data !== void 0 && _response6$data.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item => {\n              var _item$title;\n              return item.type === 'video' || item.videoUrl || item.videoID || ((_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.toLowerCase().includes('video'));\n            });\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel || !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass || !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject || !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  // Load comments for all videos when videos are loaded\n  useEffect(() => {\n    if (filteredAndSortedVideos.length > 0) {\n      filteredAndSortedVideos.forEach(video => {\n        const videoId = video._id || video.id;\n        if (videoId && !comments[videoId]) {\n          loadCommentsForVideo(videoId);\n        }\n      });\n    }\n  }, [filteredAndSortedVideos, comments]);\n\n  // Function to load comments for a specific video\n  const loadCommentsForVideo = async videoId => {\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments/${videoId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const commentsData = await response.json();\n        console.log(`✅ Loaded comments for video ${videoId}:`, commentsData);\n        if (commentsData.success && commentsData.data) {\n          const commentsArray = commentsData.data.comments || commentsData.data;\n          if (Array.isArray(commentsArray)) {\n            setComments(prev => ({\n              ...prev,\n              [videoId]: commentsArray\n            }));\n            console.log(`✅ Set ${commentsArray.length} comments for video ${videoId}`);\n          }\n        }\n      }\n    } catch (error) {\n      console.log(`⚠️ Error loading comments for video ${videoId}:`, error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => {\n          var _ref, _ref$charAt;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-item\",\n            children: currentVideoIndex === index ?\n            /*#__PURE__*/\n            /* Video Player - Replaces the thumbnail when playing */\n            _jsxDEV(\"div\", {\n              className: \"inline-video-player\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-style-layout\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-player\",\n                  children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    autoPlay: true,\n                    playsInline: true,\n                    preload: \"metadata\",\n                    width: \"100%\",\n                    height: \"100%\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      backgroundColor: '#000',\n                      objectFit: 'contain'\n                    },\n                    onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                    onCanPlay: () => setVideoError(null),\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 657,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 31\n                    }, this)), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 27\n                  }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                    title: video.title,\n                    frameBorder: \"0\",\n                    allowFullScreen: true,\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      border: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-error\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"error-icon\",\n                      children: \"\\u26A0\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: \"Video Unavailable\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError || \"This video cannot be played at the moment.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"youtube-video-title\",\n                    children: video.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: getSubjectName(video.subject)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 691,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Class \", video.className || video.class]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 692,\n                      columnNumber: 27\n                    }, this), video.level && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 695,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: video.level\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 696,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `youtube-action-btn ${commentsExpanded ? 'active' : ''}`,\n                      onClick: () => setCommentsExpanded(!commentsExpanded),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 705,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Comments\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 706,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDC4D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 709,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Like\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 710,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      onClick: () => setCurrentVideoIndex(null),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2715\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 716,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Close\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 717,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 23\n                }, this), commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-header\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [getCurrentVideoComments().length, \" Comments\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 726,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 725,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comment-input\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment-avatar\",\n                      children: user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: user.profileImage,\n                        alt: \"Profile\",\n                        style: {\n                          width: '100%',\n                          height: '100%',\n                          borderRadius: '50%',\n                          objectFit: 'cover'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 33\n                      }, this) : (_ref = (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.username) || 'Student') === null || _ref === void 0 ? void 0 : (_ref$charAt = _ref.charAt(0)) === null || _ref$charAt === void 0 ? void 0 : _ref$charAt.toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 731,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                        className: \"youtube-comment-input-field\",\n                        value: newComment,\n                        onChange: e => setNewComment(e.target.value),\n                        placeholder: \"Add a comment...\",\n                        rows: \"1\",\n                        style: {\n                          minHeight: '20px',\n                          resize: 'none',\n                          overflow: 'hidden'\n                        },\n                        onInput: e => {\n                          e.target.style.height = 'auto';\n                          e.target.style.height = e.target.scrollHeight + 'px';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 31\n                      }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-actions\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn cancel\",\n                          onClick: () => setNewComment(''),\n                          children: \"Cancel\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 761,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn submit\",\n                          onClick: handleAddComment,\n                          disabled: !newComment.trim(),\n                          children: \"Comment\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 767,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 760,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-list\",\n                    children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center',\n                        padding: '40px 0',\n                        color: '#606060'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '48px',\n                          marginBottom: '16px'\n                        },\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 783,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No comments yet. Be the first to share your thoughts!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 784,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 782,\n                      columnNumber: 31\n                    }, this) : getCurrentVideoComments().map(comment => {\n                      var _comment$author, _comment$author$charA, _comment$user, _comment$likedBy2, _comment$likedBy3;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-avatar\",\n                          children: [comment.avatar && comment.avatar.includes('http') ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: comment.avatar,\n                            alt: \"Profile\",\n                            style: {\n                              width: '100%',\n                              height: '100%',\n                              borderRadius: '50%',\n                              objectFit: 'cover'\n                            },\n                            onError: e => {\n                              e.target.style.display = 'none';\n                              e.target.nextSibling.style.display = 'flex';\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 791,\n                            columnNumber: 39\n                          }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: comment.avatar && comment.avatar.includes('http') ? 'none' : 'flex',\n                              width: '100%',\n                              height: '100%',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              fontSize: '16px',\n                              fontWeight: '600'\n                            },\n                            children: comment.avatar && !comment.avatar.includes('http') ? comment.avatar : ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 801,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 789,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-content\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-author\",\n                              children: comment.author || 'Anonymous'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 815,\n                              columnNumber: 39\n                            }, this), (comment.userRole === 'admin' || comment.isAdmin || ((_comment$user = comment.user) === null || _comment$user === void 0 ? void 0 : _comment$user.isAdmin)) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                              style: {\n                                color: '#1d9bf0',\n                                fontSize: '12px',\n                                marginLeft: '4px'\n                              },\n                              title: \"Verified Admin\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 819,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-time\",\n                              children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 821,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 814,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-text\",\n                            children: comment.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 825,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-actions\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => handleLikeComment(comment._id || comment.id),\n                              className: `youtube-comment-action ${(_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                children: (_comment$likedBy3 = comment.likedBy) !== null && _comment$likedBy3 !== void 0 && _comment$likedBy3.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 833,\n                                columnNumber: 41\n                              }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: comment.likes\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 834,\n                                columnNumber: 63\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 829,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: \"Reply\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 836,\n                              columnNumber: 39\n                            }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                children: \"Edit\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 841,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                onClick: () => {\n                                  if (window.confirm('Are you sure you want to delete this comment?')) {\n                                    handleDeleteComment(comment._id || comment.id);\n                                  }\n                                },\n                                children: \"Delete\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 844,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 828,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 813,\n                          columnNumber: 35\n                        }, this)]\n                      }, comment._id || comment.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 788,\n                        columnNumber: 33\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 19\n            }, this) :\n            /*#__PURE__*/\n            /* Video Card - Shows thumbnail when not playing */\n            _jsxDEV(\"div\", {\n              className: \"video-card\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-thumbnail\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getThumbnailUrl(video),\n                  alt: video.title,\n                  className: \"thumbnail-image\",\n                  loading: \"lazy\",\n                  onError: e => {\n                    console.log('❌ Database thumbnail failed to load:', e.target.src);\n                    console.log('🔄 Trying fallback options...');\n\n                    // If database thumbnail failed, try YouTube thumbnail as backup\n                    if (video.videoID && (video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be') || video.videoID.length === 11)) {\n                      let videoId = video.videoID;\n                      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                      videoId = match ? match[1] : videoId;\n                      const youtubeThumb = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n\n                      // Only try YouTube thumbnail if we haven't already tried it\n                      if (e.target.src !== youtubeThumb && !e.target.src.includes('youtube.com')) {\n                        console.log('🔄 Trying YouTube thumbnail fallback:', youtubeThumb);\n                        e.target.src = youtubeThumb;\n                        return;\n                      }\n                    }\n\n                    // Use educational video placeholder as final fallback\n                    console.log('🎨 Using educational placeholder as final fallback');\n                    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 870,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"play-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                    className: \"play-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 900,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-duration\",\n                  children: video.duration || \"Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 23\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 907,\n                    columnNumber: 27\n                  }, this), \"CC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 906,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 916,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 917,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-tags\",\n                  children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"topic-tag\",\n                    children: video.topic\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 924,\n                    columnNumber: 41\n                  }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"shared-tag\",\n                    children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 926,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 913,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 942,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 945,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 613,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 563,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"mZsbVeYhozEPEBiF+y9U7SXF/j0=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "getAllVideos", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoLessons", "_s", "user", "state", "console", "log", "Object", "keys", "users", "auth", "storedUser", "localStorage", "getItem", "JSON", "parse", "e", "error", "name", "firstName", "lastName", "username", "level", "class", "videos", "setVideos", "loading", "setLoading", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "comments", "setComments", "newComment", "setNewComment", "commentsExpanded", "setCommentsExpanded", "replyingTo", "setReplyingTo", "isKiswahili", "getThumbnailUrl", "video", "title", "thumbnail", "trim", "db<PERSON><PERSON><PERSON><PERSON>", "other<PERSON><PERSON><PERSON><PERSON><PERSON>ields", "field", "thumbnailUrl", "videoID", "includes", "length", "videoId", "match", "youtubeThumb", "getSubjectName", "subject", "subjectMap", "getCurrentVideoComments", "filteredAndSortedVideos", "_id", "id", "formatTimeAgo", "timestamp", "now", "Date", "time", "diffInSeconds", "Math", "floor", "handleAddComment", "userName", "displayName", "fullName", "comment", "toString", "text", "author", "userId", "userRole", "role", "isAdmin", "toISOString", "createdAt", "likes", "<PERSON><PERSON><PERSON>", "userProfile", "email", "avatar", "profilePicture", "response", "fetch", "process", "env", "REACT_APP_API_URL", "method", "headers", "body", "stringify", "char<PERSON>t", "toUpperCase", "userLevel", "userClass", "className", "ok", "savedComment", "json", "prev", "data", "handleLikeComment", "commentId", "map", "_comment$likedBy", "isLiked", "filter", "handleDeleteComment", "handleShowVideo", "index", "loadCommentsForVideo", "videoUrl", "signedVideoUrl", "warn", "fetchVideos", "_response", "_response2", "success", "_videos$", "image", "imageUrl", "poster", "posterUrl", "cover", "coverImage", "previewImage", "videoThumbnail", "thumb", "preview", "slice", "for<PERSON>ach", "message", "_response3", "_response3$data", "_response4", "_response4$data", "filters", "type", "_response5", "_response5$data", "_response6", "_response6$data", "allData", "item", "_item$title", "toLowerCase", "filtered", "matchesLevel", "matchesClass", "matchesSubject", "err", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "topic", "sort", "a", "b", "commentsData", "commentsA<PERSON>y", "Array", "isArray", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "target", "onClick", "_ref", "_ref$charAt", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "style", "backgroundColor", "objectFit", "onError", "onCanPlay", "crossOrigin", "src", "subtitles", "subtitle", "kind", "url", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "profileImage", "alt", "borderRadius", "flex", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "disabled", "textAlign", "padding", "color", "fontSize", "marginBottom", "_comment$author", "_comment$author$charA", "_comment$user", "_comment$likedBy2", "_comment$likedBy3", "display", "nextS<PERSON>ling", "alignItems", "justifyContent", "fontWeight", "marginLeft", "window", "confirm", "duration", "sharedFromClass", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.user && state.user.user) {\n        console.log('✅ Found user in state.user.user');\n        return state.user.user;\n      }\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  console.log('👤 Current user data:', user);\n  console.log('👤 User name fields:', {\n    name: user?.name,\n    firstName: user?.firstName,\n    lastName: user?.lastName,\n    username: user?.username,\n    level: user?.level,\n    class: user?.class\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    console.log('🔍 CHECKING THUMBNAIL FOR VIDEO:', video.title);\n    console.log('🔍 video.thumbnail (database):', video.thumbnail);\n\n    // PRIORITY 1: Use database thumbnail if it exists and is valid\n    if (video.thumbnail && typeof video.thumbnail === 'string' && video.thumbnail.trim() !== '') {\n      const dbThumbnail = video.thumbnail.trim();\n      console.log(`✅ USING DATABASE THUMBNAIL:`, dbThumbnail);\n      return dbThumbnail;\n    }\n\n    // PRIORITY 2: Check other thumbnail fields as backup\n    const otherThumbnailFields = [\n      'thumbnailUrl',\n      'image',\n      'imageUrl',\n      'poster',\n      'posterUrl',\n      'cover',\n      'coverImage',\n      'previewImage',\n      'videoThumbnail',\n      'thumb',\n      'preview'\n    ];\n\n    for (const field of otherThumbnailFields) {\n      if (video[field] && typeof video[field] === 'string' && video[field].trim() !== '') {\n        const thumbnailUrl = video[field].trim();\n        console.log(`✅ FOUND BACKUP THUMBNAIL in field \"${field}\":`, thumbnailUrl);\n        return thumbnailUrl;\n      }\n    }\n\n    // PRIORITY 3: Generate YouTube thumbnail if it's a YouTube video\n    if (video.videoID && (video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be') || video.videoID.length === 11)) {\n      let videoId = video.videoID;\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      const youtubeThumb = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n      console.log(`✅ GENERATED YOUTUBE THUMBNAIL:`, youtubeThumb);\n      return youtubeThumb;\n    }\n\n    // PRIORITY 4: Educational video placeholder as final fallback\n    console.log('❌ NO THUMBNAIL FOUND - using educational video placeholder');\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = user?.name || user?.firstName || user?.username || user?.displayName || 'Student';\n    const fullName = user?.firstName && user?.lastName\n      ? `${user.firstName} ${user.lastName}`\n      : userName;\n\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user?.name);\n    console.log('  - user?.firstName:', user?.firstName);\n    console.log('  - user?.lastName:', user?.lastName);\n    console.log('  - user?.username:', user?.username);\n    console.log('  - user?.displayName:', user?.displayName);\n    console.log('  - Final fullName:', fullName);\n\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: user?._id || user?.id,\n      userId: user?._id || user?.id,\n      userRole: user?.role,\n      isAdmin: user?.role === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user?.firstName,\n        lastName: user?.lastName,\n        username: user?.username,\n        email: user?.email,\n        role: user?.role,\n        avatar: user?.avatar || user?.profilePicture\n      }\n    };\n\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend using the correct API\n    try {\n      const videoId = video._id || video.id;\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n        },\n        body: JSON.stringify({\n          videoId: videoId,\n          text: newComment.trim(),\n          userId: user?._id || user?.id,\n          author: fullName,\n          avatar: user?.avatar || user?.profilePicture || fullName.charAt(0).toUpperCase(),\n          userLevel: user?.level || 'primary',\n          userClass: user?.class || user?.className\n        })\n      });\n\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      const videoId = video._id || video.id;\n      setComments(prev => ({\n        ...prev,\n        [videoId]: [...(prev[videoId] || []), comment]\n      }));\n    }\n\n    setNewComment('');\n  };\n\n  const handleLikeComment = (commentId) => {\n    if (!user?._id && !user?.id || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n    const userId = user?._id || user?.id;\n\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).map(comment => {\n        if (comment.id === commentId || comment._id === commentId) {\n          const isLiked = comment.likedBy?.includes(userId);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked\n              ? comment.likedBy.filter(id => id !== userId)\n              : [...(comment.likedBy || []), userId]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n\n  const handleDeleteComment = (commentId) => {\n    if (currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).filter(comment =>\n        comment.id !== commentId && comment._id !== commentId\n      )\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n    setCommentsExpanded(false);\n    setNewComment('');\n\n    const videoId = video._id || video.id;\n\n    // Comments are now loaded automatically via useEffect\n    // If comments aren't loaded yet, load them now\n    if (!comments[videoId]) {\n      loadCommentsForVideo(videoId);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🎥 Attempting to fetch videos from database...');\n\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n\n        if (response?.success && response?.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n          if (videos.length > 0) {\n            console.log('📹 Sample video data:', videos[0]);\n            console.log('🖼️ Sample video thumbnail:', videos[0]?.thumbnail);\n            console.log('🔍 All video fields:', Object.keys(videos[0]));\n          }\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - imageUrl:', videos[0].imageUrl);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n            console.log('  - thumb:', videos[0].thumb);\n            console.log('  - preview:', videos[0].preview);\n\n            // Check a few more videos to see if any have thumbnails\n            console.log('🔍 CHECKING MORE VIDEOS FOR THUMBNAILS:');\n            videos.slice(0, 5).forEach((video, index) => {\n              console.log(`Video ${index + 1} (${video.title}):`, {\n                thumbnail: video.thumbnail,\n                thumbnailUrl: video.thumbnailUrl,\n                image: video.image,\n                imageUrl: video.imageUrl,\n                poster: video.poster,\n                thumb: video.thumb,\n                preview: video.preview\n              });\n            });\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item =>\n              item.type === 'video' ||\n              item.videoUrl ||\n              item.videoID ||\n              item.title?.toLowerCase().includes('video')\n            );\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' ||\n                              video.level === selectedLevel ||\n                              !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' ||\n                              video.className === selectedClass ||\n                              video.class === selectedClass ||\n                              !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' ||\n                                video.subject === selectedSubject ||\n                                !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  // Load comments for all videos when videos are loaded\n  useEffect(() => {\n    if (filteredAndSortedVideos.length > 0) {\n      filteredAndSortedVideos.forEach(video => {\n        const videoId = video._id || video.id;\n        if (videoId && !comments[videoId]) {\n          loadCommentsForVideo(videoId);\n        }\n      });\n    }\n  }, [filteredAndSortedVideos, comments]);\n\n  // Function to load comments for a specific video\n  const loadCommentsForVideo = async (videoId) => {\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments/${videoId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n        },\n      });\n\n      if (response.ok) {\n        const commentsData = await response.json();\n        console.log(`✅ Loaded comments for video ${videoId}:`, commentsData);\n\n        if (commentsData.success && commentsData.data) {\n          const commentsArray = commentsData.data.comments || commentsData.data;\n          if (Array.isArray(commentsArray)) {\n            setComments(prev => ({\n              ...prev,\n              [videoId]: commentsArray\n            }));\n            console.log(`✅ Set ${commentsArray.length} comments for video ${videoId}`);\n          }\n        }\n      }\n    } catch (error) {\n      console.log(`⚠️ Error loading comments for video ${videoId}:`, error);\n    }\n  };\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {currentVideoIndex === index ? (\n                  /* Video Player - Replaces the thumbnail when playing */\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                          {video.level && (\n                            <>\n                              <span>•</span>\n                              <span>{video.level}</span>\n                            </>\n                          )}\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className={`youtube-action-btn ${commentsExpanded ? 'active' : ''}`}\n                            onClick={() => setCommentsExpanded(!commentsExpanded)}\n                          >\n                            <span>💬</span>\n                            <span>Comments</span>\n                          </button>\n                          <button className=\"youtube-action-btn\">\n                            <span>👍</span>\n                            <span>Like</span>\n                          </button>\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {commentsExpanded && (\n                        <div className=\"youtube-comments-section\">\n                          <div className=\"youtube-comments-header\">\n                            <span>{getCurrentVideoComments().length} Comments</span>\n                          </div>\n\n                          {/* Add Comment */}\n                          <div className=\"youtube-comment-input\">\n                            <div className=\"youtube-comment-avatar\">\n                              {user?.profileImage ? (\n                                <img\n                                  src={user.profileImage}\n                                  alt=\"Profile\"\n                                  style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }}\n                                />\n                              ) : (\n                                (user?.firstName || user?.name || user?.username || 'Student')?.charAt(0)?.toUpperCase()\n                              )}\n                            </div>\n                            <div style={{ flex: 1 }}>\n                              <textarea\n                                className=\"youtube-comment-input-field\"\n                                value={newComment}\n                                onChange={(e) => setNewComment(e.target.value)}\n                                placeholder=\"Add a comment...\"\n                                rows=\"1\"\n                                style={{\n                                  minHeight: '20px',\n                                  resize: 'none',\n                                  overflow: 'hidden'\n                                }}\n                                onInput={(e) => {\n                                  e.target.style.height = 'auto';\n                                  e.target.style.height = e.target.scrollHeight + 'px';\n                                }}\n                              />\n                              {newComment.trim() && (\n                                <div className=\"youtube-comment-actions\">\n                                  <button\n                                    className=\"youtube-comment-btn cancel\"\n                                    onClick={() => setNewComment('')}\n                                  >\n                                    Cancel\n                                  </button>\n                                  <button\n                                    className=\"youtube-comment-btn submit\"\n                                    onClick={handleAddComment}\n                                    disabled={!newComment.trim()}\n                                  >\n                                    Comment\n                                  </button>\n                                </div>\n                              )}\n                            </div>\n                          </div>\n\n                          {/* Comments List */}\n                          <div className=\"youtube-comments-list\">\n                            {getCurrentVideoComments().length === 0 ? (\n                              <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                                <p>No comments yet. Be the first to share your thoughts!</p>\n                              </div>\n                            ) : (\n                              getCurrentVideoComments().map((comment) => (\n                                <div key={comment._id || comment.id} className=\"youtube-comment\">\n                                  <div className=\"youtube-comment-avatar\">\n                                    {comment.avatar && comment.avatar.includes('http') ? (\n                                      <img\n                                        src={comment.avatar}\n                                        alt=\"Profile\"\n                                        style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }}\n                                        onError={(e) => {\n                                          e.target.style.display = 'none';\n                                          e.target.nextSibling.style.display = 'flex';\n                                        }}\n                                      />\n                                    ) : null}\n                                    <div style={{\n                                      display: comment.avatar && comment.avatar.includes('http') ? 'none' : 'flex',\n                                      width: '100%',\n                                      height: '100%',\n                                      alignItems: 'center',\n                                      justifyContent: 'center',\n                                      fontSize: '16px',\n                                      fontWeight: '600'\n                                    }}>\n                                      {comment.avatar && !comment.avatar.includes('http') ? comment.avatar : comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                    </div>\n                                  </div>\n                                  <div className=\"youtube-comment-content\">\n                                    <div className=\"youtube-comment-header\">\n                                      <span className=\"youtube-comment-author\">\n                                        {comment.author || 'Anonymous'}\n                                      </span>\n                                      {(comment.userRole === 'admin' || comment.isAdmin || comment.user?.isAdmin) && (\n                                        <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title=\"Verified Admin\" />\n                                      )}\n                                      <span className=\"youtube-comment-time\">\n                                        {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                      </span>\n                                    </div>\n                                    <div className=\"youtube-comment-text\">\n                                      {comment.text}\n                                    </div>\n                                    <div className=\"youtube-comment-actions\">\n                                      <button\n                                        onClick={() => handleLikeComment(comment._id || comment.id)}\n                                        className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                      >\n                                        <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                        {comment.likes > 0 && <span>{comment.likes}</span>}\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        Reply\n                                      </button>\n                                      {comment.user === user?._id && (\n                                        <>\n                                          <button className=\"youtube-comment-action\">\n                                            Edit\n                                          </button>\n                                          <button\n                                            className=\"youtube-comment-action\"\n                                            onClick={() => {\n                                              if (window.confirm('Are you sure you want to delete this comment?')) {\n                                                handleDeleteComment(comment._id || comment.id);\n                                              }\n                                            }}\n                                          >\n                                            Delete\n                                          </button>\n                                        </>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              ))\n                            )}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                ) : (\n                  /* Video Card - Shows thumbnail when not playing */\n                  <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                    <div className=\"video-card-thumbnail\">\n                      <img\n                        src={getThumbnailUrl(video)}\n                        alt={video.title}\n                        className=\"thumbnail-image\"\n                        loading=\"lazy\"\n                        onError={(e) => {\n                          console.log('❌ Database thumbnail failed to load:', e.target.src);\n                          console.log('🔄 Trying fallback options...');\n\n                          // If database thumbnail failed, try YouTube thumbnail as backup\n                          if (video.videoID && (video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be') || video.videoID.length === 11)) {\n                            let videoId = video.videoID;\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                            const youtubeThumb = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n\n                            // Only try YouTube thumbnail if we haven't already tried it\n                            if (e.target.src !== youtubeThumb && !e.target.src.includes('youtube.com')) {\n                              console.log('🔄 Trying YouTube thumbnail fallback:', youtubeThumb);\n                              e.target.src = youtubeThumb;\n                              return;\n                            }\n                          }\n\n                          // Use educational video placeholder as final fallback\n                          console.log('🎨 Using educational placeholder as final fallback');\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n                        }}\n                      />\n                      <div className=\"play-overlay\">\n                        <FaPlayCircle className=\"play-icon\" />\n                      </div>\n                      <div className=\"video-duration\">\n                        {video.duration || \"Video\"}\n                      </div>\n                      {video.subtitles && video.subtitles.length > 0 && (\n                        <div className=\"subtitle-badge\">\n                          <TbInfoCircle />\n                          CC\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"video-card-content\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                        <span className=\"video-class\">\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                            : `Form ${video.className || video.class}`}\n                        </span>\n                      </div>\n                      <div className=\"video-tags\">\n                        {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                        {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                          <span className=\"shared-tag\">\n                            {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                            {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                              ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                              : `Form ${video.sharedFromClass}`}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,IAAI,GAAGd,WAAW,CAACe,KAAK,IAAI;IAChC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,KAAK,CAAC;MACpDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,MAAM,CAACC,IAAI,CAACJ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAIA,KAAK,IAAIA,KAAK,CAACD,IAAI,IAAIC,KAAK,CAACD,IAAI,CAACA,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACD,IAAI,CAACA,IAAI;MACxB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACN,IAAI,EAAE;QAC5CE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,OAAOF,KAAK,CAACK,KAAK,CAACN,IAAI;MACzB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAACP,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACM,IAAI,CAACP,IAAI;MACxB;MAEAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMK,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACd,IAAI;UACFN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,OAAOQ,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;QAC/B,CAAC,CAAC,OAAOK,CAAC,EAAE;UACVX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC,CAAC;EAEFZ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,IAAI,CAAC;EAC1CE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;IAClCY,IAAI,EAAEf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI;IAChBC,SAAS,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS;IAC1BC,QAAQ,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,QAAQ;IACxBC,QAAQ,EAAElB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ;IACxBC,KAAK,EAAEnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,KAAK;IAClBC,KAAK,EAAEpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,KAAK,EAAEW,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC0D,QAAQ,EAAEC,WAAW,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMkE,WAAW,GAAGpB,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMqB,eAAe,GAAIC,KAAK,IAAK;IACjChD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE+C,KAAK,CAACC,KAAK,CAAC;IAC5DjD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE+C,KAAK,CAACE,SAAS,CAAC;;IAE9D;IACA,IAAIF,KAAK,CAACE,SAAS,IAAI,OAAOF,KAAK,CAACE,SAAS,KAAK,QAAQ,IAAIF,KAAK,CAACE,SAAS,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3F,MAAMC,WAAW,GAAGJ,KAAK,CAACE,SAAS,CAACC,IAAI,CAAC,CAAC;MAC1CnD,OAAO,CAACC,GAAG,CAAE,6BAA4B,EAAEmD,WAAW,CAAC;MACvD,OAAOA,WAAW;IACpB;;IAEA;IACA,MAAMC,oBAAoB,GAAG,CAC3B,cAAc,EACd,OAAO,EACP,UAAU,EACV,QAAQ,EACR,WAAW,EACX,OAAO,EACP,YAAY,EACZ,cAAc,EACd,gBAAgB,EAChB,OAAO,EACP,SAAS,CACV;IAED,KAAK,MAAMC,KAAK,IAAID,oBAAoB,EAAE;MACxC,IAAIL,KAAK,CAACM,KAAK,CAAC,IAAI,OAAON,KAAK,CAACM,KAAK,CAAC,KAAK,QAAQ,IAAIN,KAAK,CAACM,KAAK,CAAC,CAACH,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAClF,MAAMI,YAAY,GAAGP,KAAK,CAACM,KAAK,CAAC,CAACH,IAAI,CAAC,CAAC;QACxCnD,OAAO,CAACC,GAAG,CAAE,sCAAqCqD,KAAM,IAAG,EAAEC,YAAY,CAAC;QAC1E,OAAOA,YAAY;MACrB;IACF;;IAEA;IACA,IAAIP,KAAK,CAACQ,OAAO,KAAKR,KAAK,CAACQ,OAAO,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIT,KAAK,CAACQ,OAAO,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAIT,KAAK,CAACQ,OAAO,CAACE,MAAM,KAAK,EAAE,CAAC,EAAE;MACjI,IAAIC,OAAO,GAAGX,KAAK,CAACQ,OAAO;MAC3B,MAAMI,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;MACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACpC,MAAME,YAAY,GAAI,8BAA6BF,OAAQ,gBAAe;MAC1E3D,OAAO,CAACC,GAAG,CAAE,gCAA+B,EAAE4D,YAAY,CAAC;MAC3D,OAAOA,YAAY;IACrB;;IAEA;IACA7D,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzE,OAAO,4cAA4c;EACrd,CAAC;EAED,MAAM6D,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAElB,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOkB,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIjC,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAMgB,KAAK,GAAGkB,uBAAuB,CAAClC,iBAAiB,CAAC;IACxD,MAAM2B,OAAO,GAAG,CAAAX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmB,GAAG,MAAInB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoB,EAAE;IACvCpE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE0D,OAAO,CAAC;IACzD3D,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEqC,QAAQ,CAAC;IAC/C,OAAOA,QAAQ,CAACqB,OAAO,CAAC,IAAI,EAAE;EAChC,CAAC;EAED,MAAMU,aAAa,GAAIC,SAAS,IAAK;IACnC,IAAI,CAACA,SAAS,EAAE,OAAO,UAAU;IACjC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,SAAS,CAAC;IAChC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGE,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAE,cAAa;IAChF,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAE,YAAW;IACjF,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAE,WAAU;EACxD,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACrC,UAAU,CAACW,IAAI,CAAC,CAAC,IAAInB,iBAAiB,KAAK,IAAI,EAAE;IAEtD,MAAMgB,KAAK,GAAGkB,uBAAuB,CAAClC,iBAAiB,CAAC;IAExDhC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,IAAI,CAAC;;IAEtD;IACA,MAAMgF,QAAQ,GAAG,CAAAhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS,MAAIhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ,MAAIlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiF,WAAW,KAAI,SAAS;IAClG,MAAMC,QAAQ,GAAGlF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgB,SAAS,IAAIhB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiB,QAAQ,GAC7C,GAAEjB,IAAI,CAACgB,SAAU,IAAGhB,IAAI,CAACiB,QAAS,EAAC,GACpC+D,QAAQ;IAEZ9E,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvCD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI,CAAC;IAC1Cb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS,CAAC;IACpDd,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,QAAQ,CAAC;IAClDf,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ,CAAC;IAClDhB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiF,WAAW,CAAC;IACxD/E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+E,QAAQ,CAAC;IAE5C,MAAMC,OAAO,GAAG;MACdb,EAAE,EAAEI,IAAI,CAACD,GAAG,CAAC,CAAC,CAACW,QAAQ,CAAC,CAAC;MACzBC,IAAI,EAAE3C,UAAU,CAACW,IAAI,CAAC,CAAC;MACvBiC,MAAM,EAAEJ,QAAQ;MAChBlF,IAAI,EAAE,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,GAAG,MAAIrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,EAAE;MAC3BiB,MAAM,EAAE,CAAAvF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,GAAG,MAAIrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,EAAE;MAC7BkB,QAAQ,EAAExF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyF,IAAI;MACpBC,OAAO,EAAE,CAAA1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyF,IAAI,MAAK,OAAO;MAC/BjB,SAAS,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;MACnCE,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE,EAAE;MACX;MACAC,WAAW,EAAE;QACXhF,IAAI,EAAEmE,QAAQ;QACdlE,SAAS,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS;QAC1BC,QAAQ,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,QAAQ;QACxBC,QAAQ,EAAElB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ;QACxB8E,KAAK,EAAEhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,KAAK;QAClBP,IAAI,EAAEzF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyF,IAAI;QAChBQ,MAAM,EAAE,CAAAjG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,MAAM,MAAIjG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,cAAc;MAC9C;IACF,CAAC;IAEDhG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgF,OAAO,CAAC;;IAElD;IACA,IAAI;MACF,MAAMtB,OAAO,GAAGX,KAAK,CAACmB,GAAG,IAAInB,KAAK,CAACoB,EAAE;MACrC,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,qBAAoB,EAAE;QAC7GC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAAShG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDgG,IAAI,EAAE/F,IAAI,CAACgG,SAAS,CAAC;UACnB9C,OAAO,EAAEA,OAAO;UAChBwB,IAAI,EAAE3C,UAAU,CAACW,IAAI,CAAC,CAAC;UACvBkC,MAAM,EAAE,CAAAvF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,GAAG,MAAIrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,EAAE;UAC7BgB,MAAM,EAAEJ,QAAQ;UAChBe,MAAM,EAAE,CAAAjG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,MAAM,MAAIjG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,cAAc,KAAIhB,QAAQ,CAAC0B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAChFC,SAAS,EAAE,CAAA9G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,KAAK,KAAI,SAAS;UACnC4F,SAAS,EAAE,CAAA/G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,MAAIpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgH,SAAS;QAC3C,CAAC;MACH,CAAC,CAAC;MAEF,IAAIb,QAAQ,CAACc,EAAE,EAAE;QACf,MAAMC,YAAY,GAAG,MAAMf,QAAQ,CAACgB,IAAI,CAAC,CAAC;QAC1CjH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+G,YAAY,CAAC;;QAExD;QACAzE,WAAW,CAAC2E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAACvD,OAAO,GAAG,CAAC,IAAIuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,CAAC,EAAEqD,YAAY,CAACG,IAAI,IAAIH,YAAY;QACzE,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLhH,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D;QACAsC,WAAW,CAAC2E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAACvD,OAAO,GAAG,CAAC,IAAIuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,CAAC,EAAEsB,OAAO;QAC/C,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEW,KAAK,CAAC;MAC5D;MACA,MAAM+C,OAAO,GAAGX,KAAK,CAACmB,GAAG,IAAInB,KAAK,CAACoB,EAAE;MACrC7B,WAAW,CAAC2E,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACvD,OAAO,GAAG,CAAC,IAAIuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,CAAC,EAAEsB,OAAO;MAC/C,CAAC,CAAC,CAAC;IACL;IAEAxC,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAM2E,iBAAiB,GAAIC,SAAS,IAAK;IACvC,IAAI,EAACvH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqE,GAAG,KAAI,EAACrE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsE,EAAE,KAAIpC,iBAAiB,KAAK,IAAI,EAAE;IAE3D,MAAMgB,KAAK,GAAGkB,uBAAuB,CAAClC,iBAAiB,CAAC;IACxD,MAAM2B,OAAO,GAAG,CAAAX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmB,GAAG,MAAInB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoB,EAAE;IACvC,MAAMiB,MAAM,GAAG,CAAAvF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,GAAG,MAAIrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,EAAE;IAEpC7B,WAAW,CAAC2E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACvD,OAAO,GAAG,CAACuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,EAAE2D,GAAG,CAACrC,OAAO,IAAI;QAC9C,IAAIA,OAAO,CAACb,EAAE,KAAKiD,SAAS,IAAIpC,OAAO,CAACd,GAAG,KAAKkD,SAAS,EAAE;UAAA,IAAAE,gBAAA;UACzD,MAAMC,OAAO,IAAAD,gBAAA,GAAGtC,OAAO,CAACW,OAAO,cAAA2B,gBAAA,uBAAfA,gBAAA,CAAiB9D,QAAQ,CAAC4B,MAAM,CAAC;UACjD,OAAO;YACL,GAAGJ,OAAO;YACVU,KAAK,EAAE6B,OAAO,GAAGvC,OAAO,CAACU,KAAK,GAAG,CAAC,GAAGV,OAAO,CAACU,KAAK,GAAG,CAAC;YACtDC,OAAO,EAAE4B,OAAO,GACZvC,OAAO,CAACW,OAAO,CAAC6B,MAAM,CAACrD,EAAE,IAAIA,EAAE,KAAKiB,MAAM,CAAC,GAC3C,CAAC,IAAIJ,OAAO,CAACW,OAAO,IAAI,EAAE,CAAC,EAAEP,MAAM;UACzC,CAAC;QACH;QACA,OAAOJ,OAAO;MAChB,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMyC,mBAAmB,GAAIL,SAAS,IAAK;IACzC,IAAIrF,iBAAiB,KAAK,IAAI,EAAE;IAEhC,MAAMgB,KAAK,GAAGkB,uBAAuB,CAAClC,iBAAiB,CAAC;IACxD,MAAM2B,OAAO,GAAG,CAAAX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmB,GAAG,MAAInB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoB,EAAE;IAEvC7B,WAAW,CAAC2E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACvD,OAAO,GAAG,CAACuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,EAAE8D,MAAM,CAACxC,OAAO,IAC7CA,OAAO,CAACb,EAAE,KAAKiD,SAAS,IAAIpC,OAAO,CAACd,GAAG,KAAKkD,SAC9C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAM5E,KAAK,GAAGkB,uBAAuB,CAAC0D,KAAK,CAAC;IAC5C3F,oBAAoB,CAAC2F,KAAK,CAAC;IAC3BvF,aAAa,CAAC,IAAI,CAAC;IACnBM,mBAAmB,CAAC,KAAK,CAAC;IAC1BF,aAAa,CAAC,EAAE,CAAC;IAEjB,MAAMkB,OAAO,GAAGX,KAAK,CAACmB,GAAG,IAAInB,KAAK,CAACoB,EAAE;;IAErC;IACA;IACA,IAAI,CAAC9B,QAAQ,CAACqB,OAAO,CAAC,EAAE;MACtBkE,oBAAoB,CAAClE,OAAO,CAAC;IAC/B;;IAEA;IACA,IAAIX,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE8E,QAAQ,KAAK9E,KAAK,CAAC8E,QAAQ,CAACrE,QAAQ,CAAC,eAAe,CAAC,IAAIT,KAAK,CAAC8E,QAAQ,CAACrE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAT,KAAK,CAAC+E,cAAc,GAAG/E,KAAK,CAAC8E,QAAQ;MACvC,CAAC,CAAC,OAAOlH,KAAK,EAAE;QACdZ,OAAO,CAACgI,IAAI,CAAC,8CAA8C,CAAC;QAC5DhF,KAAK,CAAC+E,cAAc,GAAG/E,KAAK,CAAC8E,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGlJ,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFuC,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MAEdvB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAIgG,QAAQ,GAAG,IAAI;MACnB,IAAI9E,MAAM,GAAG,EAAE;;MAEf;MACA,IAAI;QAAA,IAAA+G,SAAA,EAAAC,UAAA;QACFnI,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjDgG,QAAQ,GAAG,MAAM1G,YAAY,CAAC,CAAC;QAC/BS,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgG,QAAQ,CAAC;QAE/C,IAAI,CAAAiC,SAAA,GAAAjC,QAAQ,cAAAiC,SAAA,eAARA,SAAA,CAAUE,OAAO,KAAAD,UAAA,GAAIlC,QAAQ,cAAAkC,UAAA,eAARA,UAAA,CAAUhB,IAAI,EAAE;UACvChG,MAAM,GAAG8E,QAAQ,CAACkB,IAAI;UACtBnH,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEkB,MAAM,CAACuC,MAAM,CAAC;UAC7E,IAAIvC,MAAM,CAACuC,MAAM,GAAG,CAAC,EAAE;YAAA,IAAA2E,QAAA;YACrBrI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/CnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,GAAAoI,QAAA,GAAElH,MAAM,CAAC,CAAC,CAAC,cAAAkH,QAAA,uBAATA,QAAA,CAAWnF,SAAS,CAAC;YAChElD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,MAAM,CAACC,IAAI,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7D;;UAEA;UACA,IAAIA,MAAM,CAACuC,MAAM,GAAG,CAAC,EAAE;YACrB1D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC;YACnDnB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,MAAM,CAACC,IAAI,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3DnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;YACzCD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC+B,SAAS,CAAC;YAClDlD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACoC,YAAY,CAAC;YACxDvD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACmH,KAAK,CAAC;YAC1CtI,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACoH,QAAQ,CAAC;YAChDvI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACqH,MAAM,CAAC;YAC5CxI,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACsH,SAAS,CAAC;YAClDzI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACuH,KAAK,CAAC;YAC1C1I,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACwH,UAAU,CAAC;YACpD3I,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACyH,YAAY,CAAC;YACxD5I,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC0H,cAAc,CAAC;YAC5D7I,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC2H,KAAK,CAAC;YAC1C9I,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC4H,OAAO,CAAC;;YAE9C;YACA/I,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtDkB,MAAM,CAAC6H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAACjG,KAAK,EAAE4E,KAAK,KAAK;cAC3C5H,OAAO,CAACC,GAAG,CAAE,SAAQ2H,KAAK,GAAG,CAAE,KAAI5E,KAAK,CAACC,KAAM,IAAG,EAAE;gBAClDC,SAAS,EAAEF,KAAK,CAACE,SAAS;gBAC1BK,YAAY,EAAEP,KAAK,CAACO,YAAY;gBAChC+E,KAAK,EAAEtF,KAAK,CAACsF,KAAK;gBAClBC,QAAQ,EAAEvF,KAAK,CAACuF,QAAQ;gBACxBC,MAAM,EAAExF,KAAK,CAACwF,MAAM;gBACpBM,KAAK,EAAE9F,KAAK,CAAC8F,KAAK;gBAClBC,OAAO,EAAE/F,KAAK,CAAC+F;cACjB,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC,OAAOnI,KAAK,EAAE;QACdZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEW,KAAK,CAACsI,OAAO,CAAC;MACtD;;MAEA;MACA,IAAI/H,MAAM,CAACuC,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAyF,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFtJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,MAAMsJ,OAAO,GAAG;YACdtI,KAAK,EAAES,aAAa;YACpB8H,IAAI,EAAE;UACR,CAAC;UAEDvD,QAAQ,GAAG,MAAM3G,gBAAgB,CAACiK,OAAO,CAAC;UAC1CvJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgG,QAAQ,CAAC;UAEnD,IAAI,CAAAkD,UAAA,GAAAlD,QAAQ,cAAAkD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUhC,IAAI,cAAAiC,eAAA,eAAdA,eAAA,CAAgBhB,OAAO,KAAAiB,UAAA,GAAIpD,QAAQ,cAAAoD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUlC,IAAI,cAAAmC,eAAA,eAAdA,eAAA,CAAgBnC,IAAI,EAAE;YACnDhG,MAAM,GAAG8E,QAAQ,CAACkB,IAAI,CAACA,IAAI;YAC3BnH,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEkB,MAAM,CAACuC,MAAM,CAAC;UACnF;QACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAACsI,OAAO,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI/H,MAAM,CAACuC,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAA+F,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACF5J,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5DgG,QAAQ,GAAG,MAAM3G,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACrCU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEgG,QAAQ,CAAC;UAEhE,IAAI,CAAAwD,UAAA,GAAAxD,QAAQ,cAAAwD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUtC,IAAI,cAAAuC,eAAA,eAAdA,eAAA,CAAgBtB,OAAO,KAAAuB,UAAA,GAAI1D,QAAQ,cAAA0D,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUxC,IAAI,cAAAyC,eAAA,eAAdA,eAAA,CAAgBzC,IAAI,EAAE;YACnD;YACA,MAAM0C,OAAO,GAAG5D,QAAQ,CAACkB,IAAI,CAACA,IAAI;YAClChG,MAAM,GAAG0I,OAAO,CAACpC,MAAM,CAACqC,IAAI;cAAA,IAAAC,WAAA;cAAA,OAC1BD,IAAI,CAACN,IAAI,KAAK,OAAO,IACrBM,IAAI,CAAChC,QAAQ,IACbgC,IAAI,CAACtG,OAAO,MAAAuG,WAAA,GACZD,IAAI,CAAC7G,KAAK,cAAA8G,WAAA,uBAAVA,WAAA,CAAYC,WAAW,CAAC,CAAC,CAACvG,QAAQ,CAAC,OAAO,CAAC;YAAA,CAC7C,CAAC;YACDzD,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEkB,MAAM,CAACuC,MAAM,CAAC;UAC1E;QACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEW,KAAK,CAACsI,OAAO,CAAC;QACvE;MACF;;MAEA;MACA,IAAI/H,MAAM,CAACuC,MAAM,GAAG,CAAC,EAAE;QACrB,MAAMuG,QAAQ,GAAG9I,MAAM,CAACsG,MAAM,CAACzE,KAAK,IAAI;UACtC,MAAMkH,YAAY,GAAGxI,aAAa,KAAK,KAAK,IACxBsB,KAAK,CAAC/B,KAAK,KAAKS,aAAa,IAC7B,CAACsB,KAAK,CAAC/B,KAAK,CAAC,CAAC;;UAElC,MAAMkJ,YAAY,GAAGvI,aAAa,KAAK,KAAK,IACxBoB,KAAK,CAAC8D,SAAS,KAAKlF,aAAa,IACjCoB,KAAK,CAAC9B,KAAK,KAAKU,aAAa,IAC7B,CAACoB,KAAK,CAAC8D,SAAS,CAAC,CAAC;;UAEtC,MAAMsD,cAAc,GAAGtI,eAAe,KAAK,KAAK,IAC1BkB,KAAK,CAACe,OAAO,KAAKjC,eAAe,IACjC,CAACkB,KAAK,CAACe,OAAO,CAAC,CAAC;;UAEtC,OAAOmG,YAAY,IAAIC,YAAY,IAAIC,cAAc;QACvD,CAAC,CAAC;QAEFhJ,SAAS,CAAC6I,QAAQ,CAAC;QACnBjK,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEgK,QAAQ,CAACvG,MAAM,EAAE,QAAQ,CAAC;QAErE,IAAIuG,QAAQ,CAACvG,MAAM,KAAK,CAAC,EAAE;UACzBnC,QAAQ,CAAC,wEAAwE,CAAC;QACpF;MACF,CAAC,MAAM;QACL;QACAvB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAElCsB,QAAQ,CAAC,yFAAyF,CAAC;QACnGH,SAAS,CAAC,EAAE,CAAC;MACf;IAEF,CAAC,CAAC,OAAOiJ,GAAG,EAAE;MACZrK,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEyJ,GAAG,CAAC;MACvD9I,QAAQ,CAAC,0DAA0D,CAAC;MACpEH,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACI,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAMoC,uBAAuB,GAAGpF,OAAO,CAAC,MAAM;IAC5C,IAAImL,QAAQ,GAAG9I,MAAM,CAACsG,MAAM,CAACzE,KAAK,IAAI;MAAA,IAAAsH,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAACjJ,UAAU,MAAA8I,YAAA,GAC/BtH,KAAK,CAACC,KAAK,cAAAqH,YAAA,uBAAXA,YAAA,CAAaN,WAAW,CAAC,CAAC,CAACvG,QAAQ,CAACjC,UAAU,CAACwI,WAAW,CAAC,CAAC,CAAC,OAAAO,cAAA,GAC7DvH,KAAK,CAACe,OAAO,cAAAwG,cAAA,uBAAbA,cAAA,CAAeP,WAAW,CAAC,CAAC,CAACvG,QAAQ,CAACjC,UAAU,CAACwI,WAAW,CAAC,CAAC,CAAC,OAAAQ,YAAA,GAC/DxH,KAAK,CAAC0H,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaR,WAAW,CAAC,CAAC,CAACvG,QAAQ,CAACjC,UAAU,CAACwI,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAME,YAAY,GAAGxI,aAAa,KAAK,KAAK,IAAIsB,KAAK,CAAC/B,KAAK,KAAKS,aAAa;MAC7E,MAAMyI,YAAY,GAAGvI,aAAa,KAAK,KAAK,IAAIoB,KAAK,CAAC8D,SAAS,KAAKlF,aAAa,IAAIoB,KAAK,CAAC9B,KAAK,KAAKU,aAAa;MAClH,MAAMwI,cAAc,GAAGtI,eAAe,KAAK,KAAK,IAAIkB,KAAK,CAACe,OAAO,KAAKjC,eAAe;MAErF,OAAO2I,aAAa,IAAIP,YAAY,IAAIC,YAAY,IAAIC,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOH,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrG,IAAI,CAACqG,CAAC,CAACnF,SAAS,CAAC,GAAG,IAAIlB,IAAI,CAACoG,CAAC,CAAClF,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAACvE,MAAM,EAAEK,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACAjD,SAAS,CAAC,MAAM;IACdoJ,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACApJ,SAAS,CAAC,MAAM;IACdoJ,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACvG,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAEmG,WAAW,CAAC,CAAC;;EAEhE;EACApJ,SAAS,CAAC,MAAM;IACd,IAAIqF,uBAAuB,CAACR,MAAM,GAAG,CAAC,EAAE;MACtCQ,uBAAuB,CAAC+E,OAAO,CAACjG,KAAK,IAAI;QACvC,MAAMW,OAAO,GAAGX,KAAK,CAACmB,GAAG,IAAInB,KAAK,CAACoB,EAAE;QACrC,IAAIT,OAAO,IAAI,CAACrB,QAAQ,CAACqB,OAAO,CAAC,EAAE;UACjCkE,oBAAoB,CAAClE,OAAO,CAAC;QAC/B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACO,uBAAuB,EAAE5B,QAAQ,CAAC,CAAC;;EAEvC;EACA,MAAMuF,oBAAoB,GAAG,MAAOlE,OAAO,IAAK;IAC9C,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,uBAAsB1C,OAAQ,EAAC,EAAE;QACxH2C,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAAShG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D;MACF,CAAC,CAAC;MAEF,IAAIyF,QAAQ,CAACc,EAAE,EAAE;QACf,MAAM+D,YAAY,GAAG,MAAM7E,QAAQ,CAACgB,IAAI,CAAC,CAAC;QAC1CjH,OAAO,CAACC,GAAG,CAAE,+BAA8B0D,OAAQ,GAAE,EAAEmH,YAAY,CAAC;QAEpE,IAAIA,YAAY,CAAC1C,OAAO,IAAI0C,YAAY,CAAC3D,IAAI,EAAE;UAC7C,MAAM4D,aAAa,GAAGD,YAAY,CAAC3D,IAAI,CAAC7E,QAAQ,IAAIwI,YAAY,CAAC3D,IAAI;UACrE,IAAI6D,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE;YAChCxI,WAAW,CAAC2E,IAAI,KAAK;cACnB,GAAGA,IAAI;cACP,CAACvD,OAAO,GAAGoH;YACb,CAAC,CAAC,CAAC;YACH/K,OAAO,CAACC,GAAG,CAAE,SAAQ8K,aAAa,CAACrH,MAAO,uBAAsBC,OAAQ,EAAC,CAAC;UAC5E;QACF;MACF;IACF,CAAC,CAAC,OAAO/C,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAE,uCAAsC0D,OAAQ,GAAE,EAAE/C,KAAK,CAAC;IACvE;EACF,CAAC;EAED,oBACEnB,OAAA;IAAKqH,SAAS,EAAC,yBAAyB;IAAAoE,QAAA,gBACtCzL,OAAA;MAAKqH,SAAS,EAAC,sBAAsB;MAAAoE,QAAA,gBACnCzL,OAAA;QAAAyL,QAAA,EAAKpI,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAAqI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5D7L,OAAA;QAAAyL,QAAA,EAAIpI,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAAqI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGN7L,OAAA;MAAKqH,SAAS,EAAC,gBAAgB;MAAAoE,QAAA,gBAC7BzL,OAAA;QAAKqH,SAAS,EAAC,gBAAgB;QAAAoE,QAAA,eAC7BzL,OAAA;UACE+J,IAAI,EAAC,MAAM;UACX+B,WAAW,EAAEzI,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClE0I,KAAK,EAAEhK,UAAW;UAClBiK,QAAQ,EAAG9K,CAAC,IAAKc,aAAa,CAACd,CAAC,CAAC+K,MAAM,CAACF,KAAK,CAAE;UAC/C1E,SAAS,EAAC;QAAc;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7L,OAAA;QAAKqH,SAAS,EAAC,gBAAgB;QAAAoE,QAAA,gBAC7BzL,OAAA;UACE+L,KAAK,EAAE9J,aAAc;UACrB+J,QAAQ,EAAG9K,CAAC,IAAKgB,gBAAgB,CAAChB,CAAC,CAAC+K,MAAM,CAACF,KAAK,CAAE;UAClD1E,SAAS,EAAC,eAAe;UAAAoE,QAAA,gBAEzBzL,OAAA;YAAQ+L,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAEpI,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrE7L,OAAA;YAAQ+L,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAEpI,WAAW,GAAG,WAAW,GAAG;UAAW;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5E7L,OAAA;YAAQ+L,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAEpI,WAAW,GAAG,KAAK,GAAG;UAAU;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAET7L,OAAA;UACE+L,KAAK,EAAE5J,aAAc;UACrB6J,QAAQ,EAAG9K,CAAC,IAAKkB,gBAAgB,CAAClB,CAAC,CAAC+K,MAAM,CAACF,KAAK,CAAE;UAClD1E,SAAS,EAAC,eAAe;UAAAoE,QAAA,eAEzBzL,OAAA;YAAQ+L,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAEpI,WAAW,GAAG,eAAe,GAAG;UAAa;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAET7L,OAAA;UACE+L,KAAK,EAAE1J,eAAgB;UACvB2J,QAAQ,EAAG9K,CAAC,IAAKoB,kBAAkB,CAACpB,CAAC,CAAC+K,MAAM,CAACF,KAAK,CAAE;UACpD1E,SAAS,EAAC,eAAe;UAAAoE,QAAA,eAEzBzL,OAAA;YAAQ+L,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAEpI,WAAW,GAAG,aAAa,GAAG;UAAc;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7L,OAAA;MAAKqH,SAAS,EAAC,eAAe;MAAAoE,QAAA,EAC3B7J,OAAO,gBACN5B,OAAA;QAAKqH,SAAS,EAAC,eAAe;QAAAoE,QAAA,gBAC5BzL,OAAA;UAAKqH,SAAS,EAAC;QAAiB;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC7L,OAAA;UAAAyL,QAAA,EAAIpI,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJ1K,KAAK,gBACPnB,OAAA;QAAKqH,SAAS,EAAC,aAAa;QAAAoE,QAAA,gBAC1BzL,OAAA,CAACL,eAAe;UAAC0H,SAAS,EAAC;QAAY;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C7L,OAAA;UAAAyL,QAAA,EAAKpI,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7E7L,OAAA;UAAAyL,QAAA,EAAItK;QAAK;UAAAuK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd7L,OAAA;UAAQkM,OAAO,EAAE1D,WAAY;UAACnB,SAAS,EAAC,WAAW;UAAAoE,QAAA,EAChDpI,WAAW,GAAG,aAAa,GAAG;QAAW;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJpH,uBAAuB,CAACR,MAAM,GAAG,CAAC,gBACpCjE,OAAA;QAAKqH,SAAS,EAAC,aAAa;QAAAoE,QAAA,EACzBhH,uBAAuB,CAACoD,GAAG,CAAC,CAACtE,KAAK,EAAE4E,KAAK;UAAA,IAAAgE,IAAA,EAAAC,WAAA;UAAA,oBACxCpM,OAAA;YAAiBqH,SAAS,EAAC,YAAY;YAAAoE,QAAA,EACpClJ,iBAAiB,KAAK4F,KAAK;YAAA;YAC1B;YACAnI,OAAA;cAAKqH,SAAS,EAAC,qBAAqB;cAAAoE,QAAA,eAClCzL,OAAA;gBAAKqH,SAAS,EAAC,sBAAsB;gBAAAoE,QAAA,gBACnCzL,OAAA;kBAAKqH,SAAS,EAAC,sBAAsB;kBAAAoE,QAAA,EAClClI,KAAK,CAAC8E,QAAQ,gBACbrI,OAAA;oBACEqM,GAAG,EAAGA,GAAG,IAAK3J,WAAW,CAAC2J,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,MAAM;oBACb5D,MAAM,EAAEzF,eAAe,CAACC,KAAK,CAAE;oBAC/BqJ,KAAK,EAAE;sBACLF,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdE,eAAe,EAAE,MAAM;sBACvBC,SAAS,EAAE;oBACb,CAAE;oBACFC,OAAO,EAAG7L,CAAC,IAAK0B,aAAa,CAAE,yBAAwBW,KAAK,CAACC,KAAM,EAAC,CAAE;oBACtEwJ,SAAS,EAAEA,CAAA,KAAMpK,aAAa,CAAC,IAAI,CAAE;oBACrCqK,WAAW,EAAC,WAAW;oBAAAxB,QAAA,gBAEvBzL,OAAA;sBAAQkN,GAAG,EAAE3J,KAAK,CAAC+E,cAAc,IAAI/E,KAAK,CAAC8E,QAAS;sBAAC0B,IAAI,EAAC;oBAAW;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvEtI,KAAK,CAAC4J,SAAS,IAAI5J,KAAK,CAAC4J,SAAS,CAAClJ,MAAM,GAAG,CAAC,IAAIV,KAAK,CAAC4J,SAAS,CAACtF,GAAG,CAAC,CAACuF,QAAQ,EAAEjF,KAAK,kBACpFnI,OAAA;sBAEEqN,IAAI,EAAC,WAAW;sBAChBH,GAAG,EAAEE,QAAQ,CAACE,GAAI;sBAClBC,OAAO,EAAEH,QAAQ,CAACI,QAAS;sBAC3BC,KAAK,EAAEL,QAAQ,CAACM,YAAa;sBAC7BC,OAAO,EAAEP,QAAQ,CAACQ,SAAS,IAAIzF,KAAK,KAAK;oBAAE,GALrC,GAAEiF,QAAQ,CAACI,QAAS,IAAGrF,KAAM,EAAC;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,GACNtI,KAAK,CAACQ,OAAO,gBACf/D,OAAA;oBACEkN,GAAG,EAAG,iCAAgC3J,KAAK,CAACQ,OAAQ,mBAAmB;oBACvEP,KAAK,EAAED,KAAK,CAACC,KAAM;oBACnBqK,WAAW,EAAC,GAAG;oBACfC,eAAe;oBACflB,KAAK,EAAE;sBAAEF,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,MAAM;sBAAEoB,MAAM,EAAE;oBAAO;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,gBAEV7L,OAAA;oBAAKqH,SAAS,EAAC,aAAa;oBAAAoE,QAAA,gBAC1BzL,OAAA;sBAAKqH,SAAS,EAAC,YAAY;sBAAAoE,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpC7L,OAAA;sBAAAyL,QAAA,EAAI;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1B7L,OAAA;sBAAAyL,QAAA,EAAI9I,UAAU,IAAI;oBAA4C;sBAAA+I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN7L,OAAA;kBAAKqH,SAAS,EAAC,oBAAoB;kBAAAoE,QAAA,gBACjCzL,OAAA;oBAAIqH,SAAS,EAAC,qBAAqB;oBAAAoE,QAAA,EAAElI,KAAK,CAACC;kBAAK;oBAAAkI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtD7L,OAAA;oBAAKqH,SAAS,EAAC,oBAAoB;oBAAAoE,QAAA,gBACjCzL,OAAA;sBAAAyL,QAAA,EAAOpH,cAAc,CAACd,KAAK,CAACe,OAAO;oBAAC;sBAAAoH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5C7L,OAAA;sBAAAyL,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACd7L,OAAA;sBAAAyL,QAAA,GAAM,QAAM,EAAClI,KAAK,CAAC8D,SAAS,IAAI9D,KAAK,CAAC9B,KAAK;oBAAA;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAClDtI,KAAK,CAAC/B,KAAK,iBACVxB,OAAA,CAAAE,SAAA;sBAAAuL,QAAA,gBACEzL,OAAA;wBAAAyL,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACd7L,OAAA;wBAAAyL,QAAA,EAAOlI,KAAK,CAAC/B;sBAAK;wBAAAkK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAC1B,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN7L,OAAA;oBAAKqH,SAAS,EAAC,uBAAuB;oBAAAoE,QAAA,gBACpCzL,OAAA;sBACEqH,SAAS,EAAG,sBAAqBpE,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;sBACpEiJ,OAAO,EAAEA,CAAA,KAAMhJ,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;sBAAAwI,QAAA,gBAEtDzL,OAAA;wBAAAyL,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACf7L,OAAA;wBAAAyL,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACT7L,OAAA;sBAAQqH,SAAS,EAAC,oBAAoB;sBAAAoE,QAAA,gBACpCzL,OAAA;wBAAAyL,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACf7L,OAAA;wBAAAyL,QAAA,EAAM;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACT7L,OAAA;sBACEqH,SAAS,EAAC,oBAAoB;sBAC9B6E,OAAO,EAAEA,CAAA,KAAM1J,oBAAoB,CAAC,IAAI,CAAE;sBAAAiJ,QAAA,gBAE1CzL,OAAA;wBAAAyL,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACd7L,OAAA;wBAAAyL,QAAA,EAAM;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL5I,gBAAgB,iBACfjD,OAAA;kBAAKqH,SAAS,EAAC,0BAA0B;kBAAAoE,QAAA,gBACvCzL,OAAA;oBAAKqH,SAAS,EAAC,yBAAyB;oBAAAoE,QAAA,eACtCzL,OAAA;sBAAAyL,QAAA,GAAOjH,uBAAuB,CAAC,CAAC,CAACP,MAAM,EAAC,WAAS;oBAAA;sBAAAyH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eAGN7L,OAAA;oBAAKqH,SAAS,EAAC,uBAAuB;oBAAAoE,QAAA,gBACpCzL,OAAA;sBAAKqH,SAAS,EAAC,wBAAwB;sBAAAoE,QAAA,EACpCpL,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2N,YAAY,gBACjBhO,OAAA;wBACEkN,GAAG,EAAE7M,IAAI,CAAC2N,YAAa;wBACvBC,GAAG,EAAC,SAAS;wBACbrB,KAAK,EAAE;0BAAEF,KAAK,EAAE,MAAM;0BAAEC,MAAM,EAAE,MAAM;0BAAEuB,YAAY,EAAE,KAAK;0BAAEpB,SAAS,EAAE;wBAAQ;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnF,CAAC,IAAAM,IAAA,GAED,CAAA9L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS,MAAIhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ,KAAI,SAAS,cAAA4K,IAAA,wBAAAC,WAAA,GAA7DD,IAAA,CAAgElF,MAAM,CAAC,CAAC,CAAC,cAAAmF,WAAA,uBAAzEA,WAAA,CAA2ElF,WAAW,CAAC;oBACxF;sBAAAwE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN7L,OAAA;sBAAK4M,KAAK,EAAE;wBAAEuB,IAAI,EAAE;sBAAE,CAAE;sBAAA1C,QAAA,gBACtBzL,OAAA;wBACEqH,SAAS,EAAC,6BAA6B;wBACvC0E,KAAK,EAAEhJ,UAAW;wBAClBiJ,QAAQ,EAAG9K,CAAC,IAAK8B,aAAa,CAAC9B,CAAC,CAAC+K,MAAM,CAACF,KAAK,CAAE;wBAC/CD,WAAW,EAAC,kBAAkB;wBAC9BsC,IAAI,EAAC,GAAG;wBACRxB,KAAK,EAAE;0BACLyB,SAAS,EAAE,MAAM;0BACjBC,MAAM,EAAE,MAAM;0BACdC,QAAQ,EAAE;wBACZ,CAAE;wBACFC,OAAO,EAAGtN,CAAC,IAAK;0BACdA,CAAC,CAAC+K,MAAM,CAACW,KAAK,CAACD,MAAM,GAAG,MAAM;0BAC9BzL,CAAC,CAAC+K,MAAM,CAACW,KAAK,CAACD,MAAM,GAAGzL,CAAC,CAAC+K,MAAM,CAACwC,YAAY,GAAG,IAAI;wBACtD;sBAAE;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACD9I,UAAU,CAACW,IAAI,CAAC,CAAC,iBAChB1D,OAAA;wBAAKqH,SAAS,EAAC,yBAAyB;wBAAAoE,QAAA,gBACtCzL,OAAA;0BACEqH,SAAS,EAAC,4BAA4B;0BACtC6E,OAAO,EAAEA,CAAA,KAAMlJ,aAAa,CAAC,EAAE,CAAE;0BAAAyI,QAAA,EAClC;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT7L,OAAA;0BACEqH,SAAS,EAAC,4BAA4B;0BACtC6E,OAAO,EAAE9G,gBAAiB;0BAC1BsJ,QAAQ,EAAE,CAAC3L,UAAU,CAACW,IAAI,CAAC,CAAE;0BAAA+H,QAAA,EAC9B;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN7L,OAAA;oBAAKqH,SAAS,EAAC,uBAAuB;oBAAAoE,QAAA,EACnCjH,uBAAuB,CAAC,CAAC,CAACP,MAAM,KAAK,CAAC,gBACrCjE,OAAA;sBAAK4M,KAAK,EAAE;wBAAE+B,SAAS,EAAE,QAAQ;wBAAEC,OAAO,EAAE,QAAQ;wBAAEC,KAAK,EAAE;sBAAU,CAAE;sBAAApD,QAAA,gBACvEzL,OAAA;wBAAK4M,KAAK,EAAE;0BAAEkC,QAAQ,EAAE,MAAM;0BAAEC,YAAY,EAAE;wBAAO,CAAE;wBAAAtD,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChE7L,OAAA;wBAAAyL,QAAA,EAAG;sBAAqD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,GAENrH,uBAAuB,CAAC,CAAC,CAACqD,GAAG,CAAErC,OAAO;sBAAA,IAAAwJ,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,iBAAA,EAAAC,iBAAA;sBAAA,oBACpCpP,OAAA;wBAAqCqH,SAAS,EAAC,iBAAiB;wBAAAoE,QAAA,gBAC9DzL,OAAA;0BAAKqH,SAAS,EAAC,wBAAwB;0BAAAoE,QAAA,GACpCjG,OAAO,CAACc,MAAM,IAAId,OAAO,CAACc,MAAM,CAACtC,QAAQ,CAAC,MAAM,CAAC,gBAChDhE,OAAA;4BACEkN,GAAG,EAAE1H,OAAO,CAACc,MAAO;4BACpB2H,GAAG,EAAC,SAAS;4BACbrB,KAAK,EAAE;8BAAEF,KAAK,EAAE,MAAM;8BAAEC,MAAM,EAAE,MAAM;8BAAEuB,YAAY,EAAE,KAAK;8BAAEpB,SAAS,EAAE;4BAAQ,CAAE;4BAClFC,OAAO,EAAG7L,CAAC,IAAK;8BACdA,CAAC,CAAC+K,MAAM,CAACW,KAAK,CAACyC,OAAO,GAAG,MAAM;8BAC/BnO,CAAC,CAAC+K,MAAM,CAACqD,WAAW,CAAC1C,KAAK,CAACyC,OAAO,GAAG,MAAM;4BAC7C;0BAAE;4BAAA3D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,GACA,IAAI,eACR7L,OAAA;4BAAK4M,KAAK,EAAE;8BACVyC,OAAO,EAAE7J,OAAO,CAACc,MAAM,IAAId,OAAO,CAACc,MAAM,CAACtC,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,MAAM;8BAC5E0I,KAAK,EAAE,MAAM;8BACbC,MAAM,EAAE,MAAM;8BACd4C,UAAU,EAAE,QAAQ;8BACpBC,cAAc,EAAE,QAAQ;8BACxBV,QAAQ,EAAE,MAAM;8BAChBW,UAAU,EAAE;4BACd,CAAE;4BAAAhE,QAAA,EACCjG,OAAO,CAACc,MAAM,IAAI,CAACd,OAAO,CAACc,MAAM,CAACtC,QAAQ,CAAC,MAAM,CAAC,GAAGwB,OAAO,CAACc,MAAM,GAAG,EAAA0I,eAAA,GAAAxJ,OAAO,CAACG,MAAM,cAAAqJ,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgB/H,MAAM,CAAC,CAAC,CAAC,cAAAgI,qBAAA,uBAAzBA,qBAAA,CAA2B/H,WAAW,CAAC,CAAC,KAAI;0BAAG;4BAAAwE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN7L,OAAA;0BAAKqH,SAAS,EAAC,yBAAyB;0BAAAoE,QAAA,gBACtCzL,OAAA;4BAAKqH,SAAS,EAAC,wBAAwB;4BAAAoE,QAAA,gBACrCzL,OAAA;8BAAMqH,SAAS,EAAC,wBAAwB;8BAAAoE,QAAA,EACrCjG,OAAO,CAACG,MAAM,IAAI;4BAAW;8BAAA+F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,EACN,CAACrG,OAAO,CAACK,QAAQ,KAAK,OAAO,IAAIL,OAAO,CAACO,OAAO,MAAAmJ,aAAA,GAAI1J,OAAO,CAACnF,IAAI,cAAA6O,aAAA,uBAAZA,aAAA,CAAcnJ,OAAO,mBACxE/F,OAAA,CAACJ,UAAU;8BAACgN,KAAK,EAAE;gCAAEiC,KAAK,EAAE,SAAS;gCAAEC,QAAQ,EAAE,MAAM;gCAAEY,UAAU,EAAE;8BAAM,CAAE;8BAAClM,KAAK,EAAC;4BAAgB;8BAAAkI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CACvG,eACD7L,OAAA;8BAAMqH,SAAS,EAAC,sBAAsB;8BAAAoE,QAAA,EACnC7G,aAAa,CAACY,OAAO,CAACS,SAAS,IAAIT,OAAO,CAACX,SAAS;4BAAC;8BAAA6G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACN7L,OAAA;4BAAKqH,SAAS,EAAC,sBAAsB;4BAAAoE,QAAA,EAClCjG,OAAO,CAACE;0BAAI;4BAAAgG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN7L,OAAA;4BAAKqH,SAAS,EAAC,yBAAyB;4BAAAoE,QAAA,gBACtCzL,OAAA;8BACEkM,OAAO,EAAEA,CAAA,KAAMvE,iBAAiB,CAACnC,OAAO,CAACd,GAAG,IAAIc,OAAO,CAACb,EAAE,CAAE;8BAC5D0C,SAAS,EAAG,0BAAyB,CAAA8H,iBAAA,GAAA3J,OAAO,CAACW,OAAO,cAAAgJ,iBAAA,eAAfA,iBAAA,CAAiBnL,QAAQ,CAAC3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;8BAAA+G,QAAA,gBAE3FzL,OAAA;gCAAAyL,QAAA,EAAO,CAAA2D,iBAAA,GAAA5J,OAAO,CAACW,OAAO,cAAAiJ,iBAAA,eAAfA,iBAAA,CAAiBpL,QAAQ,CAAC3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,GAAG,CAAC,GAAG,IAAI,GAAG;8BAAI;gCAAAgH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EAChErG,OAAO,CAACU,KAAK,GAAG,CAAC,iBAAIlG,OAAA;gCAAAyL,QAAA,EAAOjG,OAAO,CAACU;8BAAK;gCAAAwF,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5C,CAAC,eACT7L,OAAA;8BAAQqH,SAAS,EAAC,wBAAwB;8BAAAoE,QAAA,EAAC;4BAE3C;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,EACRrG,OAAO,CAACnF,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,GAAG,kBACzB1E,OAAA,CAAAE,SAAA;8BAAAuL,QAAA,gBACEzL,OAAA;gCAAQqH,SAAS,EAAC,wBAAwB;gCAAAoE,QAAA,EAAC;8BAE3C;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACT7L,OAAA;gCACEqH,SAAS,EAAC,wBAAwB;gCAClC6E,OAAO,EAAEA,CAAA,KAAM;kCACb,IAAIyD,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;oCACnE3H,mBAAmB,CAACzC,OAAO,CAACd,GAAG,IAAIc,OAAO,CAACb,EAAE,CAAC;kCAChD;gCACF,CAAE;gCAAA8G,QAAA,EACH;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA,eACT,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA,GArEErG,OAAO,CAACd,GAAG,IAAIc,OAAO,CAACb,EAAE;wBAAA+G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAsE9B,CAAC;oBAAA,CACP;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACA7L,OAAA;cAAKqH,SAAS,EAAC,YAAY;cAAC6E,OAAO,EAAEA,CAAA,KAAMhE,eAAe,CAACC,KAAK,CAAE;cAAAsD,QAAA,gBAChEzL,OAAA;gBAAKqH,SAAS,EAAC,sBAAsB;gBAAAoE,QAAA,gBACnCzL,OAAA;kBACEkN,GAAG,EAAE5J,eAAe,CAACC,KAAK,CAAE;kBAC5B0K,GAAG,EAAE1K,KAAK,CAACC,KAAM;kBACjB6D,SAAS,EAAC,iBAAiB;kBAC3BzF,OAAO,EAAC,MAAM;kBACdmL,OAAO,EAAG7L,CAAC,IAAK;oBACdX,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEU,CAAC,CAAC+K,MAAM,CAACiB,GAAG,CAAC;oBACjE3M,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;oBAE5C;oBACA,IAAI+C,KAAK,CAACQ,OAAO,KAAKR,KAAK,CAACQ,OAAO,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIT,KAAK,CAACQ,OAAO,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAIT,KAAK,CAACQ,OAAO,CAACE,MAAM,KAAK,EAAE,CAAC,EAAE;sBACjI,IAAIC,OAAO,GAAGX,KAAK,CAACQ,OAAO;sBAC3B,MAAMI,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;sBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;sBACpC,MAAME,YAAY,GAAI,8BAA6BF,OAAQ,gBAAe;;sBAE1E;sBACA,IAAIhD,CAAC,CAAC+K,MAAM,CAACiB,GAAG,KAAK9I,YAAY,IAAI,CAAClD,CAAC,CAAC+K,MAAM,CAACiB,GAAG,CAAClJ,QAAQ,CAAC,aAAa,CAAC,EAAE;wBAC1EzD,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4D,YAAY,CAAC;wBAClElD,CAAC,CAAC+K,MAAM,CAACiB,GAAG,GAAG9I,YAAY;wBAC3B;sBACF;oBACF;;oBAEA;oBACA7D,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;oBACjEU,CAAC,CAAC+K,MAAM,CAACiB,GAAG,GAAG,4cAA4c;kBAC7d;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF7L,OAAA;kBAAKqH,SAAS,EAAC,cAAc;kBAAAoE,QAAA,eAC3BzL,OAAA,CAACR,YAAY;oBAAC6H,SAAS,EAAC;kBAAW;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACN7L,OAAA;kBAAKqH,SAAS,EAAC,gBAAgB;kBAAAoE,QAAA,EAC5BlI,KAAK,CAACsM,QAAQ,IAAI;gBAAO;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EACLtI,KAAK,CAAC4J,SAAS,IAAI5J,KAAK,CAAC4J,SAAS,CAAClJ,MAAM,GAAG,CAAC,iBAC5CjE,OAAA;kBAAKqH,SAAS,EAAC,gBAAgB;kBAAAoE,QAAA,gBAC7BzL,OAAA,CAACN,YAAY;oBAAAgM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,MAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN7L,OAAA;gBAAKqH,SAAS,EAAC,oBAAoB;gBAAAoE,QAAA,gBACjCzL,OAAA;kBAAIqH,SAAS,EAAC,aAAa;kBAAAoE,QAAA,EAAElI,KAAK,CAACC;gBAAK;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9C7L,OAAA;kBAAKqH,SAAS,EAAC,YAAY;kBAAAoE,QAAA,gBACzBzL,OAAA;oBAAMqH,SAAS,EAAC,eAAe;oBAAAoE,QAAA,EAAEpH,cAAc,CAACd,KAAK,CAACe,OAAO;kBAAC;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtE7L,OAAA;oBAAMqH,SAAS,EAAC,aAAa;oBAAAoE,QAAA,EAC1BxJ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAAC8D,SAAS,IAAI9D,KAAK,CAAC9B,KAAM,EAAC,GAAI,SAAQ8B,KAAK,CAAC8D,SAAS,IAAI9D,KAAK,CAAC9B,KAAM,EAAC,GACvG,QAAO8B,KAAK,CAAC8D,SAAS,IAAI9D,KAAK,CAAC9B,KAAM;kBAAC;oBAAAiK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN7L,OAAA;kBAAKqH,SAAS,EAAC,YAAY;kBAAAoE,QAAA,GACxBlI,KAAK,CAAC0H,KAAK,iBAAIjL,OAAA;oBAAMqH,SAAS,EAAC,WAAW;oBAAAoE,QAAA,EAAElI,KAAK,CAAC0H;kBAAK;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/DtI,KAAK,CAACuM,eAAe,IAAIvM,KAAK,CAACuM,eAAe,MAAMvM,KAAK,CAAC8D,SAAS,IAAI9D,KAAK,CAAC9B,KAAK,CAAC,iBAClFzB,OAAA;oBAAMqH,SAAS,EAAC,YAAY;oBAAAoE,QAAA,GACzBpI,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDpB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAACuM,eAAgB,EAAC,GAAI,SAAQvM,KAAK,CAACuM,eAAgB,EAAC,GACrF,QAAOvM,KAAK,CAACuM,eAAgB,EAAC;kBAAA;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GAjTO1D,KAAK;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkTV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN7L,OAAA;QAAKqH,SAAS,EAAC,aAAa;QAAAoE,QAAA,gBAC1BzL,OAAA,CAACP,eAAe;UAAC4H,SAAS,EAAC;QAAY;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C7L,OAAA;UAAAyL,QAAA,EAAKpI,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1E7L,OAAA;UAAAyL,QAAA,EAAIpI,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJ7L,OAAA;UAAGqH,SAAS,EAAC,YAAY;UAAAoE,QAAA,EAAEpI,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzL,EAAA,CA96BID,YAAY;EAAA,QAEHZ,WAAW;AAAA;AAAAwQ,EAAA,GAFpB5P,YAAY;AAg7BlB,eAAeA,YAAY;AAAC,IAAA4P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}