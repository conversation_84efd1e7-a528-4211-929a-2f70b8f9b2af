/* Enhanced Video Lessons Styles */
.video-lessons-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Enhanced Header */
.video-lessons-header {
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255,255,255,0.1);
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.header-main {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
}

.header-text h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-text p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0.5rem 0 0 0;
}

.level-display {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.current-level, .current-class {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.level-label, .class-label {
  display: block;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.level-value, .class-value {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
}

/* Content Area */
.video-lessons-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 2rem 2rem;
}

/* Enhanced Controls */
.video-controls {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.controls-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.control-label svg {
  font-size: 1rem;
  color: #667eea;
}

.control-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  font-weight: 500;
  color: #2d3748;
  transition: all 0.2s ease;
  cursor: pointer;
}

.control-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.control-select:hover {
  border-color: #cbd5e0;
}

.level-select {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

.level-select:focus {
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Search Row */
.search-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-container {
  flex: 1;
  position: relative;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  font-size: 1rem;
  color: #2d3748;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input::placeholder {
  color: #a0aec0;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  font-size: 1.1rem;
}

.clear-search-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: #f56565;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.clear-search-btn:hover {
  background: #e53e3e;
  transform: translateY(-50%) scale(1.05);
}

.refresh-btn {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.refresh-btn:hover {
  background: linear-gradient(135deg, #38a169, #2f855a);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

/* Enhanced Video Grid - Better Responsive Design */
.videos-grid {
  display: grid !important;
  gap: 1rem;
  padding: 1rem 0;
  width: 100%;

  /* Mobile: Single column */
  grid-template-columns: 1fr !important;
}

/* Tablet: Better 2-column layout for readability */
@media (min-width: 768px) and (max-width: 1023px) {
  .videos-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1.5rem;
    max-width: 100%;
  }

  .video-card {
    width: 100%;
    max-width: none;
  }

  .video-card-thumbnail {
    height: 200px;
    width: 100%;
  }
}

/* Laptop: Better 3-column layout */
@media (min-width: 1024px) and (max-width: 1439px) {
  .videos-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 1.5rem;
    max-width: 100%;
  }

  .video-card {
    width: 100%;
    max-width: none;
  }

  .video-card-thumbnail {
    height: 220px;
    width: 100%;
  }
}

/* Large Desktop: 4 columns */
@media (min-width: 1440px) and (max-width: 1919px) {
  .videos-grid {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 1.5rem;
    max-width: 100%;
  }

  .video-card-thumbnail {
    height: 240px;
  }
}

/* Extra Large Desktop: 5 columns */
@media (min-width: 1920px) {
  .videos-grid {
    display: grid !important;
    grid-template-columns: repeat(5, 1fr) !important;
    gap: 2rem;
    max-width: 100%;
  }

  .video-card-thumbnail {
    height: 260px;
  }
}

.video-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 100%;
  position: relative;
}

.video-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.video-card-thumbnail {
  position: relative;
  width: 100%;
  height: 180px;
  overflow: hidden;
  background: #f7fafc;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-card:hover .thumbnail-image {
  transform: scale(1.05);
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0;
}

.video-card:hover .play-overlay {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
  color: white;
  font-size: 24px;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.subtitle-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 2px;
}

.video-card-content {
  padding: 1.25rem;
}

.video-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.video-subject {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.video-class {
  color: #718096;
  font-size: 0.85rem;
  font-weight: 500;
}

.video-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.level-tag {
  background: #e2e8f0;
  color: #4a5568;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: capitalize;
}

.topic-tag {
  background: #fed7d7;
  color: #c53030;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.shared-tag {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(251, 191, 36, 0.3);
}

/* Comments Section - Below Video Layout */
.comments-section-below {
  background: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  margin-top: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.comments-section-below.normal-comments {
  width: 100%;
  max-width: 500px;
  margin: 1rem auto 0;
  padding: 1rem;
}

.comments-section-below.expanded-comments {
  flex: 1;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  max-height: 60vh;
  overflow: hidden;
  max-width: 400px;
  margin: 0;
}

/* Legacy comments section for backward compatibility */
.comments-section {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.comments-section.normal-mode {
  max-height: 400px;
  overflow-y: auto;
}

.comments-section.expanded-mode {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 400px;
  max-height: 60vh;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  z-index: 20;
  overflow: hidden;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #e2e8f0;
  background: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.comments-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.comments-count {
  font-size: 0.85rem;
  color: #718096;
  font-weight: 500;
}

.comments-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.comments-title svg {
  color: #667eea;
  font-size: 1.3rem;
}

/* Enhanced comments section visibility */
.comments-section-below.normal-comments {
  border-top: 3px solid #667eea;
  box-shadow: 0 -2px 10px rgba(102, 126, 234, 0.1);
}

.comments-section-below.expanded-comments {
  border-left: 3px solid #667eea;
  box-shadow: -2px 0 10px rgba(102, 126, 234, 0.1);
}

/* Comments Count Header - Always visible */
.comments-count-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  margin-bottom: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.comments-count-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1d1d1f;
}

.comments-count-display svg {
  color: #007AFF;
  font-size: 1.1rem;
}

.view-comments-btn {
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: white;
  border: none;
  padding: 0.4rem 1rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 0.3rem;
  min-height: 32px;
}

.view-comments-btn:hover {
  background: linear-gradient(135deg, #0056CC, #4A44CF);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

.view-comments-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.comments-toggle-btn {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
  border: 1px solid rgba(142, 142, 147, 0.2);
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-height: 28px;
}

.comments-toggle-btn:hover {
  background: rgba(142, 142, 147, 0.15);
  color: #1d1d1f;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comments-toggle-btn:active {
  transform: translateY(0);
  box-shadow: none;
}

.comments-content {
  overflow-y: auto;
  transition: all 0.3s ease;
  flex: 1;
}

.comments-content.maximized {
  max-height: none;
}

.comments-content.minimized {
  max-height: 200px;
}

.comments-content.collapsed {
  max-height: 0;
  overflow: hidden;
}

/* Scrollbar styling for comments */
.comments-content::-webkit-scrollbar {
  width: 6px;
}

.comments-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.comments-content::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.comments-content::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.add-comment {
  margin-bottom: 1.5rem;
}

.comment-input-container {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.user-avatar.small {
  width: 32px;
  height: 32px;
  font-size: 0.8rem;
}

.comment-input-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.comment-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  font-size: 0.9rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  resize: vertical;
  min-height: 80px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
}

.comment-input:focus {
  outline: none;
  border-color: #007AFF;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.comment-input::placeholder {
  color: #8E8E93;
  font-weight: 400;
}

.comment-submit-btn {
  align-self: flex-end;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: white;
  border: none;
  padding: 0.4rem 1rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  gap: 0.3rem;
  min-height: 32px;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.comment-submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056CC, #4A44CF);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

.comment-submit-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.comment-submit-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.no-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  color: #718096;
  text-align: center;
}

.no-comments-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

.comment {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.comment:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e0;
}

.comment-main {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.85rem;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.comment-author {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
}

.comment-time {
  font-size: 0.75rem;
  color: #718096;
}

.comment-text {
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 0.75rem;
  word-wrap: break-word;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.like-btn {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #8E8E93;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  min-height: 24px;
}

.like-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  color: #FF3B30;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.like-btn.liked {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  border-color: rgba(255, 59, 48, 0.2);
}

.like-btn.liked:hover {
  background: rgba(255, 59, 48, 0.15);
}

.like-btn.small {
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  min-height: 20px;
}

.like-count {
  font-size: 0.7rem;
  font-weight: 500;
}

.reply-btn {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #007AFF;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-height: 24px;
}

.reply-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  color: #0056CC;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reply-btn:active {
  transform: translateY(0);
  box-shadow: none;
}

.replies-count {
  font-size: 0.7rem;
  color: #8E8E93;
  font-weight: 500;
  background: rgba(142, 142, 147, 0.1);
  padding: 0.15rem 0.4rem;
  border-radius: 8px;
}

.reply-input-container {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.reply-input-wrapper {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
}

.reply-input-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.reply-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.reply-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  font-size: 0.85rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  resize: vertical;
  min-height: 60px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
}

.reply-input:focus {
  outline: none;
  border-color: #007AFF;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.reply-input::placeholder {
  color: #8E8E93;
  font-weight: 400;
}

.reply-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.reply-submit-btn {
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: white;
  border: none;
  padding: 0.35rem 0.8rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-height: 28px;
  box-shadow: 0 1px 6px rgba(0, 122, 255, 0.3);
}

.reply-submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056CC, #4A44CF);
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 122, 255, 0.4);
}

.reply-submit-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 6px rgba(0, 122, 255, 0.3);
}

.reply-submit-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.reply-cancel-btn {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
  border: 1px solid rgba(142, 142, 147, 0.2);
  padding: 0.35rem 0.8rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-height: 28px;
}

.reply-cancel-btn:hover {
  background: rgba(142, 142, 147, 0.15);
  color: #1d1d1f;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reply-cancel-btn:active {
  transform: translateY(0);
  box-shadow: none;
}

.replies {
  margin-top: 0.75rem;
  padding-left: 1rem;
  border-left: 3px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.reply {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 0.75rem;
  transition: all 0.2s ease;
}

.reply:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.reply-main {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
}

.reply-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
  min-width: 0;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.4rem;
}

.reply-author {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.85rem;
}

.reply-time {
  font-size: 0.7rem;
  color: #718096;
}

.reply-text {
  color: #4a5568;
  line-height: 1.4;
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  word-wrap: break-word;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Loading, Error, and Empty States */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: white;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  font-size: 1.1rem;
  margin: 0;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: white;
  text-align: center;
}

.error-state .error-icon {
  font-size: 4rem;
  color: #f56565;
  margin-bottom: 1rem;
}

.error-state h3 {
  font-size: 1.5rem;
  margin: 0 0 1rem 0;
  color: white;
}

.error-state p {
  font-size: 1rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

.retry-btn {
  background: linear-gradient(135deg, #f56565, #e53e3e);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #e53e3e, #c53030);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 101, 101, 0.3);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: white;
  text-align: center;
}

.empty-state .empty-icon {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin: 0 0 1rem 0;
  color: white;
}

.empty-state p {
  font-size: 1rem;
  margin: 0 0 0.5rem 0;
  opacity: 0.9;
}

.empty-state .suggestion {
  font-size: 0.9rem;
  opacity: 0.7;
  font-style: italic;
}

/* Inline Video Player - Replaces thumbnail when playing */
.inline-video-player {
  position: relative;
  width: 100%;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  margin-top: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.inline-video-player video,
.inline-video-player iframe {
  width: 100%;
  height: 300px;
  border: none;
  display: block;
}

.video-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: #f5f5f5;
  color: #666;
  text-align: center;
}

.video-error .error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

/* Video Player Overlay - Replaces thumbnail when playing */
.video-player-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
}

/* Horizontal Video Actions */
.youtube-video-actions-horizontal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.youtube-action-btn-small {
  background: rgba(0, 123, 255, 0.1);
  border: 1px solid rgba(0, 123, 255, 0.2);
  color: #007BFF;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  white-space: nowrap;
  flex: 1;
  justify-content: center;
  min-height: 32px;
}

.youtube-action-btn-small:hover {
  background: rgba(0, 123, 255, 0.2);
  transform: translateY(-1px);
}

.youtube-action-btn-small.active {
  background: #007BFF;
  color: white;
  border-color: #007BFF;
}

.youtube-action-btn-small.close-btn {
  background: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.youtube-action-btn-small.close-btn:hover {
  background: rgba(220, 53, 69, 0.2);
}

/* Top Pagination */
.pagination-top {
  margin: 1rem 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1rem;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1rem;
  position: relative;
  z-index: 5;
}

.pagination-info {
  color: white;
  font-size: 0.9rem;
  opacity: 0.8;
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.pagination-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.pagination-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn.active {
  background: #007BFF;
  border-color: #007BFF;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 0.9rem;
}

.page-size-selector select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .videos-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
  }

  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .level-display {
    justify-content: center;
    flex-wrap: wrap;
  }

  .controls-row {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
  }

  .videos-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
  }

  .video-card-thumbnail {
    height: 160px;
  }

  .comments-section {
    max-height: 300px;
  }
}

@media (max-width: 768px) {
  .video-lessons-header {
    padding: 1.5rem 0;
  }

  .header-content {
    padding: 0 1rem;
  }

  .header-text h1 {
    font-size: 2rem;
  }

  .level-display {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .current-level, .current-class {
    text-align: center;
  }

  .video-lessons-content {
    padding: 0 1rem 2rem 1rem;
  }

  .video-controls {
    padding: 1rem;
  }

  .controls-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .search-row {
    flex-direction: column;
    gap: 0.75rem;
  }

  .search-container {
    min-width: auto;
  }

  .refresh-btn {
    width: 100%;
    justify-content: center;
  }

  .videos-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .video-card-thumbnail {
    height: 200px;
  }

  .comments-section {
    padding: 1rem;
    max-height: 250px;
  }

  .comment-input-container {
    gap: 0.5rem;
  }

  .reply-actions {
    flex-direction: column;
    gap: 0.25rem;
  }

  .reply-submit-btn, .reply-cancel-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-main {
    flex-direction: column;
    gap: 1rem;
  }

  .header-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .header-text h1 {
    font-size: 1.75rem;
  }

  .header-text p {
    font-size: 1rem;
  }

  .video-controls {
    padding: 0.75rem;
  }

  .search-input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    font-size: 0.9rem;
  }

  .search-icon {
    left: 0.75rem;
  }

  .clear-search-btn {
    position: static;
    transform: none;
    margin-top: 0.5rem;
    width: 100%;
    font-size: 0.8rem;
  }

  .video-card-content {
    padding: 0.75rem;
  }

  .video-title {
    font-size: 0.95rem;
  }

  .video-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .video-tags {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .comments-section {
    padding: 0.75rem;
    max-height: 200px;
  }

  .comments-title {
    font-size: 1rem;
  }

  .comment-input {
    min-height: 60px;
    font-size: 0.85rem;
  }

  .comment {
    padding: 0.75rem;
  }

  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .comment-text {
    font-size: 0.9rem;
  }
}

/* Video grid specific styles */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

.video-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.video-thumbnail-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  cursor: pointer;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-thumbnail-container:hover .video-thumbnail {
  transform: scale(1.05);
}

.video-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.video-play-overlay:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
  color: white;
  font-size: 24px;
}

.video-duration-badge {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.video-card-content {
  padding: 1rem;
}

.video-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-card-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #667eea;
  font-size: 0.9rem;
  font-weight: 500;
}

.video-card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.video-card-subject {
  background: #e2e8f0;
  color: #4a5568;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.video-card-class {
  color: #718096;
  font-size: 0.8rem;
}

/* Video modal styles */
.video-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 25;
  padding: 2rem;
}

.video-overlay.expanded {
  padding: 0;
}

.video-modal {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  width: 90vw;
  max-width: 800px; /* Fixed maximum width */
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.video-modal.expanded {
  width: 95vw;
  max-width: 1000px; /* Slightly larger when expanded but still fixed */
  border-radius: 12px; /* Keep rounded corners */
}

/* Video Main Layout */
.video-main-layout {
  display: flex;
  overflow: hidden;
  gap: 1rem;
  padding: 1rem;
}

.video-main-layout.normal-layout {
  flex-direction: column;
  align-items: center;
}

.video-main-layout.expanded-layout {
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  max-height: 80vh; /* Fixed height to maintain window size */
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.video-info h3 {
  margin: 0;
  color: #1d1d1f;
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.3;
}

.video-meta {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.video-meta span {
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.video-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Modern Apple-style Buttons */
.control-btn {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.4rem 0.8rem;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.8rem;
  color: #1d1d1f;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-weight: 500;
  min-height: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.control-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-icon {
  font-size: 0.9rem;
  line-height: 1;
}

.btn-text {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Specific Button Styles */
.add-comment-btn {
  background: linear-gradient(135deg, #007AFF, #5856D6) !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3) !important;
}

.add-comment-btn:hover {
  background: linear-gradient(135deg, #0056CC, #4A44CF) !important;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4) !important;
}

.close-comment-btn {
  background: rgba(255, 59, 48, 0.1) !important;
  color: #FF3B30 !important;
  border: 1px solid rgba(255, 59, 48, 0.2) !important;
}

.close-comment-btn:hover {
  background: rgba(255, 59, 48, 0.15) !important;
  border-color: rgba(255, 59, 48, 0.3) !important;
}

.close-btn {
  background: rgba(142, 142, 147, 0.1) !important;
  color: #8E8E93 !important;
  border: 1px solid rgba(142, 142, 147, 0.2) !important;
}

.close-btn:hover {
  background: rgba(142, 142, 147, 0.15) !important;
  color: #1d1d1f !important;
}

.video-container {
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  aspect-ratio: 16 / 9; /* YouTube-style 16:9 aspect ratio */
  max-width: 100%; /* Full width for better viewing */
  margin: 0 auto;
  flex-shrink: 0;
  border-radius: 12px;
  overflow: hidden;
}

/* Video container in expanded layout - YouTube style */
.expanded-layout .video-container {
  max-width: 70%; /* Take most of the width, leave space for comments */
  aspect-ratio: 16 / 9; /* Maintain YouTube aspect ratio */
}

.video-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 12px;
}

/* Video element styling - YouTube style */
video {
  width: 100%;
  height: 100%;
  object-fit: contain; /* Show full video without cropping */
  border-radius: 12px;
  background: #000;
}

.subtitle-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 0.8rem;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.subtitle-icon {
  font-size: 1rem;
}

/* YouTube-style video layout */
.youtube-style-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.youtube-video-section {
  width: 100%;
}

.youtube-video-player {
  width: 100%;
  aspect-ratio: 16 / 9;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
  max-height: 70vh;
}

.youtube-video-info {
  padding: 0 4px;
}

.youtube-video-title {
  font-size: 20px;
  font-weight: 600;
  color: #0f0f0f;
  margin-bottom: 8px;
  line-height: 1.3;
}

.youtube-video-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #606060;
}

.youtube-video-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
  border-bottom: 1px solid #e5e5e5;
  margin-bottom: 16px;
}

.youtube-action-btn {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 3px 6px;
  background: #f2f2f2;
  border: none;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  color: #606060;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 22px;
}

.youtube-action-btn:hover {
  background: #e5e5e5;
}

.youtube-action-btn.active {
  background: #065fd4;
  color: white;
}

.youtube-comments-section {
  margin-top: 24px;
}

.youtube-comments-header {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-bottom: 24px;
  font-size: 16px;
  font-weight: 600;
  color: #0f0f0f;
}

.youtube-comment-input {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.youtube-comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #065fd4;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
}

.youtube-comment-input-field {
  flex: 1;
  border: none;
  border-bottom: 1px solid #ccc;
  padding: 8px 0;
  font-size: 14px;
  outline: none;
  resize: none;
  font-family: inherit;
}

.youtube-comment-input-field:focus {
  border-bottom: 2px solid #065fd4;
}

.youtube-comment-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  justify-content: flex-end;
}

.youtube-comment-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 18px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.youtube-comment-btn.cancel {
  background: transparent;
  color: #606060;
}

.youtube-comment-btn.submit {
  background: #065fd4;
  color: white;
}

.youtube-comment-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.youtube-comments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.youtube-comment {
  display: flex;
  gap: 12px;
}

.youtube-comment-content {
  flex: 1;
}

.youtube-comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.youtube-comment-author {
  font-size: 13px;
  font-weight: 500;
  color: #0f0f0f;
}

.youtube-comment-time {
  font-size: 12px;
  color: #606060;
}

.youtube-comment-text {
  font-size: 14px;
  color: #0f0f0f;
  line-height: 1.4;
  margin-bottom: 8px;
}

.youtube-comment-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.youtube-comment-action {
  display: flex;
  align-items: center;
  gap: 1px;
  background: none;
  border: none;
  color: #606060;
  font-size: 8px;
  font-weight: 400;
  cursor: pointer;
  padding: 1px 2px;
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 16px;
  line-height: 1;
}

.youtube-comment-action:hover {
  background: #f2f2f2;
  color: #404040;
}

.youtube-comment-action.liked {
  color: #065fd4;
  background: rgba(6, 95, 212, 0.1);
}

.youtube-comment-action span {
  font-size: 8px;
  line-height: 1;
}

/* Inline Video Player Styles */
.video-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.inline-video-player {
  margin-top: 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.inline-video-player .youtube-style-layout {
  padding: 0;
  max-width: none;
  margin: 0;
}

.inline-video-player .youtube-video-player {
  border-radius: 0;
  margin-bottom: 0;
}

.inline-video-player .youtube-video-info {
  padding: 16px 20px;
}

.inline-video-player .youtube-comments-section {
  padding: 0 20px 20px;
}

/* Video grid adjustments for inline player */
.videos-grid {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.video-card {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12px;
  overflow: hidden;
}

.video-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
  .youtube-style-layout {
    padding: 8px;
  }

  .youtube-video-title {
    font-size: 14px;
    line-height: 1.2;
  }

  .youtube-video-meta {
    font-size: 12px;
    flex-wrap: wrap;
    gap: 4px;
  }

  .youtube-video-actions {
    flex-wrap: wrap;
    gap: 6px;
    justify-content: center;
  }

  .youtube-action-btn {
    padding: 4px 8px;
    font-size: 9px;
    min-height: 20px;
    gap: 1px;
  }

  .youtube-action-btn span {
    font-size: 12px;
  }

  .inline-video-player .youtube-video-info {
    padding: 8px 12px;
  }

  .inline-video-player .youtube-comments-section {
    padding: 0 12px 12px;
  }

  .youtube-comments-header {
    font-size: 14px;
    margin-bottom: 16px;
  }

  .youtube-comment-input {
    gap: 8px;
  }

  .youtube-comment-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .youtube-comment-input-field {
    font-size: 13px;
    padding: 6px 0;
  }

  .youtube-comment-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .youtube-comment {
    gap: 8px;
  }

  .youtube-comment-author {
    font-size: 12px;
  }

  .youtube-comment-time {
    font-size: 11px;
  }

  .youtube-comment-text {
    font-size: 13px;
    line-height: 1.3;
  }

  .youtube-comment-action {
    font-size: 7px;
    padding: 1px;
    min-height: 14px;
  }

  .youtube-comment-action span {
    font-size: 10px;
  }
}

/* Tablet Responsive Design */
@media (min-width: 769px) and (max-width: 1024px) {
  .youtube-style-layout {
    padding: 16px;
  }

  .youtube-video-title {
    font-size: 18px;
  }

  .youtube-video-meta {
    font-size: 13px;
  }

  .youtube-video-actions {
    gap: 8px;
  }

  .youtube-action-btn {
    padding: 5px 10px;
    font-size: 10px;
    min-height: 24px;
  }

  .youtube-comments-header {
    font-size: 15px;
  }

  .youtube-comment-avatar {
    width: 36px;
    height: 36px;
    font-size: 15px;
  }

  .youtube-comment-input-field {
    font-size: 14px;
  }

  .youtube-comment-btn {
    padding: 7px 14px;
    font-size: 13px;
  }

  .youtube-comment-author {
    font-size: 13px;
  }

  .youtube-comment-text {
    font-size: 14px;
  }

  .youtube-comment-action {
    font-size: 8px;
    min-height: 16px;
  }
}

/* Desktop Responsive Design */
@media (min-width: 1025px) {
  .youtube-style-layout {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .youtube-video-title {
    font-size: 20px;
  }

  .youtube-video-meta {
    font-size: 14px;
  }

  .youtube-video-actions {
    gap: 12px;
  }

  .youtube-action-btn {
    padding: 6px 12px;
    font-size: 11px;
    min-height: 26px;
  }

  .youtube-comments-header {
    font-size: 16px;
  }

  .youtube-comment-avatar {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .youtube-comment-input-field {
    font-size: 14px;
  }

  .youtube-comment-btn {
    padding: 8px 16px;
    font-size: 14px;
  }

  .youtube-comment-author {
    font-size: 13px;
  }

  .youtube-comment-text {
    font-size: 14px;
  }

  .youtube-comment-action {
    font-size: 9px;
    min-height: 18px;
  }
}

/* Video Player Responsive Design */
@media (max-width: 480px) {
  .youtube-video-player {
    border-radius: 8px;
    max-height: 50vh;
  }

  .youtube-style-layout {
    padding: 4px;
  }

  .youtube-video-title {
    font-size: 12px;
    line-height: 1.1;
  }

  .youtube-video-meta {
    font-size: 10px;
    gap: 2px;
  }

  .youtube-video-actions {
    gap: 4px;
    flex-wrap: wrap;
  }

  .youtube-action-btn {
    padding: 2px 6px;
    font-size: 8px;
    min-height: 18px;
    border-radius: 8px;
  }

  .youtube-action-btn span {
    font-size: 10px;
  }

  .youtube-comments-header {
    font-size: 12px;
    margin-bottom: 12px;
  }

  .youtube-comment-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .youtube-comment-input-field {
    font-size: 12px;
    padding: 4px 0;
  }

  .youtube-comment-btn {
    padding: 4px 8px;
    font-size: 10px;
  }

  .youtube-comment-author {
    font-size: 11px;
  }

  .youtube-comment-time {
    font-size: 9px;
  }

  .youtube-comment-text {
    font-size: 12px;
    line-height: 1.2;
  }

  .youtube-comment-action {
    font-size: 6px;
    padding: 0px 1px;
    min-height: 12px;
  }

  .youtube-comment-action span {
    font-size: 8px;
  }
}

@media (min-width: 1440px) {
  .youtube-video-player {
    max-height: 80vh;
  }

  .youtube-style-layout {
    max-width: 1400px;
    padding: 24px;
  }
}

.video-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.error-content {
  padding: 2rem;
}

.error-content .error-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #f56565;
}

.dismiss-error-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 1rem;
  transition: background 0.2s ease;
}

.dismiss-error-btn:hover {
  background: #5a67d8;
}

.video-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: white;
  min-height: 400px;
}

.video-error .error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.video-error h3 {
  margin-bottom: 1rem;
  color: white;
}

.external-link-btn {
  background: #667eea;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  display: inline-block;
  margin-top: 1rem;
  transition: background 0.2s ease;
}

.external-link-btn:hover {
  background: #5a67d8;
  color: white;
  text-decoration: none;
}

/* Responsive Design - Mobile First Approach */

/* Mobile Devices (320px - 768px) */
@media (max-width: 768px) {
  .video-lessons-container {
    padding: 0.5rem;
  }

  .video-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0.5rem 0;
  }

  .video-overlay {
    padding: 0.5rem;
  }

  .video-modal {
    width: 95vw;
    max-width: 95vw;
    border-radius: 12px;
  }

  .video-modal.expanded {
    width: 98vw;
    max-width: 98vw;
  }

  .video-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .video-info h3 {
    font-size: 1rem;
    line-height: 1.2;
  }

  .video-meta {
    gap: 0.3rem;
  }

  .video-meta span {
    font-size: 0.7rem;
    padding: 0.15rem 0.5rem;
  }

  .video-controls {
    gap: 0.3rem;
    width: 100%;
    justify-content: flex-end;
  }

  .control-btn {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
    min-height: 28px;
  }

  .btn-text {
    display: none; /* Hide text on mobile, show only icons */
  }

  .btn-icon {
    font-size: 0.8rem;
  }

  /* Square video container for mobile */
  .video-container {
    max-width: 280px;
    aspect-ratio: 1 / 1;
  }

  .expanded-layout .video-container {
    max-width: 250px;
  }

  /* Video layout responsive design */
  .video-main-layout.expanded-layout {
    flex-direction: column !important;
    align-items: center;
    max-height: none;
  }

  .comments-section-below.normal-comments {
    max-width: 280px;
  }

  .comments-section-below.expanded-comments {
    max-width: 280px;
    max-height: 40vh;
    padding: 0.75rem;
    margin-top: 1rem;
  }

  .comments-count-header {
    padding: 0.75rem;
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }

  .comments-count-display {
    font-size: 0.9rem;
  }

  .view-comments-btn {
    padding: 0.35rem 0.8rem;
    font-size: 0.75rem;
    min-height: 28px;
  }

  .comment-input {
    min-height: 60px;
    font-size: 0.85rem;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  .comment-avatar {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}

/* Tablet Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .video-lessons-container {
    padding: 1rem;
  }

  .video-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  .video-overlay {
    padding: 1.5rem;
  }

  .video-modal {
    width: 85vw;
    max-width: 700px;
  }

  .video-modal.expanded {
    width: 90vw;
    max-width: 900px;
  }

  .video-header {
    padding: 1rem 1.25rem;
  }

  .video-info h3 {
    font-size: 1.05rem;
  }

  .control-btn {
    padding: 0.35rem 0.7rem;
    font-size: 0.75rem;
  }

  .btn-text {
    font-size: 0.75rem;
  }

  /* Square video container for tablet */
  .video-container {
    max-width: 400px;
    aspect-ratio: 1 / 1;
  }

  .expanded-layout .video-container {
    max-width: 350px;
  }

  .comments-section-below.normal-comments {
    max-width: 400px;
  }

  .comments-section-below.expanded-comments {
    max-height: 50vh;
    max-width: 350px;
  }

  .comments-count-header {
    padding: 1rem 1.25rem;
  }
}

/* Desktop/Laptop Devices (1025px+) */
@media (min-width: 1025px) {
  .video-lessons-container {
    padding: 1.5rem;
  }

  .video-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .video-overlay {
    padding: 2rem;
  }

  .video-modal {
    width: 80vw;
    max-width: 800px;
  }

  .video-modal.expanded {
    width: 90vw;
    max-width: 1000px;
  }

  .video-header {
    padding: 1rem 1.5rem;
  }

  /* Square video container for desktop */
  .video-container {
    max-width: 500px;
    aspect-ratio: 1 / 1;
  }

  .expanded-layout .video-container {
    max-width: 400px;
  }

  .comments-section-below.normal-comments {
    max-width: 500px;
  }

  .comments-section-below.expanded-comments {
    max-height: 60vh;
    max-width: 400px;
  }
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .video-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .video-modal {
    max-width: 900px;
  }

  .video-modal.expanded {
    max-width: 1100px;
  }

  .video-container {
    max-width: 550px;
  }

  .expanded-layout .video-container {
    max-width: 450px;
  }

  .comments-section-below.normal-comments {
    max-width: 550px;
  }

  .comments-section-below.expanded-comments {
    max-width: 450px;
  }
}

/* User Profile Styling in Comments */
.comment-author-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.author-details {
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-level, .user-class {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  text-transform: capitalize;
}

.user-level {
  background: #e3f2fd;
  color: #1976d2;
}

.user-class {
  background: #f3e5f5;
  color: #7b1fa2;
}

/* Delete Button Styling */
.delete-btn {
  background: none !important;
  border: none !important;
  color: #ef4444 !important;
  cursor: pointer !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  margin-left: 8px !important;
  transition: all 0.2s ease !important;
}

.delete-btn:hover {
  background: #fef2f2 !important;
  color: #dc2626 !important;
  transform: scale(1.05);
}

/* Enhanced Comment Actions */
.comment-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.comment-actions button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive adjustments for new features */
@media (max-width: 768px) {
  .comment-author-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .author-details {
    gap: 0.25rem;
  }

  .user-level, .user-class {
    font-size: 9px;
    padding: 1px 4px;
  }

  .delete-btn {
    font-size: 11px !important;
    padding: 3px 6px !important;
  }
}

/* Enhanced Mobile Responsive Design for Video Lessons */
@media (max-width: 767px) {
  .video-lessons-container {
    padding: 0;
  }

  .header-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .header-text h1 {
    font-size: 1.75rem;
  }

  .header-text p {
    font-size: 0.85rem;
  }

  .video-lessons-content {
    padding: 0 0.75rem 1rem 0.75rem;
  }

  .videos-grid {
    gap: 0.75rem;
  }

  .video-card {
    border-radius: 8px;
  }

  .video-card-thumbnail {
    height: 150px;
  }

  .video-title {
    font-size: 0.9rem;
    line-height: 1.3;
  }

  .video-meta span {
    font-size: 0.7rem;
  }

  .topic-tag, .shared-tag {
    font-size: 0.6rem;
    padding: 0.15rem 0.3rem;
  }

  .youtube-video-player {
    height: 150px;
  }

  .youtube-video-title {
    font-size: 1rem;
  }

  .youtube-action-btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }

  /* Hide Top Pagination on Mobile - Keep Only Bottom */
  .pagination-top {
    display: none !important;
  }

  .pagination-container.pagination-top {
    display: none !important;
  }

  /* Mobile Horizontal Navigation - Show All Buttons */
  .pagination-container {
    display: flex !important;
    flex-direction: column !important;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin: 1rem 0;
  }

  .pagination-info {
    text-align: center;
    font-size: 0.8rem;
    color: white;
    opacity: 0.9;
    margin: 0;
    width: auto;
  }

  .pagination-controls {
    display: flex !important;
    flex-direction: row !important;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0.25rem;
  }

  .pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 40px;
    height: 36px;
    border-radius: 6px;
    white-space: nowrap;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    margin: 0.125rem;
  }

  .pagination-btn.active {
    background: #007BFF !important;
    color: white !important;
    border-color: #007BFF !important;
  }

  .pagination-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
  }

  .page-size-selector {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: white;
    width: auto;
    margin: 0;
  }

  .page-size-selector select {
    padding: 0.5rem;
    font-size: 0.8rem;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.3);
    color: white;
  }

  /* Mobile Horizontal Video Action Buttons */
  .youtube-video-actions-horizontal {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 0 0 8px 8px !important;
    margin-top: 0 !important;
    position: relative !important;
    z-index: 5 !important;
  }

  .youtube-action-btn-small {
    flex: 1;
    padding: 0.6rem 0.5rem;
    font-size: 0.8rem;
    min-height: 40px;
    text-align: center;
    border-radius: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 1) !important;
    color: #333 !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  .youtube-action-btn-small:hover {
    background: rgba(0, 123, 255, 0.1) !important;
    border-color: rgba(0, 123, 255, 0.5) !important;
    transform: translateY(-1px) !important;
  }

  .youtube-action-btn-small.active {
    background: rgba(0, 123, 255, 0.9) !important;
    color: white !important;
    border-color: rgba(0, 123, 255, 1) !important;
  }

  .pagination-top {
    margin: 0.5rem 0;
    padding: 0.75rem;
  }

  .video-player-overlay {
    border-radius: 8px 8px 0 0;
  }

  .inline-video-player video,
  .inline-video-player iframe {
    height: 150px;
    border-radius: 8px;
  }

  .inline-video-player {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced Comments Section for Mobile */
  .youtube-comments-section {
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin-top: 0.5rem !important;
    margin-bottom: 1rem !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    max-height: 300px !important;
    overflow-y: auto !important;
    position: relative !important;
    z-index: 10 !important;
    width: 100% !important;
  }

  .youtube-comments-header {
    font-size: 1rem !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
    color: white !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding-bottom: 0.5rem !important;
  }

  .youtube-comment {
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 6px !important;
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    transition: all 0.2s ease !important;
  }

  .youtube-comment:hover {
    background: rgba(255, 255, 255, 0.08) !important;
    transform: translateY(-1px) !important;
  }

  .youtube-comment-text {
    color: white !important;
    line-height: 1.4 !important;
    margin: 0.5rem 0 !important;
    font-size: 0.9rem !important;
  }

  .youtube-comment-actions {
    display: flex !important;
    gap: 0.75rem !important;
    margin-top: 0.5rem !important;
  }

  .youtube-comment-action {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    transition: all 0.2s ease !important;
  }

  .youtube-comment-action:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px) !important;
  }

  /* Mobile Comment Input and List */
  .youtube-comment-input {
    display: flex !important;
    gap: 0.75rem !important;
    margin-bottom: 1rem !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 8px !important;
    padding: 0.75rem !important;
  }

  .youtube-comment-input-field {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border-radius: 6px !important;
    padding: 0.5rem !important;
    font-size: 0.9rem !important;
    width: 100% !important;
  }

  .youtube-comment-input-field::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  .youtube-comments-list {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .youtube-comment-avatar {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.2) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-weight: 600 !important;
    font-size: 0.8rem !important;
  }

  .youtube-comment-content {
    flex: 1 !important;
    color: white !important;
  }

  .youtube-comment-author {
    color: white !important;
    font-weight: 600 !important;
    font-size: 0.85rem !important;
  }

  .youtube-comment-time {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 0.75rem !important;
    margin-left: 0.5rem !important;
  }
}

/* Tablet Responsive Design - Vertical Single Column Layout */
@media (min-width: 768px) and (max-width: 1023px) {
  .video-lessons-container {
    padding: 0 2rem;
  }

  .videos-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 3rem;
    width: 100%;
    max-width: 100%;
    align-items: center;
  }

  .video-card {
    width: 90%;
    max-width: 1200px;
    border-radius: 12px;
    overflow: visible;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .video-card-thumbnail {
    width: 100%;
    aspect-ratio: 4/3;
    object-fit: cover;
  }

  .video-title {
    font-size: 1.9rem;
    line-height: 1.3;
    margin-bottom: 1rem;
  }

  /* Enhanced Video Info Section for Tablets */
  .youtube-video-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    backdrop-filter: blur(10px);
  }

  .youtube-video-actions-horizontal {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-top: 0.5rem;
  }

  .youtube-action-btn-small {
    flex: 1;
    padding: 1.2rem;
    font-size: 1.7rem;
    text-align: center;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    transition: all 0.2s ease;
  }

  .youtube-action-btn-small:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  /* Enhanced Comments Section for Tablets */
  .youtube-comments-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-height: 500px;
    overflow-y: auto;
  }

  .youtube-comments-header {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.5rem;
  }

  .pagination-container {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    padding: 1rem;
  }

  .pagination-controls {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
  }

  .pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    min-width: 40px;
  }

  .inline-video-player video,
  .inline-video-player iframe {
    aspect-ratio: 4/3;
    height: auto;
  }
}

/* Laptop Responsive Design - Vertical Single Column Layout */
@media (min-width: 1024px) and (max-width: 1439px) {
  .video-lessons-container {
    padding: 0 3rem;
  }

  .videos-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 4rem;
    width: 100%;
    max-width: 100%;
    align-items: center;
  }

  .video-card {
    width: 85%;
    max-width: 1600px;
    border-radius: 12px;
    overflow: visible;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .video-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  .video-card-thumbnail {
    width: 100%;
    aspect-ratio: 4/3;
    object-fit: cover;
    border-radius: 12px 12px 0 0;
  }

  .video-title {
    font-size: 2rem;
    line-height: 1.4;
    margin-bottom: 1rem;
  }

  /* Enhanced Video Info Section for Laptops */
  .youtube-video-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .youtube-video-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: white;
  }

  .youtube-video-actions-horizontal {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-top: 1rem;
  }

  .youtube-action-btn-small {
    flex: 1;
    padding: 1.5rem;
    font-size: 1.8rem;
    text-align: center;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    transition: all 0.2s ease;
    font-weight: 500;
  }

  .youtube-action-btn-small:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  /* Enhanced Comments Section for Laptops */
  .youtube-comments-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 2rem;
    margin-top: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-height: 600px;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .youtube-comments-header {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.75rem;
  }

  .youtube-comment-input {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .pagination-container {
    flex-direction: row;
    justify-content: center;
    gap: 1rem;
    padding: 1.5rem;
  }

  .pagination-controls {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
  }

  .pagination-btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
    min-width: 44px;
  }

  .inline-video-player video,
  .inline-video-player iframe {
    aspect-ratio: 4/3;
    height: auto;
  }
}

/* Force Grid Layout Override for All Devices */
.videos-grid {
  display: grid !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* Mobile Override - 1 Column */
@media (max-width: 767px) {
  .videos-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }
}

/* Tablet Override - Vertical Single Column */
@media (min-width: 768px) and (max-width: 1023px) {
  .videos-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 1.5rem !important;
    width: 100% !important;
    align-items: center !important;
  }
}

/* Laptop Override - Vertical Single Column */
@media (min-width: 1024px) and (max-width: 1439px) {
  .videos-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
    width: 100% !important;
    align-items: center !important;
  }
}

/* Desktop Override - Vertical Single Column */
@media (min-width: 1440px) and (max-width: 1919px) {
  .videos-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 2.5rem !important;
    width: 100% !important;
    align-items: center !important;
  }
}

/* Large Desktop Override - Vertical Single Column */
@media (min-width: 1920px) {
  .video-lessons-container {
    padding: 0 5rem;
  }

  .videos-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 3rem !important;
    width: 100% !important;
    align-items: center !important;
  }

  .video-card {
    width: 75%;
    max-width: 2400px;
    border-radius: 12px;
    overflow: visible;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .video-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
  }

  .video-card-thumbnail {
    width: 100%;
    aspect-ratio: 4/3;
    object-fit: cover;
    border-radius: 12px 12px 0 0;
  }
}

/* Video Card Responsive Improvements */
.video-card {
  width: 100% !important;
  max-width: none !important;
  box-sizing: border-box !important;
}

.video-card-thumbnail {
  width: 100% !important;
  object-fit: cover !important;
}

/* Container Responsive Improvements */
.video-lessons-content {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Enhanced Comments Section - General Improvements */
.youtube-comments-section {
  position: relative;
  z-index: 10;
  margin-top: 1rem;
}

.youtube-comment {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.youtube-comment:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
}

.youtube-comment-text {
  color: white;
  line-height: 1.5;
  margin: 0.5rem 0;
}

.youtube-comment-actions {
  display: flex;
  gap: 1rem;
  margin-top: 0.75rem;
}

.youtube-comment-action {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.youtube-comment-action:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Mobile Pagination Force Horizontal - Show All Buttons */
@media (max-width: 767px) {
  .pagination-container {
    display: flex !important;
    flex-direction: column !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin: 1rem 0 !important;
  }

  .pagination-info {
    font-size: 0.8rem !important;
    text-align: center !important;
    margin-bottom: 0.75rem !important;
    color: white !important;
    opacity: 0.9 !important;
  }

  .pagination-controls {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    width: 100% !important;
    gap: 0.5rem !important;
    padding: 0.25rem !important;
  }

  .pagination-btn {
    flex-shrink: 0 !important;
    white-space: nowrap !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.8rem !important;
    min-width: 40px !important;
    height: 36px !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    margin: 0.125rem !important;
  }

  .pagination-btn.active {
    background: #007BFF !important;
    border-color: #007BFF !important;
    color: white !important;
  }

  .pagination-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
  }

  .page-size-selector {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    margin-top: 0.75rem !important;
    font-size: 0.8rem !important;
    color: white !important;
  }

  .page-size-selector select {
    flex-shrink: 0 !important;
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    background: rgba(0, 0, 0, 0.3) !important;
    color: white !important;
  }
}
