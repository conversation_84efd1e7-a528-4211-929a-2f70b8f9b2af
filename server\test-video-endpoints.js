const axios = require('axios');

console.log('🎥 Testing Video Endpoints...\n');

// Test video endpoints
async function testVideoEndpoints() {
  try {
    console.log('1️⃣ Testing videos-subtitle-status endpoint...');
    
    // This endpoint requires authentication, so let's test without auth first
    try {
      const response = await axios.get('http://localhost:5000/api/study/videos-subtitle-status');
      console.log('✅ Videos endpoint (no auth): PASSED');
      console.log(`   Status: ${response.status}`);
      console.log(`   Videos found: ${response.data?.data?.length || 0}`);
      if (response.data?.data?.length > 0) {
        console.log(`   Sample video: ${response.data.data[0].title}`);
        console.log(`   Has thumbnail: ${response.data.data[0].thumbnail ? 'YES' : 'NO'}`);
      }
    } catch (error) {
      console.log('❌ Videos endpoint (no auth): FAILED');
      console.log(`   Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
      
      if (error.response?.status === 401) {
        console.log('   This endpoint requires authentication');
      }
    }
    
    console.log('\n2️⃣ Testing study content endpoint...');
    
    // Test study content endpoint
    try {
      const response = await axios.post('http://localhost:5000/api/study/get-study-content', {
        content: 'videos',
        className: 'all',
        subject: 'all'
      });
      console.log('✅ Study content endpoint (no auth): PASSED');
      console.log(`   Status: ${response.status}`);
      console.log(`   Videos found: ${response.data?.data?.length || 0}`);
    } catch (error) {
      console.log('❌ Study content endpoint (no auth): FAILED');
      console.log(`   Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
      
      if (error.response?.status === 401) {
        console.log('   This endpoint requires authentication');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testVideoEndpoints();
