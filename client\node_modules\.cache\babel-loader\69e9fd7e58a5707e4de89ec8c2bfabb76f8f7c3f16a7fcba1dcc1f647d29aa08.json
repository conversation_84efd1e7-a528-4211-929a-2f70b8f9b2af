{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef, startTransition } from \"react\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport AdminNavigation from \"./AdminNavigation\";\nimport ModernSidebar from \"./ModernSidebar\";\nimport { TbHome, TbBrandTanzania, TbMenu2, TbX, TbChevronDown, TbLogout, TbUser, TbSettings, TbBell, TbStar } from \"react-icons/tb\";\nimport OnlineStatusIndicator from './common/OnlineStatusIndicator';\nimport NotificationBell from './common/NotificationBell';\nimport ProfilePicture from './common/ProfilePicture';\nimport FloatingBrainwaveAI from './FloatingBrainwaveAI';\nimport { setUserOnline, setUserOffline, sendHeartbeat } from '../apicalls/notifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const intervalRef = useRef(null);\n  const heartbeatRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Check if current page should show floating AI (exclude quiz, results, plans, and profile pages)\n  const shouldShowFloatingAI = () => {\n    const currentPath = location.pathname;\n    const excludedPaths = ['/user/quiz', '/user/quiz/', '/quiz', '/quiz/', '/results', '/results/', '/user/results', '/user/results/', '/user/plans', '/user/plans/', '/plans', '/plans/', '/profile', '/profile/', '/user/profile', '/user/profile/'];\n\n    // Check if current path starts with any excluded path or contains quiz/result keywords\n    return !excludedPaths.some(path => currentPath.includes(path)) && !currentPath.includes('quiz') && !currentPath.includes('result') && !currentPath.includes('plans') && !currentPath.includes('profile');\n  };\n  const activeRoute = location.pathname;\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n\n        // Store user data in localStorage for consistency\n        localStorage.setItem(\"user\", JSON.stringify(response.data));\n\n        // Debug log to help identify admin login issues\n        console.log(\"User data loaded:\", {\n          name: response.data.name,\n          isAdmin: response.data.isAdmin,\n          email: response.data.email\n        });\n      } else {\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      // Check if user data already exists in Redux (from login)\n      if (!user) {\n        // Try to load user from localStorage first\n        const storedUser = localStorage.getItem(\"user\");\n        if (storedUser) {\n          try {\n            const userData = JSON.parse(storedUser);\n            console.log(\"ProtectedRoute: Loading user from localStorage\", {\n              name: userData.name,\n              isAdmin: userData.isAdmin\n            });\n            dispatch(SetUser(userData));\n          } catch (error) {\n            console.log(\"ProtectedRoute: Error parsing stored user data, fetching from server\");\n            getUserData();\n          }\n        } else {\n          console.log(\"ProtectedRoute: No user in Redux or localStorage, fetching from server\");\n          getUserData();\n        }\n      } else {\n        console.log(\"ProtectedRoute: User already in Redux\", {\n          name: user.name,\n          isAdmin: user.isAdmin\n        });\n      }\n    } else {\n      navigate(\"/login\");\n    }\n  }, []);\n  useEffect(() => {\n    // Allow access to profile page, subscription page, and logout for all users\n    const allowedRoutes = ['/user/profile', '/profile', '/subscription', '/user/subscription', '/logout'];\n    const isAllowedRoute = allowedRoutes.some(route => activeRoute.includes(route));\n\n    // Redirect users with paymentRequired or no subscription to subscription page\n    if (isPaymentPending && !isAllowedRoute && (user !== null && user !== void 0 && user.paymentRequired || (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'free' || !(user !== null && user !== void 0 && user.subscriptionStatus)) && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Redirecting user to subscription page - paymentRequired:\", user === null || user === void 0 ? void 0 : user.paymentRequired);\n      navigate('/subscription'); // Redirect to subscription page to choose plan\n    }\n  }, [isPaymentPending, activeRoute, navigate, user]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        // Set payment pending if user has paymentRequired or no subscription\n        if ((user !== null && user !== void 0 && user.paymentRequired || (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'free' || !(user !== null && user !== void 0 && user.subscriptionStatus)) && !(user !== null && user !== void 0 && user.isAdmin)) {\n          setIsPaymentPending(true);\n        } else {\n          setIsPaymentPending(false); // User has active subscription, allow access\n        }\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      // Set payment pending if user has paymentRequired or no subscription\n      if ((user !== null && user !== void 0 && user.paymentRequired || (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'free' || !(user !== null && user !== void 0 && user.subscriptionStatus)) && !(user !== null && user !== void 0 && user.isAdmin)) {\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false); // User has active subscription, allow access\n      }\n    }\n  };\n\n  useEffect(() => {\n    // Verify payment for users with paymentRequired or no subscription\n    if (user && !(user !== null && user !== void 0 && user.isAdmin) && (user !== null && user !== void 0 && user.paymentRequired || (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'free' || !(user !== null && user !== void 0 && user.subscriptionStatus))) {\n      console.log(\"Effect Running - checking payment for user with paymentRequired:\", user === null || user === void 0 ? void 0 : user.paymentRequired);\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    } else {\n      // For users with active subscription, ensure they have access\n      setIsPaymentPending(false);\n    }\n  }, [paymentVerificationNeeded, user]);\n  useEffect(() => {\n    // Verify payment for users with paymentRequired or no subscription\n    if (user && !(user !== null && user !== void 0 && user.isAdmin) && (user !== null && user !== void 0 && user.paymentRequired || (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'free' || !(user !== null && user !== void 0 && user.subscriptionStatus))) {\n      console.log(\"Effect Running - verifying payment status for user with paymentRequired:\", user === null || user === void 0 ? void 0 : user.paymentRequired);\n      verifyPaymentStatus();\n    } else {\n      // For users with active subscription, ensure they have access\n      setIsPaymentPending(false);\n    }\n  }, [user, activeRoute]);\n\n  // Online status management\n  useEffect(() => {\n    if (user && !user.isAdmin) {\n      // Set user as online when component mounts\n      setUserOnline().catch(console.error);\n\n      // Send heartbeat every 2 minutes\n      heartbeatRef.current = setInterval(() => {\n        sendHeartbeat().catch(console.error);\n      }, 120000); // 2 minutes\n\n      // Set user as offline when component unmounts or page unloads\n      const handleBeforeUnload = () => {\n        setUserOffline().catch(console.error);\n      };\n      window.addEventListener('beforeunload', handleBeforeUnload);\n      return () => {\n        if (heartbeatRef.current) {\n          clearInterval(heartbeatRef.current);\n        }\n        window.removeEventListener('beforeunload', handleBeforeUnload);\n        setUserOffline().catch(console.error);\n      };\n    }\n  }, [user]);\n  const getButtonClass = title => {\n    // Always allow access to Profile, Subscription/Plans, and Logout\n    if (title === \"Plans\" || title === \"Profile\" || title === \"Logout\" || title === \"Subscription\") {\n      return \"\"; // No class applied\n    }\n\n    // Disable buttons for users with paymentRequired or no subscription\n    if ((user !== null && user !== void 0 && user.paymentRequired || (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'free' || !(user !== null && user !== void 0 && user.subscriptionStatus)) && !(user !== null && user !== void 0 && user.isAdmin)) {\n      return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" ? \"button-disabled\" : \"\";\n    }\n\n    // Users with active subscription can access all features\n    return \"\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @media (max-width: 768px) {\n          /* Reset all old mobile header styles */\n          .nav-modern, header, .safe-header-animation {\n            all: unset !important;\n          }\n\n          .lg\\\\:hidden {\n            all: unset !important;\n          }\n\n          /* Hide old sidebar */\n          .sidebar, .mobile-sidebar, .modern-sidebar {\n            display: none !important;\n          }\n\n          /* Ensure flag image displays properly */\n          img[alt*=\"flag\"] {\n            display: block !important;\n            visibility: visible !important;\n            opacity: 1 !important;\n          }\n        }\n\n        /* Bell positioning - mobile vs tablet */\n        @media (max-width: 640px) {\n          .mobile-bell-left {\n            display: block !important;\n          }\n          .mobile-bell-right {\n            display: none !important;\n          }\n        }\n\n        /* Hub page specific styles handled in Hub.css */\n\n        @media (min-width: 641px) and (max-width: 768px) {\n          .mobile-bell-left {\n            display: none !important;\n          }\n          .mobile-bell-right {\n            display: block !important;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(ModernSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 26\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: `nav-modern safe-header-animation ${location.pathname.includes('/write-exam') || location.pathname.includes('/take-quiz/') ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98' : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'} backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`,\n        style: {\n          minHeight: '48px',\n          height: '48px',\n          maxHeight: '48px',\n          padding: '0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"block md:hidden\",\n          style: {\n            padding: '0 16px',\n            height: '60px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              height: '60px',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: '0 0 auto',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300\",\n                onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n                style: {\n                  width: '40px',\n                  height: '40px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  background: 'rgba(255, 255, 255, 0.9)',\n                  borderRadius: '12px',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',\n                  border: '1px solid rgba(0, 0, 0, 0.05)'\n                },\n                children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 39\n                }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 59\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mobile-bell-left\",\n                style: {\n                  transform: 'scale(0.8)'\n                },\n                children: /*#__PURE__*/_jsxDEV(NotificationBell, {\n                  unreadCount: 2\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: '1 1 auto',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://flagcdn.com/w40/tz.png\",\n                alt: \"Tanzania flag\",\n                style: {\n                  width: '28px',\n                  height: '20px',\n                  borderRadius: '3px',\n                  objectFit: 'cover'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                style: {\n                  fontSize: '1.2rem',\n                  fontWeight: '800',\n                  margin: '0',\n                  lineHeight: '1.2',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#000000'\n                  },\n                  children: \"BRAIN\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#22c55e'\n                  },\n                  children: \"WAVE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: '0 0 auto',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'flex-end',\n                  textAlign: 'right'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.8rem',\n                    fontWeight: '600',\n                    color: '#374151',\n                    lineHeight: '1.1',\n                    margin: '0',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.7rem',\n                    color: '#6b7280',\n                    lineHeight: '1.1',\n                    margin: '0',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : user === null || user === void 0 ? void 0 : user.class\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this), !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mobile-bell-right\",\n                style: {\n                  transform: 'scale(0.8)'\n                },\n                children: /*#__PURE__*/_jsxDEV(NotificationBell, {\n                  unreadCount: 2\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: {\n                  ...user,\n                  isOnline: true,\n                  lastActivity: new Date().toISOString()\n                },\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px',\n                  flexShrink: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 max-w-6xl mx-auto\",\n          style: {\n            padding: '0 1rem',\n            maxWidth: '1200px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            style: {\n              height: '48px',\n              minHeight: '48px',\n              maxHeight: '48px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center flex-1\",\n              style: {\n                position: 'absolute',\n                left: '50%',\n                top: '50%',\n                transform: 'translate(-50%, -50%)',\n                zIndex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative group flex items-center space-x-2 sm:space-x-3 safe-center-animation\",\n                style: {\n                  alignItems: 'center',\n                  gap: '8px',\n                  height: 'auto',\n                  display: 'flex'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                  style: {\n                    width: '24px',\n                    height: '18px',\n                    flexShrink: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"https://flagcdn.com/w40/tz.png\",\n                    alt: \"Tanzania Flag\",\n                    className: \"w-full h-full object-cover\",\n                    style: {\n                      objectFit: 'cover'\n                    },\n                    onError: e => {\n                      // Fallback to another flag source if first fails\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                      e.target.onerror = () => {\n                        // Final fallback - hide image and show text\n                        e.target.style.display = 'none';\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                      };\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative brainwave-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-base sm:text-lg md:text-xl font-black tracking-tight relative z-10 select-none\",\n                    style: {\n                      fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                      letterSpacing: '-0.02em',\n                      fontSize: '1.1rem',\n                      lineHeight: '1.2',\n                      margin: '0',\n                      padding: '0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"relative inline-block brain-text\",\n                      style: {\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)',\n                        animation: 'brainGlow 3s ease-in-out infinite'\n                      },\n                      children: [\"Brain\", /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full electric-spark\",\n                        style: {\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6',\n                          animation: 'sparkPulse 2s ease-in-out infinite'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 531,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"relative inline-block wave-text\",\n                      style: {\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)',\n                        animation: 'waveFlow 3s ease-in-out infinite'\n                      },\n                      children: [\"wave\", /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full wave-particle\",\n                        style: {\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981',\n                          animation: 'waveParticle 3s ease-in-out infinite'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 554,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute -bottom-1 left-0 h-1 rounded-full glowing-underline\",\n                    style: {\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)',\n                      width: '100%',\n                      animation: 'underlineGlow 3s ease-in-out infinite'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                  style: {\n                    background: '#f0f0f0',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                    width: '24px',\n                    height: '24px',\n                    flexShrink: 0\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/favicon.png\",\n                    alt: \"Brainwave Logo\",\n                    className: \"w-full h-full object-cover\",\n                    style: {\n                      objectFit: 'cover'\n                    },\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                    style: {\n                      display: 'none',\n                      fontSize: '12px'\n                    },\n                    children: \"\\uD83E\\uDDE0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end space-x-2 sm:space-x-3 flex-1\",\n              style: {\n                position: 'absolute',\n                right: '0',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                zIndex: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px'\n              },\n              children: [!(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"safe-notification-animation\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    transform: 'scale(0.8)',\n                    transformOrigin: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(NotificationBell, {\n                    unreadCount: 2\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 group safe-profile-animation cursor-pointer\",\n                onClick: () => navigate('/profile'),\n                title: \"Go to Profile\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '6px',\n                  flexDirection: 'row'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right mobile-profile-info\",\n                  style: {\n                    display: 'flex !important',\n                    flexDirection: 'column !important',\n                    alignItems: 'flex-end !important',\n                    textAlign: 'right !important',\n                    marginRight: '4px !important'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\",\n                    style: {\n                      display: 'block !important',\n                      fontSize: '0.75rem !important',\n                      fontWeight: '600 !important',\n                      color: '#374151 !important',\n                      lineHeight: '1.1 !important',\n                      margin: '0 !important',\n                      whiteSpace: 'nowrap !important'\n                    },\n                    children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\",\n                    style: {\n                      display: 'block !important',\n                      fontSize: '0.7rem !important',\n                      color: '#6b7280 !important',\n                      lineHeight: '1.1 !important',\n                      margin: '0 !important',\n                      whiteSpace: 'nowrap !important'\n                    },\n                    children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : user === null || user === void 0 ? void 0 : user.class\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ProfilePicture, {\n                  user: {\n                    ...user,\n                    isOnline: true,\n                    lastActivity: new Date().toISOString()\n                  },\n                  size: \"sm\",\n                  showOnlineStatus: true,\n                  style: {\n                    width: '28px',\n                    height: '28px',\n                    flexShrink: 0\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hidden md:block\",\n                  children: /*#__PURE__*/_jsxDEV(TbUser, {\n                    className: \"text-gray-400 group-hover:text-blue-500 transition-colors duration-300 text-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `flex-1 overflow-auto ${user !== null && user !== void 0 && user.isAdmin ? 'bg-gray-100' : 'bg-gradient-to-br from-gray-50 to-blue-50'} ${user !== null && user !== void 0 && user.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full safe-content-animation\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 730,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 725,\n        columnNumber: 9\n      }, this), shouldShowFloatingAI() && /*#__PURE__*/_jsxDEV(FloatingBrainwaveAI, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 36\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"6LpBMn9tTcaYhjWx5CpAtcW9U1o=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "startTransition", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "AdminNavigation", "ModernSidebar", "TbHome", "TbBrandTanzania", "TbMenu2", "TbX", "TbChevronDown", "TbLogout", "TbUser", "TbSettings", "TbBell", "TbStar", "OnlineStatusIndicator", "NotificationBell", "ProfilePicture", "FloatingBrainwaveAI", "setUserOnline", "setUserOffline", "sendHeartbeat", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "state", "isPaymentPending", "setIsPaymentPending", "isMobileMenuOpen", "setIsMobileMenuOpen", "intervalRef", "heartbeatRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "location", "shouldShowFloatingAI", "currentPath", "pathname", "excludedPaths", "some", "path", "includes", "activeRoute", "getUserData", "response", "success", "data", "localStorage", "setItem", "JSON", "stringify", "console", "log", "name", "isAdmin", "email", "error", "token", "getItem", "storedUser", "userData", "parse", "allowedRoutes", "isAllowedRoute", "route", "paymentRequired", "subscriptionStatus", "verifyPaymentStatus", "paymentStatus", "current", "clearInterval", "setInterval", "catch", "handleBeforeUnload", "window", "addEventListener", "removeEventListener", "getButtonClass", "title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "minHeight", "height", "maxHeight", "padding", "display", "alignItems", "justifyContent", "width", "flex", "gap", "onClick", "background", "borderRadius", "boxShadow", "border", "size", "transform", "unreadCount", "src", "alt", "objectFit", "fontSize", "fontWeight", "margin", "lineHeight", "whiteSpace", "color", "flexDirection", "textAlign", "level", "class", "isOnline", "lastActivity", "Date", "toISOString", "showOnlineStatus", "flexShrink", "max<PERSON><PERSON><PERSON>", "position", "left", "top", "zIndex", "onError", "e", "target", "onerror", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "textShadow", "animation", "backgroundColor", "nextS<PERSON>ling", "right", "transform<PERSON><PERSON>in", "marginRight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef, startTransition } from \"react\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport AdminNavigation from \"./AdminNavigation\";\r\nimport ModernSidebar from \"./ModernSidebar\";\r\n\r\nimport { TbHome, TbBrandTanzania, TbMenu2, TbX, TbChevronDown, TbL<PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ell, TbStar } from \"react-icons/tb\";\r\nimport OnlineStatusIndicator from './common/OnlineStatusIndicator';\r\nimport NotificationBell from './common/NotificationBell';\r\nimport ProfilePicture from './common/ProfilePicture';\r\nimport FloatingBrainwaveAI from './FloatingBrainwaveAI';\r\nimport { setUserOnline, setUserOffline, sendHeartbeat } from '../apicalls/notifications';\r\n\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const heartbeatRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n\r\n  // Check if current page should show floating AI (exclude quiz, results, plans, and profile pages)\r\n  const shouldShowFloatingAI = () => {\r\n    const currentPath = location.pathname;\r\n    const excludedPaths = [\r\n      '/user/quiz',\r\n      '/user/quiz/',\r\n      '/quiz',\r\n      '/quiz/',\r\n      '/results',\r\n      '/results/',\r\n      '/user/results',\r\n      '/user/results/',\r\n      '/user/plans',\r\n      '/user/plans/',\r\n      '/plans',\r\n      '/plans/',\r\n      '/profile',\r\n      '/profile/',\r\n      '/user/profile',\r\n      '/user/profile/'\r\n    ];\r\n\r\n    // Check if current path starts with any excluded path or contains quiz/result keywords\r\n    return !excludedPaths.some(path => currentPath.includes(path)) &&\r\n           !currentPath.includes('quiz') &&\r\n           !currentPath.includes('result') &&\r\n           !currentPath.includes('plans') &&\r\n           !currentPath.includes('profile');\r\n  };\r\n  const activeRoute = location.pathname;\r\n\r\n\r\n\r\n\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n\r\n        // Store user data in localStorage for consistency\r\n        localStorage.setItem(\"user\", JSON.stringify(response.data));\r\n\r\n        // Debug log to help identify admin login issues\r\n        console.log(\"User data loaded:\", {\r\n          name: response.data.name,\r\n          isAdmin: response.data.isAdmin,\r\n          email: response.data.email\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      // Check if user data already exists in Redux (from login)\r\n      if (!user) {\r\n        // Try to load user from localStorage first\r\n        const storedUser = localStorage.getItem(\"user\");\r\n        if (storedUser) {\r\n          try {\r\n            const userData = JSON.parse(storedUser);\r\n            console.log(\"ProtectedRoute: Loading user from localStorage\", { name: userData.name, isAdmin: userData.isAdmin });\r\n            dispatch(SetUser(userData));\r\n          } catch (error) {\r\n            console.log(\"ProtectedRoute: Error parsing stored user data, fetching from server\");\r\n            getUserData();\r\n          }\r\n        } else {\r\n          console.log(\"ProtectedRoute: No user in Redux or localStorage, fetching from server\");\r\n          getUserData();\r\n        }\r\n      } else {\r\n        console.log(\"ProtectedRoute: User already in Redux\", { name: user.name, isAdmin: user.isAdmin });\r\n      }\r\n    } else {\r\n      navigate(\"/login\");\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    // Allow access to profile page, subscription page, and logout for all users\r\n    const allowedRoutes = ['/user/profile', '/profile', '/subscription', '/user/subscription', '/logout'];\r\n    const isAllowedRoute = allowedRoutes.some(route => activeRoute.includes(route));\r\n\r\n    // Redirect users with paymentRequired or no subscription to subscription page\r\n    if (isPaymentPending && !isAllowedRoute &&\r\n        (user?.paymentRequired || user?.subscriptionStatus === 'free' || !user?.subscriptionStatus) &&\r\n        !user?.isAdmin) {\r\n      console.log(\"Redirecting user to subscription page - paymentRequired:\", user?.paymentRequired);\r\n      navigate('/subscription'); // Redirect to subscription page to choose plan\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate, user]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        // Set payment pending if user has paymentRequired or no subscription\r\n        if ((user?.paymentRequired || user?.subscriptionStatus === 'free' || !user?.subscriptionStatus) && !user?.isAdmin) {\r\n          setIsPaymentPending(true);\r\n        } else {\r\n          setIsPaymentPending(false); // User has active subscription, allow access\r\n        }\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      // Set payment pending if user has paymentRequired or no subscription\r\n      if ((user?.paymentRequired || user?.subscriptionStatus === 'free' || !user?.subscriptionStatus) && !user?.isAdmin) {\r\n        setIsPaymentPending(true);\r\n      } else {\r\n        setIsPaymentPending(false); // User has active subscription, allow access\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Verify payment for users with paymentRequired or no subscription\r\n    if (user && !user?.isAdmin && (user?.paymentRequired || user?.subscriptionStatus === 'free' || !user?.subscriptionStatus)) {\r\n      console.log(\"Effect Running - checking payment for user with paymentRequired:\", user?.paymentRequired);\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    } else {\r\n      // For users with active subscription, ensure they have access\r\n      setIsPaymentPending(false);\r\n    }\r\n  }, [paymentVerificationNeeded, user]);\r\n\r\n  useEffect(() => {\r\n    // Verify payment for users with paymentRequired or no subscription\r\n    if (user && !user?.isAdmin && (user?.paymentRequired || user?.subscriptionStatus === 'free' || !user?.subscriptionStatus)) {\r\n      console.log(\"Effect Running - verifying payment status for user with paymentRequired:\", user?.paymentRequired);\r\n      verifyPaymentStatus();\r\n    } else {\r\n      // For users with active subscription, ensure they have access\r\n      setIsPaymentPending(false);\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n  // Online status management\r\n  useEffect(() => {\r\n    if (user && !user.isAdmin) {\r\n      // Set user as online when component mounts\r\n      setUserOnline().catch(console.error);\r\n\r\n      // Send heartbeat every 2 minutes\r\n      heartbeatRef.current = setInterval(() => {\r\n        sendHeartbeat().catch(console.error);\r\n      }, 120000); // 2 minutes\r\n\r\n      // Set user as offline when component unmounts or page unloads\r\n      const handleBeforeUnload = () => {\r\n        setUserOffline().catch(console.error);\r\n      };\r\n\r\n      window.addEventListener('beforeunload', handleBeforeUnload);\r\n\r\n      return () => {\r\n        if (heartbeatRef.current) {\r\n          clearInterval(heartbeatRef.current);\r\n        }\r\n        window.removeEventListener('beforeunload', handleBeforeUnload);\r\n        setUserOffline().catch(console.error);\r\n      };\r\n    }\r\n  }, [user]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Always allow access to Profile, Subscription/Plans, and Logout\r\n    if (title === \"Plans\" || title === \"Profile\" || title === \"Logout\" || title === \"Subscription\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    // Disable buttons for users with paymentRequired or no subscription\r\n    if ((user?.paymentRequired || user?.subscriptionStatus === 'free' || !user?.subscriptionStatus) && !user?.isAdmin) {\r\n      return subscriptionData?.paymentStatus !== \"paid\" ? \"button-disabled\" : \"\";\r\n    }\r\n\r\n    // Users with active subscription can access all features\r\n    return \"\";\r\n  };\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* CSS Override to fix mobile header issues */}\r\n      <style>{`\r\n        @media (max-width: 768px) {\r\n          /* Reset all old mobile header styles */\r\n          .nav-modern, header, .safe-header-animation {\r\n            all: unset !important;\r\n          }\r\n\r\n          .lg\\\\:hidden {\r\n            all: unset !important;\r\n          }\r\n\r\n          /* Hide old sidebar */\r\n          .sidebar, .mobile-sidebar, .modern-sidebar {\r\n            display: none !important;\r\n          }\r\n\r\n          /* Ensure flag image displays properly */\r\n          img[alt*=\"flag\"] {\r\n            display: block !important;\r\n            visibility: visible !important;\r\n            opacity: 1 !important;\r\n          }\r\n        }\r\n\r\n        /* Bell positioning - mobile vs tablet */\r\n        @media (max-width: 640px) {\r\n          .mobile-bell-left {\r\n            display: block !important;\r\n          }\r\n          .mobile-bell-right {\r\n            display: none !important;\r\n          }\r\n        }\r\n\r\n        /* Hub page specific styles handled in Hub.css */\r\n\r\n        @media (min-width: 641px) and (max-width: 768px) {\r\n          .mobile-bell-left {\r\n            display: none !important;\r\n          }\r\n          .mobile-bell-right {\r\n            display: block !important;\r\n          }\r\n        }\r\n      `}</style>\r\n\r\n      {/* Modern Sidebar for regular users */}\r\n      {!user?.isAdmin && <ModernSidebar />}\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex-1 flex flex-col min-h-screen\">\r\n        {/* Modern Responsive Header - Show for all users */}\r\n        {(\r\n          <header\r\n            className={`nav-modern safe-header-animation ${\r\n              location.pathname.includes('/write-exam') || location.pathname.includes('/take-quiz/')\r\n                ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'\r\n                : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'\r\n            } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}\r\n            style={{\r\n              minHeight: '48px',\r\n              height: '48px',\r\n              maxHeight: '48px',\r\n              padding: '0'\r\n            }}\r\n          >\r\n          {/* Mobile Header Design - Bell on left for mobile only */}\r\n          <div className=\"block md:hidden\" style={{ padding: '0 16px', height: '60px' }}>\r\n            <div style={{\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'space-between',\r\n              height: '60px',\r\n              width: '100%'\r\n            }}>\r\n              {/* Left - Hamburger Menu + Bell (mobile only) */}\r\n              <div style={{\r\n                flex: '0 0 auto',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                gap: '8px'\r\n              }}>\r\n                <button\r\n                  className=\"p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300\"\r\n                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\r\n                  style={{\r\n                    width: '40px',\r\n                    height: '40px',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center',\r\n                    background: 'rgba(255, 255, 255, 0.9)',\r\n                    borderRadius: '12px',\r\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',\r\n                    border: '1px solid rgba(0, 0, 0, 0.05)'\r\n                  }}\r\n                >\r\n                  {isMobileMenuOpen ? <TbX size={20} /> : <TbMenu2 size={20} />}\r\n                </button>\r\n\r\n                {/* Notification Bell - Only on mobile screens (max-width: 640px) */}\r\n                {!user?.isAdmin && (\r\n                  <div className=\"mobile-bell-left\" style={{ transform: 'scale(0.8)' }}>\r\n                    <NotificationBell unreadCount={2} />\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Center - Flag + BRAINWAVE */}\r\n              <div style={{\r\n                flex: '1 1 auto',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                gap: '8px'\r\n              }}>\r\n                {/* Tanzania Flag */}\r\n                <img\r\n                  src=\"https://flagcdn.com/w40/tz.png\"\r\n                  alt=\"Tanzania flag\"\r\n                  style={{\r\n                    width: '28px',\r\n                    height: '20px',\r\n                    borderRadius: '3px',\r\n                    objectFit: 'cover'\r\n                  }}\r\n                />\r\n\r\n                {/* Simple BRAINWAVE Text - No weird animations */}\r\n                <h1 style={{\r\n                  fontSize: '1.2rem',\r\n                  fontWeight: '800',\r\n                  margin: '0',\r\n                  lineHeight: '1.2',\r\n                  whiteSpace: 'nowrap'\r\n                }}>\r\n                  <span style={{ color: '#000000' }}>BRAIN</span>\r\n                  <span style={{ color: '#22c55e' }}>WAVE</span>\r\n                </h1>\r\n              </div>\r\n\r\n              {/* Right - Profile Info + Bell (tablets) + Picture */}\r\n              <div style={{\r\n                flex: '0 0 auto',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                gap: '8px'\r\n              }}>\r\n                {/* Profile Info */}\r\n                <div style={{\r\n                  display: 'flex',\r\n                  flexDirection: 'column',\r\n                  alignItems: 'flex-end',\r\n                  textAlign: 'right'\r\n                }}>\r\n                  <div style={{\r\n                    fontSize: '0.8rem',\r\n                    fontWeight: '600',\r\n                    color: '#374151',\r\n                    lineHeight: '1.1',\r\n                    margin: '0',\r\n                    whiteSpace: 'nowrap'\r\n                  }}>\r\n                    {user?.name || 'User'}\r\n                  </div>\r\n                  <div style={{\r\n                    fontSize: '0.7rem',\r\n                    color: '#6b7280',\r\n                    lineHeight: '1.1',\r\n                    margin: '0',\r\n                    whiteSpace: 'nowrap'\r\n                  }}>\r\n                    {user?.level === 'primary' ? `Class ${user?.class}` : user?.class}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Notification Bell - On tablets (width > 640px) */}\r\n                {!user?.isAdmin && (\r\n                  <div className=\"mobile-bell-right\" style={{ transform: 'scale(0.8)' }}>\r\n                    <NotificationBell unreadCount={2} />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Profile Picture */}\r\n                <ProfilePicture\r\n                  user={{\r\n                    ...user,\r\n                    isOnline: true,\r\n                    lastActivity: new Date().toISOString()\r\n                  }}\r\n                  size=\"sm\"\r\n                  showOnlineStatus={true}\r\n                  style={{\r\n                    width: '32px',\r\n                    height: '32px',\r\n                    flexShrink: 0\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Desktop Header Design */}\r\n          <div className=\"hidden md:block px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 max-w-6xl mx-auto\" style={{ padding: '0 1rem', maxWidth: '1200px' }}>\r\n            <div className=\"flex items-center justify-between\" style={{ height: '48px', minHeight: '48px', maxHeight: '48px' }}>\r\n              {/* Left section - Empty for spacing */}\r\n              <div className=\"flex-1\"></div>\r\n\r\n              {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n              <div\r\n                className=\"flex justify-center flex-1\"\r\n                style={{\r\n                  position: 'absolute',\r\n                  left: '50%',\r\n                  top: '50%',\r\n                  transform: 'translate(-50%, -50%)',\r\n                  zIndex: 1\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"relative group flex items-center space-x-2 sm:space-x-3 safe-center-animation\"\r\n                  style={{\r\n                    alignItems: 'center',\r\n                    gap: '8px',\r\n                    height: 'auto',\r\n                    display: 'flex'\r\n                  }}\r\n                >\r\n                  {/* Tanzania Flag - Using actual flag image */}\r\n                  <div\r\n                    className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                    style={{\r\n                      width: '24px',\r\n                      height: '18px',\r\n                      flexShrink: 0\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"https://flagcdn.com/w40/tz.png\"\r\n                      alt=\"Tanzania Flag\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        // Fallback to another flag source if first fails\r\n                        e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                        e.target.onerror = () => {\r\n                          // Final fallback - hide image and show text\r\n                          e.target.style.display = 'none';\r\n                          e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                        };\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Amazing Animated Brainwave Text */}\r\n                  <div className=\"relative brainwave-container\">\r\n                    <h1 className=\"text-base sm:text-lg md:text-xl font-black tracking-tight relative z-10 select-none\"\r\n                        style={{\r\n                          fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                          letterSpacing: '-0.02em',\r\n                          fontSize: '1.1rem',\r\n                          lineHeight: '1.2',\r\n                          margin: '0',\r\n                          padding: '0'\r\n                        }}>\r\n                      {/* Brain - simplified safe animation */}\r\n                      <span\r\n                        className=\"relative inline-block brain-text\"\r\n                        style={{\r\n                          color: '#1f2937',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(59, 130, 246, 0.5)',\r\n                          animation: 'brainGlow 3s ease-in-out infinite'\r\n                        }}\r\n                      >\r\n                        Brain\r\n\r\n                        {/* Electric spark - CSS animation */}\r\n                        <div\r\n                          className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full electric-spark\"\r\n                          style={{\r\n                            backgroundColor: '#3b82f6',\r\n                            boxShadow: '0 0 10px #3b82f6',\r\n                            animation: 'sparkPulse 2s ease-in-out infinite'\r\n                          }}\r\n                        />\r\n                      </span>\r\n\r\n                      {/* Wave - simplified safe animation */}\r\n                      <span\r\n                        className=\"relative inline-block wave-text\"\r\n                        style={{\r\n                          color: '#059669',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                          animation: 'waveFlow 3s ease-in-out infinite'\r\n                        }}\r\n                      >\r\n                        wave\r\n\r\n                        {/* Wave particle - CSS animation */}\r\n                        <div\r\n                          className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full wave-particle\"\r\n                          style={{\r\n                            backgroundColor: '#10b981',\r\n                            boxShadow: '0 0 8px #10b981',\r\n                            animation: 'waveParticle 3s ease-in-out infinite'\r\n                          }}\r\n                        />\r\n                      </span>\r\n                    </h1>\r\n\r\n                    {/* Glowing underline effect - CSS animation */}\r\n                    <div\r\n                      className=\"absolute -bottom-1 left-0 h-1 rounded-full glowing-underline\"\r\n                      style={{\r\n                        background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                        boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)',\r\n                        width: '100%',\r\n                        animation: 'underlineGlow 3s ease-in-out infinite'\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Official Logo - Small like profile */}\r\n                  <div\r\n                    className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                    style={{\r\n                      background: '#f0f0f0',\r\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                      width: '24px',\r\n                      height: '24px',\r\n                      flexShrink: 0\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"/favicon.png\"\r\n                      alt=\"Brainwave Logo\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        e.target.style.display = 'none';\r\n                        e.target.nextSibling.style.display = 'flex';\r\n                      }}\r\n                    />\r\n                    <div\r\n                      className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                      style={{\r\n                        display: 'none',\r\n                        fontSize: '12px'\r\n                      }}\r\n                    >\r\n                      🧠\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Modern Glow Effect */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Right Section - Notifications + User Profile */}\r\n              <div\r\n                className=\"flex items-center justify-end space-x-2 sm:space-x-3 flex-1\"\r\n                style={{\r\n                  position: 'absolute',\r\n                  right: '0',\r\n                  top: '50%',\r\n                  transform: 'translateY(-50%)',\r\n                  zIndex: 2,\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: '8px'\r\n                }}\r\n              >\r\n                {/* Notification Bell */}\r\n                {!user?.isAdmin && (\r\n                  <div\r\n                    className=\"safe-notification-animation\"\r\n                    style={{\r\n                      display: 'flex',\r\n                      alignItems: 'center'\r\n                    }}\r\n                  >\r\n                    <div style={{\r\n                      transform: 'scale(0.8)',\r\n                      transformOrigin: 'center'\r\n                    }}>\r\n                      <NotificationBell unreadCount={2} />\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"flex items-center space-x-2 group safe-profile-animation cursor-pointer\"\r\n                  onClick={() => navigate('/profile')}\r\n                  title=\"Go to Profile\"\r\n                  style={{\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    gap: '6px',\r\n                    flexDirection: 'row'\r\n                  }}\r\n                >\r\n                  {/* User Name and Class - Show on mobile, positioned to the right */}\r\n                  <div\r\n                    className=\"text-right mobile-profile-info\"\r\n                    style={{\r\n                      display: 'flex !important',\r\n                      flexDirection: 'column !important',\r\n                      alignItems: 'flex-end !important',\r\n                      textAlign: 'right !important',\r\n                      marginRight: '4px !important'\r\n                    }}\r\n                  >\r\n                    <div\r\n                      className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\"\r\n                      style={{\r\n                        display: 'block !important',\r\n                        fontSize: '0.75rem !important',\r\n                        fontWeight: '600 !important',\r\n                        color: '#374151 !important',\r\n                        lineHeight: '1.1 !important',\r\n                        margin: '0 !important',\r\n                        whiteSpace: 'nowrap !important'\r\n                      }}\r\n                    >\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div\r\n                      className=\"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\"\r\n                      style={{\r\n                        display: 'block !important',\r\n                        fontSize: '0.7rem !important',\r\n                        color: '#6b7280 !important',\r\n                        lineHeight: '1.1 !important',\r\n                        margin: '0 !important',\r\n                        whiteSpace: 'nowrap !important'\r\n                      }}\r\n                    >\r\n                      {user?.level === 'primary' ? `Class ${user?.class}` : user?.class}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={{\r\n                      ...user,\r\n                      isOnline: true,\r\n                      lastActivity: new Date().toISOString()\r\n                    }}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '28px',\r\n                      height: '28px',\r\n                      flexShrink: 0\r\n                    }}\r\n                  />\r\n\r\n                  {/* Profile Access Indicator */}\r\n                  <div className=\"hidden md:block\">\r\n                    <TbUser className=\"text-gray-400 group-hover:text-blue-500 transition-colors duration-300 text-sm\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n        )}\r\n\r\n        {/* Page Content */}\r\n        <main className={`flex-1 overflow-auto ${\r\n          user?.isAdmin\r\n            ? 'bg-gray-100'\r\n            : 'bg-gradient-to-br from-gray-50 to-blue-50'\r\n        } ${user?.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`}>\r\n          <div\r\n            className=\"h-full safe-content-animation\"\r\n          >\r\n            {children}\r\n          </div>\r\n        </main>\r\n\r\n        {/* Floating Brainwave AI - Show on all pages except quiz and results */}\r\n        {shouldShowFloatingAI() && <FloatingBrainwaveAI />}\r\n\r\n\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,eAAe,QAAQ,OAAO;AAC3E,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,SAASC,MAAM,EAAEC,eAAe,EAAEC,OAAO,EAAEC,GAAG,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AACnI,OAAOC,qBAAqB,MAAM,gCAAgC;AAClE,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,aAAa,EAAEC,cAAc,EAAEC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzF,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGjC,WAAW,CAAEkC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM4C,WAAW,GAAG3C,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM4C,YAAY,GAAG5C,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAE6C;EAAiB,CAAC,GAAGzC,WAAW,CAAEkC,KAAK,IAAKA,KAAK,CAACQ,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAG3C,WAAW,CAAEkC,KAAK,IAAKA,KAAK,CAACU,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAC9B,MAAM+C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM6C,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,WAAW,GAAGF,QAAQ,CAACG,QAAQ;IACrC,MAAMC,aAAa,GAAG,CACpB,YAAY,EACZ,aAAa,EACb,OAAO,EACP,QAAQ,EACR,UAAU,EACV,WAAW,EACX,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,cAAc,EACd,QAAQ,EACR,SAAS,EACT,UAAU,EACV,WAAW,EACX,eAAe,EACf,gBAAgB,CACjB;;IAED;IACA,OAAO,CAACA,aAAa,CAACC,IAAI,CAACC,IAAI,IAAIJ,WAAW,CAACK,QAAQ,CAACD,IAAI,CAAC,CAAC,IACvD,CAACJ,WAAW,CAACK,QAAQ,CAAC,MAAM,CAAC,IAC7B,CAACL,WAAW,CAACK,QAAQ,CAAC,QAAQ,CAAC,IAC/B,CAACL,WAAW,CAACK,QAAQ,CAAC,OAAO,CAAC,IAC9B,CAACL,WAAW,CAACK,QAAQ,CAAC,SAAS,CAAC;EACzC,CAAC;EACD,MAAMC,WAAW,GAAGR,QAAQ,CAACG,QAAQ;EAMrC,MAAMM,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM3D,WAAW,CAAC,CAAC;MACpC,IAAI2D,QAAQ,CAACC,OAAO,EAAE;QACpBb,QAAQ,CAAC5C,OAAO,CAACwD,QAAQ,CAACE,IAAI,CAAC,CAAC;;QAEhC;QACAC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ,CAACE,IAAI,CAAC,CAAC;;QAE3D;QACAK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;UAC/BC,IAAI,EAAET,QAAQ,CAACE,IAAI,CAACO,IAAI;UACxBC,OAAO,EAAEV,QAAQ,CAACE,IAAI,CAACQ,OAAO;UAC9BC,KAAK,EAAEX,QAAQ,CAACE,IAAI,CAACS;QACvB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL5E,OAAO,CAAC6E,KAAK,CAACZ,QAAQ,CAACjE,OAAO,CAAC;QAC/BsD,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdvB,QAAQ,CAAC,QAAQ,CAAC;MAClBtD,OAAO,CAAC6E,KAAK,CAACA,KAAK,CAAC7E,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd,MAAM4E,KAAK,GAAGV,YAAY,CAACW,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAID,KAAK,EAAE;MACT;MACA,IAAI,CAACrC,IAAI,EAAE;QACT;QACA,MAAMuC,UAAU,GAAGZ,YAAY,CAACW,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIC,UAAU,EAAE;UACd,IAAI;YACF,MAAMC,QAAQ,GAAGX,IAAI,CAACY,KAAK,CAACF,UAAU,CAAC;YACvCR,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;cAAEC,IAAI,EAAEO,QAAQ,CAACP,IAAI;cAAEC,OAAO,EAAEM,QAAQ,CAACN;YAAQ,CAAC,CAAC;YACjHtB,QAAQ,CAAC5C,OAAO,CAACwE,QAAQ,CAAC,CAAC;UAC7B,CAAC,CAAC,OAAOJ,KAAK,EAAE;YACdL,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;YACnFT,WAAW,CAAC,CAAC;UACf;QACF,CAAC,MAAM;UACLQ,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;UACrFT,WAAW,CAAC,CAAC;QACf;MACF,CAAC,MAAM;QACLQ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;UAAEC,IAAI,EAAEjC,IAAI,CAACiC,IAAI;UAAEC,OAAO,EAAElC,IAAI,CAACkC;QAAQ,CAAC,CAAC;MAClG;IACF,CAAC,MAAM;MACLrB,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EAINpD,SAAS,CAAC,MAAM;IACd;IACA,MAAMiF,aAAa,GAAG,CAAC,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,oBAAoB,EAAE,SAAS,CAAC;IACrG,MAAMC,cAAc,GAAGD,aAAa,CAACvB,IAAI,CAACyB,KAAK,IAAItB,WAAW,CAACD,QAAQ,CAACuB,KAAK,CAAC,CAAC;;IAE/E;IACA,IAAI1C,gBAAgB,IAAI,CAACyC,cAAc,KAClC3C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6C,eAAe,IAAI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,kBAAkB,MAAK,MAAM,IAAI,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,kBAAkB,EAAC,IAC3F,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,GAAE;MAClBH,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAEhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,eAAe,CAAC;MAC9FhC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAACX,gBAAgB,EAAEoB,WAAW,EAAET,QAAQ,EAAEb,IAAI,CAAC,CAAC;EAEnD,MAAM+C,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMrB,IAAI,GAAG,MAAMrD,kBAAkB,CAAC,CAAC;MACvC0D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEN,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEU,KAAK,IAAI,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,aAAa,MAAK,MAAM,EAAE;QACjD,IAAIxC,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACtC,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACA;QACA,IAAI,CAAC0B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6C,eAAe,IAAI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,kBAAkB,MAAK,MAAM,IAAI,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,kBAAkB,MAAK,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,GAAE;UACjH/B,mBAAmB,CAAC,IAAI,CAAC;QAC3B,CAAC,MAAM;UACLA,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9B;MACF,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BS,QAAQ,CAACtC,eAAe,CAACoD,IAAI,CAAC,CAAC;QAC/B,IAAIpB,WAAW,CAAC2C,OAAO,EAAE;UACvBC,aAAa,CAAC5C,WAAW,CAAC2C,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEI,KAAK,CAAC;MACpDxB,QAAQ,CAACtC,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/B;MACA,IAAI,CAAC0B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6C,eAAe,IAAI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,kBAAkB,MAAK,MAAM,IAAI,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,kBAAkB,MAAK,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,GAAE;QACjH/B,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLA,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC9B;IACF;EACF,CAAC;;EAED1C,SAAS,CAAC,MAAM;IACd;IACA,IAAIuC,IAAI,IAAI,EAACA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,MAAKlC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6C,eAAe,IAAI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,kBAAkB,MAAK,MAAM,IAAI,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,kBAAkB,EAAC,EAAE;MACzHf,OAAO,CAACC,GAAG,CAAC,kEAAkE,EAAEhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,eAAe,CAAC;MAEtG,IAAInC,yBAAyB,EAAE;QAC7BqB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C1B,WAAW,CAAC2C,OAAO,GAAGE,WAAW,CAAC,MAAM;UACtCpB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCe,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTnC,QAAQ,CAACrC,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF,CAAC,MAAM;MACL;MACA4B,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAACO,yBAAyB,EAAEV,IAAI,CAAC,CAAC;EAErCvC,SAAS,CAAC,MAAM;IACd;IACA,IAAIuC,IAAI,IAAI,EAACA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,MAAKlC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6C,eAAe,IAAI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,kBAAkB,MAAK,MAAM,IAAI,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,kBAAkB,EAAC,EAAE;MACzHf,OAAO,CAACC,GAAG,CAAC,0EAA0E,EAAEhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,eAAe,CAAC;MAC9GE,mBAAmB,CAAC,CAAC;IACvB,CAAC,MAAM;MACL;MACA5C,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAACH,IAAI,EAAEsB,WAAW,CAAC,CAAC;;EAEvB;EACA7D,SAAS,CAAC,MAAM;IACd,IAAIuC,IAAI,IAAI,CAACA,IAAI,CAACkC,OAAO,EAAE;MACzB;MACA1C,aAAa,CAAC,CAAC,CAAC4D,KAAK,CAACrB,OAAO,CAACK,KAAK,CAAC;;MAEpC;MACA7B,YAAY,CAAC0C,OAAO,GAAGE,WAAW,CAAC,MAAM;QACvCzD,aAAa,CAAC,CAAC,CAAC0D,KAAK,CAACrB,OAAO,CAACK,KAAK,CAAC;MACtC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;MAEZ;MACA,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;QAC/B5D,cAAc,CAAC,CAAC,CAAC2D,KAAK,CAACrB,OAAO,CAACK,KAAK,CAAC;MACvC,CAAC;MAEDkB,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEF,kBAAkB,CAAC;MAE3D,OAAO,MAAM;QACX,IAAI9C,YAAY,CAAC0C,OAAO,EAAE;UACxBC,aAAa,CAAC3C,YAAY,CAAC0C,OAAO,CAAC;QACrC;QACAK,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEH,kBAAkB,CAAC;QAC9D5D,cAAc,CAAC,CAAC,CAAC2D,KAAK,CAACrB,OAAO,CAACK,KAAK,CAAC;MACvC,CAAC;IACH;EACF,CAAC,EAAE,CAACpC,IAAI,CAAC,CAAC;EAGV,MAAMyD,cAAc,GAAIC,KAAK,IAAK;IAChC;IACA,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC9F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA;IACA,IAAI,CAAC1D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6C,eAAe,IAAI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,kBAAkB,MAAK,MAAM,IAAI,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,kBAAkB,MAAK,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,GAAE;MACjH,OAAO,CAAA1B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwC,aAAa,MAAK,MAAM,GAAG,iBAAiB,GAAG,EAAE;IAC5E;;IAEA;IACA,OAAO,EAAE;EACX,CAAC;EAKD,oBACEpD,OAAA;IAAK+D,SAAS,EAAC,0CAA0C;IAAA7D,QAAA,gBAEvDF,OAAA;MAAAE,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAGT,EAAC/D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,kBAAItC,OAAA,CAACnB,aAAa;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGpCnE,OAAA;MAAK+D,SAAS,EAAC,mCAAmC;MAAA7D,QAAA,gBAG9CF,OAAA;QACE+D,SAAS,EAAG,oCACV7C,QAAQ,CAACG,QAAQ,CAACI,QAAQ,CAAC,aAAa,CAAC,IAAIP,QAAQ,CAACG,QAAQ,CAACI,QAAQ,CAAC,aAAa,CAAC,GAClF,8EAA8E,GAC9E,2DACL,8FAA8F;QAC/F2C,KAAK,EAAE;UACLC,SAAS,EAAE,MAAM;UACjBC,MAAM,EAAE,MAAM;UACdC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE;QACX,CAAE;QAAAtE,QAAA,gBAGJF,OAAA;UAAK+D,SAAS,EAAC,iBAAiB;UAACK,KAAK,EAAE;YAAEI,OAAO,EAAE,QAAQ;YAAEF,MAAM,EAAE;UAAO,CAAE;UAAApE,QAAA,eAC5EF,OAAA;YAAKoE,KAAK,EAAE;cACVK,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,eAAe;cAC/BL,MAAM,EAAE,MAAM;cACdM,KAAK,EAAE;YACT,CAAE;YAAA1E,QAAA,gBAEAF,OAAA;cAAKoE,KAAK,EAAE;gBACVS,IAAI,EAAE,UAAU;gBAChBJ,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBI,GAAG,EAAE;cACP,CAAE;cAAA5E,QAAA,gBACAF,OAAA;gBACE+D,SAAS,EAAC,+FAA+F;gBACzGgB,OAAO,EAAEA,CAAA,KAAMtE,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;gBACtD4D,KAAK,EAAE;kBACLQ,KAAK,EAAE,MAAM;kBACbN,MAAM,EAAE,MAAM;kBACdG,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBK,UAAU,EAAE,0BAA0B;kBACtCC,YAAY,EAAE,MAAM;kBACpBC,SAAS,EAAE,8BAA8B;kBACzCC,MAAM,EAAE;gBACV,CAAE;gBAAAjF,QAAA,EAEDM,gBAAgB,gBAAGR,OAAA,CAACf,GAAG;kBAACmG,IAAI,EAAE;gBAAG;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGnE,OAAA,CAAChB,OAAO;kBAACoG,IAAI,EAAE;gBAAG;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,EAGR,EAAC/D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,kBACbtC,OAAA;gBAAK+D,SAAS,EAAC,kBAAkB;gBAACK,KAAK,EAAE;kBAAEiB,SAAS,EAAE;gBAAa,CAAE;gBAAAnF,QAAA,eACnEF,OAAA,CAACP,gBAAgB;kBAAC6F,WAAW,EAAE;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNnE,OAAA;cAAKoE,KAAK,EAAE;gBACVS,IAAI,EAAE,UAAU;gBAChBJ,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBG,GAAG,EAAE;cACP,CAAE;cAAA5E,QAAA,gBAEAF,OAAA;gBACEuF,GAAG,EAAC,gCAAgC;gBACpCC,GAAG,EAAC,eAAe;gBACnBpB,KAAK,EAAE;kBACLQ,KAAK,EAAE,MAAM;kBACbN,MAAM,EAAE,MAAM;kBACdW,YAAY,EAAE,KAAK;kBACnBQ,SAAS,EAAE;gBACb;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFnE,OAAA;gBAAIoE,KAAK,EAAE;kBACTsB,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE,KAAK;kBACjBC,MAAM,EAAE,GAAG;kBACXC,UAAU,EAAE,KAAK;kBACjBC,UAAU,EAAE;gBACd,CAAE;gBAAA5F,QAAA,gBACAF,OAAA;kBAAMoE,KAAK,EAAE;oBAAE2B,KAAK,EAAE;kBAAU,CAAE;kBAAA7F,QAAA,EAAC;gBAAK;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CnE,OAAA;kBAAMoE,KAAK,EAAE;oBAAE2B,KAAK,EAAE;kBAAU,CAAE;kBAAA7F,QAAA,EAAC;gBAAI;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGNnE,OAAA;cAAKoE,KAAK,EAAE;gBACVS,IAAI,EAAE,UAAU;gBAChBJ,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBI,GAAG,EAAE;cACP,CAAE;cAAA5E,QAAA,gBAEAF,OAAA;gBAAKoE,KAAK,EAAE;kBACVK,OAAO,EAAE,MAAM;kBACfuB,aAAa,EAAE,QAAQ;kBACvBtB,UAAU,EAAE,UAAU;kBACtBuB,SAAS,EAAE;gBACb,CAAE;gBAAA/F,QAAA,gBACAF,OAAA;kBAAKoE,KAAK,EAAE;oBACVsB,QAAQ,EAAE,QAAQ;oBAClBC,UAAU,EAAE,KAAK;oBACjBI,KAAK,EAAE,SAAS;oBAChBF,UAAU,EAAE,KAAK;oBACjBD,MAAM,EAAE,GAAG;oBACXE,UAAU,EAAE;kBACd,CAAE;kBAAA5F,QAAA,EACC,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,IAAI,KAAI;gBAAM;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNnE,OAAA;kBAAKoE,KAAK,EAAE;oBACVsB,QAAQ,EAAE,QAAQ;oBAClBK,KAAK,EAAE,SAAS;oBAChBF,UAAU,EAAE,KAAK;oBACjBD,MAAM,EAAE,GAAG;oBACXE,UAAU,EAAE;kBACd,CAAE;kBAAA5F,QAAA,EACC,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8F,KAAK,MAAK,SAAS,GAAI,SAAQ9F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F,KAAM,EAAC,GAAG/F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F;gBAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL,EAAC/D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,kBACbtC,OAAA;gBAAK+D,SAAS,EAAC,mBAAmB;gBAACK,KAAK,EAAE;kBAAEiB,SAAS,EAAE;gBAAa,CAAE;gBAAAnF,QAAA,eACpEF,OAAA,CAACP,gBAAgB;kBAAC6F,WAAW,EAAE;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CACN,eAGDnE,OAAA,CAACN,cAAc;gBACbU,IAAI,EAAE;kBACJ,GAAGA,IAAI;kBACPgG,QAAQ,EAAE,IAAI;kBACdC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;gBACvC,CAAE;gBACFnB,IAAI,EAAC,IAAI;gBACToB,gBAAgB,EAAE,IAAK;gBACvBpC,KAAK,EAAE;kBACLQ,KAAK,EAAE,MAAM;kBACbN,MAAM,EAAE,MAAM;kBACdmC,UAAU,EAAE;gBACd;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnE,OAAA;UAAK+D,SAAS,EAAC,0EAA0E;UAACK,KAAK,EAAE;YAAEI,OAAO,EAAE,QAAQ;YAAEkC,QAAQ,EAAE;UAAS,CAAE;UAAAxG,QAAA,eACzIF,OAAA;YAAK+D,SAAS,EAAC,mCAAmC;YAACK,KAAK,EAAE;cAAEE,MAAM,EAAE,MAAM;cAAED,SAAS,EAAE,MAAM;cAAEE,SAAS,EAAE;YAAO,CAAE;YAAArE,QAAA,gBAEjHF,OAAA;cAAK+D,SAAS,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAG9BnE,OAAA;cACE+D,SAAS,EAAC,4BAA4B;cACtCK,KAAK,EAAE;gBACLuC,QAAQ,EAAE,UAAU;gBACpBC,IAAI,EAAE,KAAK;gBACXC,GAAG,EAAE,KAAK;gBACVxB,SAAS,EAAE,uBAAuB;gBAClCyB,MAAM,EAAE;cACV,CAAE;cAAA5G,QAAA,eAEFF,OAAA;gBACE+D,SAAS,EAAC,+EAA+E;gBACzFK,KAAK,EAAE;kBACLM,UAAU,EAAE,QAAQ;kBACpBI,GAAG,EAAE,KAAK;kBACVR,MAAM,EAAE,MAAM;kBACdG,OAAO,EAAE;gBACX,CAAE;gBAAAvE,QAAA,gBAGFF,OAAA;kBACE+D,SAAS,EAAC,wEAAwE;kBAClFK,KAAK,EAAE;oBACLQ,KAAK,EAAE,MAAM;oBACbN,MAAM,EAAE,MAAM;oBACdmC,UAAU,EAAE;kBACd,CAAE;kBAAAvG,QAAA,eAEFF,OAAA;oBACEuF,GAAG,EAAC,gCAAgC;oBACpCC,GAAG,EAAC,eAAe;oBACnBzB,SAAS,EAAC,4BAA4B;oBACtCK,KAAK,EAAE;sBAAEqB,SAAS,EAAE;oBAAQ,CAAE;oBAC9BsB,OAAO,EAAGC,CAAC,IAAK;sBACd;sBACAA,CAAC,CAACC,MAAM,CAAC1B,GAAG,GAAG,8GAA8G;sBAC7HyB,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,MAAM;wBACvB;wBACAF,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAACK,OAAO,GAAG,MAAM;wBAC/BuC,CAAC,CAACC,MAAM,CAACE,aAAa,CAACC,SAAS,GAAG,+GAA+G;sBACpJ,CAAC;oBACH;kBAAE;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNnE,OAAA;kBAAK+D,SAAS,EAAC,8BAA8B;kBAAA7D,QAAA,gBAC3CF,OAAA;oBAAI+D,SAAS,EAAC,qFAAqF;oBAC/FK,KAAK,EAAE;sBACLiD,UAAU,EAAE,yDAAyD;sBACrEC,aAAa,EAAE,SAAS;sBACxB5B,QAAQ,EAAE,QAAQ;sBAClBG,UAAU,EAAE,KAAK;sBACjBD,MAAM,EAAE,GAAG;sBACXpB,OAAO,EAAE;oBACX,CAAE;oBAAAtE,QAAA,gBAEJF,OAAA;sBACE+D,SAAS,EAAC,kCAAkC;sBAC5CK,KAAK,EAAE;wBACL2B,KAAK,EAAE,SAAS;wBAChBJ,UAAU,EAAE,KAAK;wBACjB4B,UAAU,EAAE,kCAAkC;wBAC9CC,SAAS,EAAE;sBACb,CAAE;sBAAAtH,QAAA,GACH,OAGC,eACAF,OAAA;wBACE+D,SAAS,EAAC,8DAA8D;wBACxEK,KAAK,EAAE;0BACLqD,eAAe,EAAE,SAAS;0BAC1BvC,SAAS,EAAE,kBAAkB;0BAC7BsC,SAAS,EAAE;wBACb;sBAAE;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAGPnE,OAAA;sBACE+D,SAAS,EAAC,iCAAiC;sBAC3CK,KAAK,EAAE;wBACL2B,KAAK,EAAE,SAAS;wBAChBJ,UAAU,EAAE,KAAK;wBACjB4B,UAAU,EAAE,kCAAkC;wBAC9CC,SAAS,EAAE;sBACb,CAAE;sBAAAtH,QAAA,GACH,MAGC,eACAF,OAAA;wBACE+D,SAAS,EAAC,8DAA8D;wBACxEK,KAAK,EAAE;0BACLqD,eAAe,EAAE,SAAS;0BAC1BvC,SAAS,EAAE,iBAAiB;0BAC5BsC,SAAS,EAAE;wBACb;sBAAE;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAGLnE,OAAA;oBACE+D,SAAS,EAAC,8DAA8D;oBACxEK,KAAK,EAAE;sBACLY,UAAU,EAAE,mDAAmD;sBAC/DE,SAAS,EAAE,kCAAkC;sBAC7CN,KAAK,EAAE,MAAM;sBACb4C,SAAS,EAAE;oBACb;kBAAE;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNnE,OAAA;kBACE+D,SAAS,EAAC,gEAAgE;kBAC1EK,KAAK,EAAE;oBACLY,UAAU,EAAE,SAAS;oBACrBE,SAAS,EAAE,4BAA4B;oBACvCN,KAAK,EAAE,MAAM;oBACbN,MAAM,EAAE,MAAM;oBACdmC,UAAU,EAAE;kBACd,CAAE;kBAAAvG,QAAA,gBAEFF,OAAA;oBACEuF,GAAG,EAAC,cAAc;oBAClBC,GAAG,EAAC,gBAAgB;oBACpBzB,SAAS,EAAC,4BAA4B;oBACtCK,KAAK,EAAE;sBAAEqB,SAAS,EAAE;oBAAQ,CAAE;oBAC9BsB,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAACK,OAAO,GAAG,MAAM;sBAC/BuC,CAAC,CAACC,MAAM,CAACS,WAAW,CAACtD,KAAK,CAACK,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFnE,OAAA;oBACE+D,SAAS,EAAC,gHAAgH;oBAC1HK,KAAK,EAAE;sBACLK,OAAO,EAAE,MAAM;sBACfiB,QAAQ,EAAE;oBACZ,CAAE;oBAAAxF,QAAA,EACH;kBAED;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNnE,OAAA;kBAAK+D,SAAS,EAAC;gBAAyK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnE,OAAA;cACE+D,SAAS,EAAC,6DAA6D;cACvEK,KAAK,EAAE;gBACLuC,QAAQ,EAAE,UAAU;gBACpBgB,KAAK,EAAE,GAAG;gBACVd,GAAG,EAAE,KAAK;gBACVxB,SAAS,EAAE,kBAAkB;gBAC7ByB,MAAM,EAAE,CAAC;gBACTrC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBI,GAAG,EAAE;cACP,CAAE;cAAA5E,QAAA,GAGD,EAACE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,kBACbtC,OAAA;gBACE+D,SAAS,EAAC,6BAA6B;gBACvCK,KAAK,EAAE;kBACLK,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE;gBACd,CAAE;gBAAAxE,QAAA,eAEFF,OAAA;kBAAKoE,KAAK,EAAE;oBACViB,SAAS,EAAE,YAAY;oBACvBuC,eAAe,EAAE;kBACnB,CAAE;kBAAA1H,QAAA,eACAF,OAAA,CAACP,gBAAgB;oBAAC6F,WAAW,EAAE;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDnE,OAAA;gBACE+D,SAAS,EAAC,yEAAyE;gBACnFgB,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAAC,UAAU,CAAE;gBACpC6C,KAAK,EAAC,eAAe;gBACrBM,KAAK,EAAE;kBACLK,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBI,GAAG,EAAE,KAAK;kBACVkB,aAAa,EAAE;gBACjB,CAAE;gBAAA9F,QAAA,gBAGFF,OAAA;kBACE+D,SAAS,EAAC,gCAAgC;kBAC1CK,KAAK,EAAE;oBACLK,OAAO,EAAE,iBAAiB;oBAC1BuB,aAAa,EAAE,mBAAmB;oBAClCtB,UAAU,EAAE,qBAAqB;oBACjCuB,SAAS,EAAE,kBAAkB;oBAC7B4B,WAAW,EAAE;kBACf,CAAE;kBAAA3H,QAAA,gBAEFF,OAAA;oBACE+D,SAAS,EAAC,uGAAuG;oBACjHK,KAAK,EAAE;sBACLK,OAAO,EAAE,kBAAkB;sBAC3BiB,QAAQ,EAAE,oBAAoB;sBAC9BC,UAAU,EAAE,gBAAgB;sBAC5BI,KAAK,EAAE,oBAAoB;sBAC3BF,UAAU,EAAE,gBAAgB;sBAC5BD,MAAM,EAAE,cAAc;sBACtBE,UAAU,EAAE;oBACd,CAAE;oBAAA5F,QAAA,EAED,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,IAAI,KAAI;kBAAM;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACNnE,OAAA;oBACE+D,SAAS,EAAC,gFAAgF;oBAC1FK,KAAK,EAAE;sBACLK,OAAO,EAAE,kBAAkB;sBAC3BiB,QAAQ,EAAE,mBAAmB;sBAC7BK,KAAK,EAAE,oBAAoB;sBAC3BF,UAAU,EAAE,gBAAgB;sBAC5BD,MAAM,EAAE,cAAc;sBACtBE,UAAU,EAAE;oBACd,CAAE;oBAAA5F,QAAA,EAED,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8F,KAAK,MAAK,SAAS,GAAI,SAAQ9F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F,KAAM,EAAC,GAAG/F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F;kBAAK;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNnE,OAAA,CAACN,cAAc;kBACbU,IAAI,EAAE;oBACJ,GAAGA,IAAI;oBACPgG,QAAQ,EAAE,IAAI;oBACdC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;kBACvC,CAAE;kBACFnB,IAAI,EAAC,IAAI;kBACToB,gBAAgB,EAAE,IAAK;kBACvBpC,KAAK,EAAE;oBACLQ,KAAK,EAAE,MAAM;oBACbN,MAAM,EAAE,MAAM;oBACdmC,UAAU,EAAE;kBACd;gBAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGFnE,OAAA;kBAAK+D,SAAS,EAAC,iBAAiB;kBAAA7D,QAAA,eAC9BF,OAAA,CAACZ,MAAM;oBAAC2E,SAAS,EAAC;kBAAgF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAITnE,OAAA;QAAM+D,SAAS,EAAG,wBAChB3D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,GACT,aAAa,GACb,2CACL,IAAGlC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,OAAO,GAAG,KAAK,GAAG,eAAgB,EAAE;QAAApC,QAAA,eAC5CF,OAAA;UACE+D,SAAS,EAAC,+BAA+B;UAAA7D,QAAA,EAExCA;QAAQ;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGNhD,oBAAoB,CAAC,CAAC,iBAAInB,OAAA,CAACL,mBAAmB;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAI/C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChE,EAAA,CAltBQF,cAAc;EAAA,QACJ9B,WAAW,EAKCA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EACXC,WAAW;AAAA;AAAAwJ,EAAA,GAVrB7H,cAAc;AAotBvB,eAAeA,cAAc;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}