const axios = require('axios');

const testAPIResponse = async () => {
  try {
    console.log('🧪 Testing API Response for Video Thumbnails...\n');
    
    console.log('Testing get-study-content endpoint (no auth required)...');
    
    try {
      const response = await axios.post('http://localhost:5000/api/study/get-study-content', {
        content: 'videos',
        className: 'all',
        subject: 'all'
      });
      
      console.log('✅ Study content API Response received');
      console.log(`   Status: ${response.status}`);
      console.log(`   Videos returned: ${response.data?.data?.length || 0}`);
      
      if (response.data?.data?.length > 0) {
        console.log('\n📹 Video data from study content API:');
        response.data.data.forEach((video, index) => {
          console.log(`\n${index + 1}. "${video.title}"`);
          console.log(`   - ID: ${video._id}`);
          console.log(`   - Thumbnail: ${video.thumbnail || 'NO THUMBNAIL'}`);
          console.log(`   - Has thumbnail field: ${video.hasOwnProperty('thumbnail') ? 'YES' : 'NO'}`);
          console.log(`   - Thumbnail type: ${typeof video.thumbnail}`);
          if (video.thumbnail) {
            console.log(`   - Thumbnail length: ${video.thumbnail.length}`);
            console.log(`   - Thumbnail preview: ${video.thumbnail.substring(0, 50)}...`);
          }
          console.log(`   - All fields: ${Object.keys(video).join(', ')}`);
        });
        
        console.log('\n🎯 CONCLUSION:');
        const videosWithThumbnails = response.data.data.filter(v => v.thumbnail && v.thumbnail.trim() !== '').length;
        console.log(`   - API returns ${videosWithThumbnails}/${response.data.data.length} videos with thumbnails`);
        console.log(`   - Thumbnail field is included: ${response.data.data[0].hasOwnProperty('thumbnail') ? 'YES' : 'NO'}`);
        
        if (videosWithThumbnails > 0) {
          console.log('   - ✅ Database thumbnails are available via API');
          console.log('   - The frontend should be able to display them');
        } else {
          console.log('   - ❌ No thumbnails available via API');
        }
      }
      
    } catch (error) {
      console.log('❌ Study content API Error:', error.response?.status, error.response?.data?.message || error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
};

testAPIResponse();
