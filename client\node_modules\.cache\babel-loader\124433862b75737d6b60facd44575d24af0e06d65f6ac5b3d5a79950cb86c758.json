{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { Suspense, lazy } from \"react\";\nimport \"./stylesheets/theme.css\";\nimport \"./stylesheets/alignments.css\";\nimport \"./stylesheets/textelements.css\";\nimport \"./stylesheets/form-elements.css\";\nimport \"./stylesheets/custom-components.css\";\nimport \"./stylesheets/layout.css\";\nimport \"./styles/modern.css\";\nimport \"./styles/animations.css\";\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Loader from \"./components/Loader\";\nimport { useSelector } from \"react-redux\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { LanguageProvider } from \"./contexts/LanguageContext\";\nimport { ErrorBoundary } from \"./components/modern\";\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\n\n// Immediate load components (critical for initial render)\nimport Login from \"./pages/common/Login\";\nimport Register from \"./pages/common/Register\";\nimport Home from \"./pages/common/Home\";\n\n// Lazy load components for better performance\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = /*#__PURE__*/lazy(_c = () => import(\"./pages/user/Quiz\"));\n_c2 = Quiz;\nconst QuizPlay = /*#__PURE__*/lazy(_c3 = () => import(\"./pages/user/Quiz/QuizPlay\"));\n_c4 = QuizPlay;\nconst QuizResult = /*#__PURE__*/lazy(_c5 = () => import(\"./pages/user/Quiz/QuizResult\"));\n_c6 = QuizResult;\nconst Exams = /*#__PURE__*/lazy(_c7 = () => import(\"./pages/admin/Exams\"));\n_c8 = Exams;\nconst AddEditExam = /*#__PURE__*/lazy(_c9 = () => import(\"./pages/admin/Exams/AddEditExam\"));\n_c10 = AddEditExam;\nconst Users = /*#__PURE__*/lazy(_c11 = () => import(\"./pages/admin/Users\"));\n_c12 = Users;\nconst AdminDashboard = /*#__PURE__*/lazy(_c13 = () => import(\"./pages/admin/Dashboard\"));\n_c14 = AdminDashboard;\nconst WriteExam = /*#__PURE__*/lazy(_c15 = () => import(\"./pages/user/WriteExam\"));\n_c16 = WriteExam;\nconst UserReports = /*#__PURE__*/lazy(_c17 = () => import(\"./pages/user/UserReports\"));\n_c18 = UserReports;\nconst AdminReports = /*#__PURE__*/lazy(_c19 = () => import(\"./pages/admin/AdminReports\"));\n_c20 = AdminReports;\nconst StudyMaterial = /*#__PURE__*/lazy(_c21 = () => import(\"./pages/user/StudyMaterial\"));\n_c22 = StudyMaterial;\nconst VideoLessons = /*#__PURE__*/lazy(_c23 = () => import(\"./pages/user/VideoLessons\"));\n_c24 = VideoLessons;\nconst Skills = /*#__PURE__*/lazy(_c25 = () => import(\"./pages/user/Skills\"));\n_c26 = Skills;\nconst Ranking = /*#__PURE__*/lazy(_c27 = () => import(\"./pages/user/Ranking\"));\n_c28 = Ranking;\nconst RankingErrorBoundary = /*#__PURE__*/lazy(_c29 = () => import(\"./components/RankingErrorBoundary\"));\n_c30 = RankingErrorBoundary;\nconst Profile = /*#__PURE__*/lazy(_c31 = () => import(\"./pages/common/Profile\"));\n_c32 = Profile;\nconst Forum = /*#__PURE__*/lazy(_c33 = () => import(\"./pages/common/Forum\"));\n_c34 = Forum;\nconst Test = /*#__PURE__*/lazy(_c35 = () => import(\"./pages/user/Test\"));\n_c36 = Test;\nconst Subscription = /*#__PURE__*/lazy(_c37 = () => import(\"./pages/user/Subscription\"));\n_c38 = Subscription;\nconst Hub = /*#__PURE__*/lazy(_c39 = () => import(\"./pages/user/Hub\"));\n_c40 = Hub;\nconst AdminStudyMaterials = /*#__PURE__*/lazy(_c41 = () => import(\"./pages/admin/StudyMaterials\"));\n_c42 = AdminStudyMaterials;\nconst AdminSkills = /*#__PURE__*/lazy(_c43 = () => import(\"./pages/admin/Skills\"));\n_c44 = AdminSkills;\nconst AdminVideos = /*#__PURE__*/lazy(_c45 = () => import(\"./pages/admin/Videos\"));\n_c46 = AdminVideos;\nconst AdminVideoLessons = /*#__PURE__*/lazy(_c47 = () => import(\"./pages/admin/VideoLessons\"));\n_c48 = AdminVideoLessons;\nconst AdminProfile = /*#__PURE__*/lazy(_c49 = () => import(\"./pages/admin/Profile\"));\n_c50 = AdminProfile;\nconst AdminNotifications = /*#__PURE__*/lazy(_c51 = () => import(\"./pages/admin/Notifications/AdminNotifications\"));\n_c52 = AdminNotifications;\nconst AdminForum = /*#__PURE__*/lazy(_c53 = () => import(\"./pages/admin/Forum\"));\n_c54 = AdminForum;\nconst DebugAuth = /*#__PURE__*/lazy(_c55 = () => import(\"./components/DebugAuth\"));\n_c56 = DebugAuth;\nconst RankingDemo = /*#__PURE__*/lazy(_c57 = () => import(\"./components/modern/RankingDemo\"));\n_c58 = RankingDemo;\nconst MathTest = /*#__PURE__*/lazy(_c59 = () => import(\"./components/MathTest\"));\n\n// Global error handler for CSS style errors and null reference errors\n_c60 = MathTest;\nwindow.addEventListener('error', event => {\n  if (event.message && (event.message.includes('Indexed property setter is not supported') || event.message.includes('Cannot read properties of null') || event.message.includes('Cannot read property \\'style\\''))) {\n    console.warn('DOM/Style Error caught and handled:', event.message);\n    event.preventDefault();\n    return false;\n  }\n});\n\n// Handle unhandled promise rejections that might be related to style errors\nwindow.addEventListener('unhandledrejection', event => {\n  if (event.reason && event.reason.message && (event.reason.message.includes('Indexed property setter is not supported') || event.reason.message.includes('Cannot read properties of null') || event.reason.message.includes('Cannot read property \\'style\\''))) {\n    console.warn('DOM/Style Promise Rejection caught and handled:', event.reason.message);\n    event.preventDefault();\n  }\n});\n// Fast loading component for lazy routes\nconst FastLoader = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 font-medium\",\n      children: \"Loading page...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-400 text-sm mt-2\",\n      children: \"Please wait a moment\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 85,\n  columnNumber: 3\n}, this);\n_c61 = FastLoader;\nfunction App() {\n  _s();\n  const {\n    loading\n  } = useSelector(state => state.loader);\n\n  // Global mobile responsive fixes for ALL pages\n  React.useEffect(() => {\n    const globalMobileStyles = document.createElement('style');\n    globalMobileStyles.textContent = `\n      /* GLOBAL MOBILE RESPONSIVE FIXES FOR ALL PAGES */\n      @media (max-width: 768px) {\n        /* SHOW hamburger menu on top left */\n        .lg\\\\:hidden {\n          display: flex !important;\n          visibility: visible !important;\n          opacity: 1 !important;\n          position: fixed !important;\n          top: 6px !important;\n          left: 6px !important;\n          z-index: 10001 !important;\n        }\n\n        .lg\\\\:hidden button {\n          display: flex !important;\n          visibility: visible !important;\n          opacity: 1 !important;\n          background: rgba(255, 255, 255, 0.95) !important;\n          border-radius: 8px !important;\n          padding: 6px !important;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;\n          border: 1px solid rgba(0, 0, 0, 0.1) !important;\n          width: 32px !important;\n          height: 32px !important;\n          align-items: center !important;\n          justify-content: center !important;\n        }\n\n        /* Desktop-like header layout for mobile on ALL pages */\n        .nav-modern,\n        header,\n        .safe-header-animation {\n          position: fixed !important;\n          top: 0 !important;\n          left: 0 !important;\n          right: 0 !important;\n          height: 48px !important;\n          min-height: 48px !important;\n          max-height: 48px !important;\n          display: flex !important;\n          align-items: center !important;\n          justify-content: center !important;\n          padding: 0 44px 0 44px !important;\n          z-index: 10000 !important;\n          background: rgba(255, 255, 255, 0.98) !important;\n          backdrop-filter: blur(15px) !important;\n          border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;\n          box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1) !important;\n        }\n\n        /* Header content layout for ALL pages */\n        .nav-modern .flex,\n        header .flex {\n          width: 100% !important;\n          display: flex !important;\n          align-items: center !important;\n          justify-content: space-between !important;\n          height: 48px !important;\n          position: relative !important;\n        }\n\n        /* Logo section - CENTERED between hamburger and profile */\n        .nav-modern .flex > div:first-child,\n        header .flex > div:first-child {\n          position: absolute !important;\n          left: 50% !important;\n          transform: translateX(-50%) !important;\n          display: flex !important;\n          align-items: center !important;\n          justify-content: center !important;\n          gap: 8px !important;\n          z-index: 1 !important;\n        }\n\n        /* BRAINWAVE text - force single line and center */\n        .brainwave-heading-enhanced,\n        .brainwave-heading,\n        h1 {\n          white-space: nowrap !important;\n          overflow: hidden !important;\n          text-overflow: ellipsis !important;\n          font-size: 1.1rem !important;\n          line-height: 1.2 !important;\n          text-align: center !important;\n        }\n\n        /* Flag image - proper sizing */\n        .nav-modern img[alt*=\"flag\"],\n        header img[alt*=\"flag\"],\n        .flag-image {\n          width: 24px !important;\n          height: 16px !important;\n          border-radius: 2px !important;\n        }\n\n        /* Right section (profile + bell) - positioned on the right */\n        .nav-modern .flex > div:last-child,\n        header .flex > div:last-child {\n          position: absolute !important;\n          right: 0 !important;\n          display: flex !important;\n          align-items: center !important;\n          gap: 8px !important;\n          flex-direction: row !important;\n        }\n\n        /* Profile section - name and class */\n        .user-info,\n        .profile-info,\n        .nav-modern .flex > div:last-child > div:first-child,\n        header .flex > div:last-child > div:first-child {\n          display: flex !important;\n          flex-direction: column !important;\n          align-items: flex-end !important;\n          text-align: right !important;\n          margin-right: 4px !important;\n        }\n\n        /* Profile name styling */\n        .user-name,\n        .profile-name {\n          font-size: 0.8rem !important;\n          font-weight: 600 !important;\n          color: #1f2937 !important;\n          line-height: 1.1 !important;\n          margin: 0 !important;\n        }\n\n        /* User class styling */\n        .user-class,\n        .profile-class {\n          font-size: 0.7rem !important;\n          color: #6b7280 !important;\n          line-height: 1.1 !important;\n          margin: 0 !important;\n        }\n\n        /* Bell icon size reduction - make it much smaller */\n        .notification-bell-button .w-5,\n        .notification-bell-button .h-5 {\n          width: 10px !important;\n          height: 10px !important;\n        }\n\n        .notification-bell-button {\n          padding: 3px !important;\n          width: 20px !important;\n          height: 20px !important;\n          border-radius: 6px !important;\n        }\n\n        /* Force sidebar text to be white on ALL pages */\n        .sidebar,\n        .mobile-sidebar,\n        .modern-sidebar,\n        [class*=\"sidebar\"],\n        .ant-menu,\n        .ant-menu-item {\n          background: rgba(0, 0, 0, 0.95) !important;\n          color: #ffffff !important;\n          z-index: 8888 !important;\n        }\n\n        .sidebar *,\n        .mobile-sidebar *,\n        .modern-sidebar *,\n        [class*=\"sidebar\"] *,\n        .ant-menu *,\n        .ant-menu-item *,\n        .sidebar .menu-item,\n        .mobile-sidebar .menu-item,\n        .modern-sidebar .menu-item,\n        .sidebar a,\n        .mobile-sidebar a,\n        .modern-sidebar a,\n        .sidebar span,\n        .mobile-sidebar span,\n        .modern-sidebar span,\n        .sidebar div,\n        .mobile-sidebar div,\n        .modern-sidebar div,\n        .sidebar p,\n        .mobile-sidebar p,\n        .modern-sidebar p,\n        .sidebar h1, .sidebar h2, .sidebar h3, .sidebar h4, .sidebar h5, .sidebar h6,\n        .mobile-sidebar h1, .mobile-sidebar h2, .mobile-sidebar h3, .mobile-sidebar h4, .mobile-sidebar h5, .mobile-sidebar h6,\n        .modern-sidebar h1, .modern-sidebar h2, .modern-sidebar h3, .modern-sidebar h4, .modern-sidebar h5, .modern-sidebar h6,\n        .sidebar .text-gray-100,\n        .sidebar .text-gray-200,\n        .sidebar .text-gray-300,\n        .sidebar .text-gray-400,\n        .sidebar .text-gray-500,\n        .sidebar .text-gray-600,\n        .sidebar .text-gray-700,\n        .sidebar .text-gray-800,\n        .sidebar .text-gray-900,\n        .mobile-sidebar .text-gray-100,\n        .mobile-sidebar .text-gray-200,\n        .mobile-sidebar .text-gray-300,\n        .mobile-sidebar .text-gray-400,\n        .mobile-sidebar .text-gray-500,\n        .mobile-sidebar .text-gray-600,\n        .mobile-sidebar .text-gray-700,\n        .mobile-sidebar .text-gray-800,\n        .mobile-sidebar .text-gray-900,\n        .modern-sidebar .text-gray-100,\n        .modern-sidebar .text-gray-200,\n        .modern-sidebar .text-gray-300,\n        .modern-sidebar .text-gray-400,\n        .modern-sidebar .text-gray-500,\n        .modern-sidebar .text-gray-600,\n        .modern-sidebar .text-gray-700,\n        .modern-sidebar .text-gray-800,\n        .modern-sidebar .text-gray-900 {\n          color: #ffffff !important;\n          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;\n          font-weight: 500 !important;\n        }\n\n        /* Sidebar hover effects */\n        .sidebar .menu-item:hover,\n        .mobile-sidebar .menu-item:hover,\n        .modern-sidebar .menu-item:hover,\n        .sidebar a:hover,\n        .mobile-sidebar a:hover,\n        .modern-sidebar a:hover {\n          color: #ffffff !important;\n          background: rgba(255, 255, 255, 0.2) !important;\n          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;\n        }\n\n        /* Adjust ALL page content to not overlap with header */\n        .min-h-screen,\n        .study-material-modern,\n        .container-modern,\n        .layout,\n        .layout-modern,\n        main,\n        .body,\n        .video-lessons-container {\n          margin-top: 48px !important;\n          padding-top: 8px !important;\n        }\n\n        /* Remove gaps between header and content on ALL pages */\n        .modern-header,\n        .page-header,\n        .content-header {\n          margin-top: 0px !important;\n          padding-top: 8px !important;\n        }\n\n        .tabs-container,\n        .content-container,\n        .material-grid,\n        .pdf-grid {\n          margin-top: 0px !important;\n          padding-top: 8px !important;\n        }\n\n        /* Center ALL modals */\n        .ant-modal,\n        .modal,\n        .pdf-modal,\n        .study-modal,\n        .material-modal,\n        .quiz-modal,\n        .marking-modal,\n        .result-modal {\n          display: flex !important;\n          align-items: center !important;\n          justify-content: center !important;\n          top: 0 !important;\n          padding-top: 0 !important;\n        }\n\n        .ant-modal-content,\n        .modal-content,\n        .pdf-modal-content,\n        .study-modal-content,\n        .material-modal-content,\n        .quiz-modal-content,\n        .marking-modal-content,\n        .result-modal-content {\n          margin: 0 auto !important;\n          position: relative !important;\n          top: auto !important;\n          transform: none !important;\n        }\n\n        .ant-modal-wrap {\n          display: flex !important;\n          align-items: center !important;\n          justify-content: center !important;\n          min-height: 100vh !important;\n        }\n      }\n    `;\n    document.head.appendChild(globalMobileStyles);\n    return () => {\n      document.head.removeChild(globalMobileStyles);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: /*#__PURE__*/_jsxDEV(LanguageProvider, {\n        children: [loading && /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 23\n        }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 36\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/ranking-demo\",\n              element: /*#__PURE__*/_jsxDEV(RankingDemo, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/test\",\n              element: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 33\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Test, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 13\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/forum\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Forum, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/profile\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/profile\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/subscription\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Subscription, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/subscription\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Subscription, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/hub\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Hub, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/quiz\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Quiz, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/write-exam/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(WriteExam, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/quiz/:id/result\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(QuizResult, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/quiz/:id/play\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(QuizPlay, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/math-test\",\n              element: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 35\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(MathTest, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/reports\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(UserReports, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/study-material\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(StudyMaterial, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/video-lessons\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(VideoLessons, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/skills\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Skills, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/ranking\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(RankingErrorBoundary, {\n                    children: /*#__PURE__*/_jsxDEV(Ranking, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 606,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/users\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Users, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/exams\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/exams/add\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/exams/edit/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/study-materials\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminStudyMaterials, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/skills\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminSkills, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/videos\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminVideos, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/video-lessons\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminVideoLessons, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 690,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/reports\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminReports, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/notifications\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminNotifications, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/profile\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminProfile, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/forum\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminForum, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/debug\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(DebugAuth, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 410,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"lBEGihwj84A0HHPhDw41q8J0+C4=\", false, function () {\n  return [useSelector];\n});\n_c62 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56, _c57, _c58, _c59, _c60, _c61, _c62;\n$RefreshReg$(_c, \"Quiz$lazy\");\n$RefreshReg$(_c2, \"Quiz\");\n$RefreshReg$(_c3, \"QuizPlay$lazy\");\n$RefreshReg$(_c4, \"QuizPlay\");\n$RefreshReg$(_c5, \"QuizResult$lazy\");\n$RefreshReg$(_c6, \"QuizResult\");\n$RefreshReg$(_c7, \"Exams$lazy\");\n$RefreshReg$(_c8, \"Exams\");\n$RefreshReg$(_c9, \"AddEditExam$lazy\");\n$RefreshReg$(_c10, \"AddEditExam\");\n$RefreshReg$(_c11, \"Users$lazy\");\n$RefreshReg$(_c12, \"Users\");\n$RefreshReg$(_c13, \"AdminDashboard$lazy\");\n$RefreshReg$(_c14, \"AdminDashboard\");\n$RefreshReg$(_c15, \"WriteExam$lazy\");\n$RefreshReg$(_c16, \"WriteExam\");\n$RefreshReg$(_c17, \"UserReports$lazy\");\n$RefreshReg$(_c18, \"UserReports\");\n$RefreshReg$(_c19, \"AdminReports$lazy\");\n$RefreshReg$(_c20, \"AdminReports\");\n$RefreshReg$(_c21, \"StudyMaterial$lazy\");\n$RefreshReg$(_c22, \"StudyMaterial\");\n$RefreshReg$(_c23, \"VideoLessons$lazy\");\n$RefreshReg$(_c24, \"VideoLessons\");\n$RefreshReg$(_c25, \"Skills$lazy\");\n$RefreshReg$(_c26, \"Skills\");\n$RefreshReg$(_c27, \"Ranking$lazy\");\n$RefreshReg$(_c28, \"Ranking\");\n$RefreshReg$(_c29, \"RankingErrorBoundary$lazy\");\n$RefreshReg$(_c30, \"RankingErrorBoundary\");\n$RefreshReg$(_c31, \"Profile$lazy\");\n$RefreshReg$(_c32, \"Profile\");\n$RefreshReg$(_c33, \"Forum$lazy\");\n$RefreshReg$(_c34, \"Forum\");\n$RefreshReg$(_c35, \"Test$lazy\");\n$RefreshReg$(_c36, \"Test\");\n$RefreshReg$(_c37, \"Subscription$lazy\");\n$RefreshReg$(_c38, \"Subscription\");\n$RefreshReg$(_c39, \"Hub$lazy\");\n$RefreshReg$(_c40, \"Hub\");\n$RefreshReg$(_c41, \"AdminStudyMaterials$lazy\");\n$RefreshReg$(_c42, \"AdminStudyMaterials\");\n$RefreshReg$(_c43, \"AdminSkills$lazy\");\n$RefreshReg$(_c44, \"AdminSkills\");\n$RefreshReg$(_c45, \"AdminVideos$lazy\");\n$RefreshReg$(_c46, \"AdminVideos\");\n$RefreshReg$(_c47, \"AdminVideoLessons$lazy\");\n$RefreshReg$(_c48, \"AdminVideoLessons\");\n$RefreshReg$(_c49, \"AdminProfile$lazy\");\n$RefreshReg$(_c50, \"AdminProfile\");\n$RefreshReg$(_c51, \"AdminNotifications$lazy\");\n$RefreshReg$(_c52, \"AdminNotifications\");\n$RefreshReg$(_c53, \"AdminForum$lazy\");\n$RefreshReg$(_c54, \"AdminForum\");\n$RefreshReg$(_c55, \"DebugAuth$lazy\");\n$RefreshReg$(_c56, \"DebugAuth\");\n$RefreshReg$(_c57, \"RankingDemo$lazy\");\n$RefreshReg$(_c58, \"RankingDemo\");\n$RefreshReg$(_c59, \"MathTest$lazy\");\n$RefreshReg$(_c60, \"MathTest\");\n$RefreshReg$(_c61, \"FastLoader\");\n$RefreshReg$(_c62, \"App\");", "map": {"version": 3, "names": ["React", "Suspense", "lazy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "ProtectedRoute", "Loader", "useSelector", "ThemeProvider", "LanguageProvider", "Error<PERSON>ou<PERSON><PERSON>", "AdminProtectedRoute", "<PERSON><PERSON>", "Register", "Home", "jsxDEV", "_jsxDEV", "Quiz", "_c", "_c2", "QuizPlay", "_c3", "_c4", "QuizResult", "_c5", "_c6", "<PERSON><PERSON>", "_c7", "_c8", "AddEditExam", "_c9", "_c10", "Users", "_c11", "_c12", "AdminDashboard", "_c13", "_c14", "WriteExam", "_c15", "_c16", "UserReports", "_c17", "_c18", "AdminReports", "_c19", "_c20", "StudyMaterial", "_c21", "_c22", "VideoLessons", "_c23", "_c24", "Skills", "_c25", "_c26", "Ranking", "_c27", "_c28", "RankingError<PERSON><PERSON><PERSON>ry", "_c29", "_c30", "Profile", "_c31", "_c32", "Forum", "_c33", "_c34", "Test", "_c35", "_c36", "Subscription", "_c37", "_c38", "<PERSON><PERSON>", "_c39", "_c40", "AdminStudyMaterials", "_c41", "_c42", "AdminSkills", "_c43", "_c44", "AdminVideos", "_c45", "_c46", "AdminVideoLessons", "_c47", "_c48", "AdminProfile", "_c49", "_c50", "AdminNotifications", "_c51", "_c52", "AdminForum", "_c53", "_c54", "DebugAuth", "_c55", "_c56", "RankingDemo", "_c57", "_c58", "MathTest", "_c59", "_c60", "window", "addEventListener", "event", "message", "includes", "console", "warn", "preventDefault", "reason", "FastLoader", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c61", "App", "_s", "loading", "state", "loader", "useEffect", "globalMobileStyles", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "path", "element", "fallback", "_c62", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/App.js"], "sourcesContent": ["import React, { Suspense, lazy } from \"react\";\r\nimport \"./stylesheets/theme.css\";\r\nimport \"./stylesheets/alignments.css\";\r\nimport \"./stylesheets/textelements.css\";\r\nimport \"./stylesheets/form-elements.css\";\r\nimport \"./stylesheets/custom-components.css\";\r\nimport \"./stylesheets/layout.css\";\r\nimport \"./styles/modern.css\";\r\nimport \"./styles/animations.css\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\nimport Loader from \"./components/Loader\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\r\nimport { LanguageProvider } from \"./contexts/LanguageContext\";\r\nimport { ErrorBoundary } from \"./components/modern\";\r\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\r\n\r\n// Immediate load components (critical for initial render)\r\nimport Login from \"./pages/common/Login\";\r\nimport Register from \"./pages/common/Register\";\r\nimport Home from \"./pages/common/Home\";\r\n\r\n// Lazy load components for better performance\r\nconst Quiz = lazy(() => import(\"./pages/user/Quiz\"));\r\nconst QuizPlay = lazy(() => import(\"./pages/user/Quiz/QuizPlay\"));\r\nconst QuizResult = lazy(() => import(\"./pages/user/Quiz/QuizResult\"));\r\nconst Exams = lazy(() => import(\"./pages/admin/Exams\"));\r\nconst AddEditExam = lazy(() => import(\"./pages/admin/Exams/AddEditExam\"));\r\nconst Users = lazy(() => import(\"./pages/admin/Users\"));\r\nconst AdminDashboard = lazy(() => import(\"./pages/admin/Dashboard\"));\r\n\r\nconst WriteExam = lazy(() => import(\"./pages/user/WriteExam\"));\r\nconst UserReports = lazy(() => import(\"./pages/user/UserReports\"));\r\nconst AdminReports = lazy(() => import(\"./pages/admin/AdminReports\"));\r\nconst StudyMaterial = lazy(() => import(\"./pages/user/StudyMaterial\"));\r\nconst VideoLessons = lazy(() => import(\"./pages/user/VideoLessons\"));\r\nconst Skills = lazy(() => import(\"./pages/user/Skills\"));\r\nconst Ranking = lazy(() => import(\"./pages/user/Ranking\"));\r\nconst RankingErrorBoundary = lazy(() => import(\"./components/RankingErrorBoundary\"));\r\nconst Profile = lazy(() => import(\"./pages/common/Profile\"));\r\n\r\nconst Forum = lazy(() => import(\"./pages/common/Forum\"));\r\nconst Test = lazy(() => import(\"./pages/user/Test\"));\r\nconst Subscription = lazy(() => import(\"./pages/user/Subscription\"));\r\n\r\nconst Hub = lazy(() => import(\"./pages/user/Hub\"));\r\nconst AdminStudyMaterials = lazy(() => import(\"./pages/admin/StudyMaterials\"));\r\nconst AdminSkills = lazy(() => import(\"./pages/admin/Skills\"));\r\nconst AdminVideos = lazy(() => import(\"./pages/admin/Videos\"));\r\nconst AdminVideoLessons = lazy(() => import(\"./pages/admin/VideoLessons\"));\r\nconst AdminProfile = lazy(() => import(\"./pages/admin/Profile\"));\r\nconst AdminNotifications = lazy(() => import(\"./pages/admin/Notifications/AdminNotifications\"));\r\nconst AdminForum = lazy(() => import(\"./pages/admin/Forum\"));\r\nconst DebugAuth = lazy(() => import(\"./components/DebugAuth\"));\r\nconst RankingDemo = lazy(() => import(\"./components/modern/RankingDemo\"));\r\nconst MathTest = lazy(() => import(\"./components/MathTest\"));\r\n\r\n// Global error handler for CSS style errors and null reference errors\r\nwindow.addEventListener('error', (event) => {\r\n  if (event.message && (\r\n    event.message.includes('Indexed property setter is not supported') ||\r\n    event.message.includes('Cannot read properties of null') ||\r\n    event.message.includes('Cannot read property \\'style\\'')\r\n  )) {\r\n    console.warn('DOM/Style Error caught and handled:', event.message);\r\n    event.preventDefault();\r\n    return false;\r\n  }\r\n});\r\n\r\n// Handle unhandled promise rejections that might be related to style errors\r\nwindow.addEventListener('unhandledrejection', (event) => {\r\n  if (event.reason && event.reason.message && (\r\n    event.reason.message.includes('Indexed property setter is not supported') ||\r\n    event.reason.message.includes('Cannot read properties of null') ||\r\n    event.reason.message.includes('Cannot read property \\'style\\'')\r\n  )) {\r\n    console.warn('DOM/Style Promise Rejection caught and handled:', event.reason.message);\r\n    event.preventDefault();\r\n  }\r\n});\r\n// Fast loading component for lazy routes\r\nconst FastLoader = () => (\r\n  <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n    <div className=\"text-center\">\r\n      <div className=\"relative\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n        <div className=\"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"></div>\r\n      </div>\r\n      <p className=\"text-gray-600 font-medium\">Loading page...</p>\r\n      <p className=\"text-gray-400 text-sm mt-2\">Please wait a moment</p>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nfunction App() {\r\n  const { loading } = useSelector((state) => state.loader);\r\n\r\n  // Global mobile responsive fixes for ALL pages\r\n  React.useEffect(() => {\r\n    const globalMobileStyles = document.createElement('style');\r\n    globalMobileStyles.textContent = `\r\n      /* GLOBAL MOBILE RESPONSIVE FIXES FOR ALL PAGES */\r\n      @media (max-width: 768px) {\r\n        /* SHOW hamburger menu on top left */\r\n        .lg\\\\:hidden {\r\n          display: flex !important;\r\n          visibility: visible !important;\r\n          opacity: 1 !important;\r\n          position: fixed !important;\r\n          top: 6px !important;\r\n          left: 6px !important;\r\n          z-index: 10001 !important;\r\n        }\r\n\r\n        .lg\\\\:hidden button {\r\n          display: flex !important;\r\n          visibility: visible !important;\r\n          opacity: 1 !important;\r\n          background: rgba(255, 255, 255, 0.95) !important;\r\n          border-radius: 8px !important;\r\n          padding: 6px !important;\r\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;\r\n          border: 1px solid rgba(0, 0, 0, 0.1) !important;\r\n          width: 32px !important;\r\n          height: 32px !important;\r\n          align-items: center !important;\r\n          justify-content: center !important;\r\n        }\r\n\r\n        /* Desktop-like header layout for mobile on ALL pages */\r\n        .nav-modern,\r\n        header,\r\n        .safe-header-animation {\r\n          position: fixed !important;\r\n          top: 0 !important;\r\n          left: 0 !important;\r\n          right: 0 !important;\r\n          height: 48px !important;\r\n          min-height: 48px !important;\r\n          max-height: 48px !important;\r\n          display: flex !important;\r\n          align-items: center !important;\r\n          justify-content: center !important;\r\n          padding: 0 44px 0 44px !important;\r\n          z-index: 10000 !important;\r\n          background: rgba(255, 255, 255, 0.98) !important;\r\n          backdrop-filter: blur(15px) !important;\r\n          border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;\r\n          box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1) !important;\r\n        }\r\n\r\n        /* Header content layout for ALL pages */\r\n        .nav-modern .flex,\r\n        header .flex {\r\n          width: 100% !important;\r\n          display: flex !important;\r\n          align-items: center !important;\r\n          justify-content: space-between !important;\r\n          height: 48px !important;\r\n          position: relative !important;\r\n        }\r\n\r\n        /* Logo section - CENTERED between hamburger and profile */\r\n        .nav-modern .flex > div:first-child,\r\n        header .flex > div:first-child {\r\n          position: absolute !important;\r\n          left: 50% !important;\r\n          transform: translateX(-50%) !important;\r\n          display: flex !important;\r\n          align-items: center !important;\r\n          justify-content: center !important;\r\n          gap: 8px !important;\r\n          z-index: 1 !important;\r\n        }\r\n\r\n        /* BRAINWAVE text - force single line and center */\r\n        .brainwave-heading-enhanced,\r\n        .brainwave-heading,\r\n        h1 {\r\n          white-space: nowrap !important;\r\n          overflow: hidden !important;\r\n          text-overflow: ellipsis !important;\r\n          font-size: 1.1rem !important;\r\n          line-height: 1.2 !important;\r\n          text-align: center !important;\r\n        }\r\n\r\n        /* Flag image - proper sizing */\r\n        .nav-modern img[alt*=\"flag\"],\r\n        header img[alt*=\"flag\"],\r\n        .flag-image {\r\n          width: 24px !important;\r\n          height: 16px !important;\r\n          border-radius: 2px !important;\r\n        }\r\n\r\n        /* Right section (profile + bell) - positioned on the right */\r\n        .nav-modern .flex > div:last-child,\r\n        header .flex > div:last-child {\r\n          position: absolute !important;\r\n          right: 0 !important;\r\n          display: flex !important;\r\n          align-items: center !important;\r\n          gap: 8px !important;\r\n          flex-direction: row !important;\r\n        }\r\n\r\n        /* Profile section - name and class */\r\n        .user-info,\r\n        .profile-info,\r\n        .nav-modern .flex > div:last-child > div:first-child,\r\n        header .flex > div:last-child > div:first-child {\r\n          display: flex !important;\r\n          flex-direction: column !important;\r\n          align-items: flex-end !important;\r\n          text-align: right !important;\r\n          margin-right: 4px !important;\r\n        }\r\n\r\n        /* Profile name styling */\r\n        .user-name,\r\n        .profile-name {\r\n          font-size: 0.8rem !important;\r\n          font-weight: 600 !important;\r\n          color: #1f2937 !important;\r\n          line-height: 1.1 !important;\r\n          margin: 0 !important;\r\n        }\r\n\r\n        /* User class styling */\r\n        .user-class,\r\n        .profile-class {\r\n          font-size: 0.7rem !important;\r\n          color: #6b7280 !important;\r\n          line-height: 1.1 !important;\r\n          margin: 0 !important;\r\n        }\r\n\r\n        /* Bell icon size reduction - make it much smaller */\r\n        .notification-bell-button .w-5,\r\n        .notification-bell-button .h-5 {\r\n          width: 10px !important;\r\n          height: 10px !important;\r\n        }\r\n\r\n        .notification-bell-button {\r\n          padding: 3px !important;\r\n          width: 20px !important;\r\n          height: 20px !important;\r\n          border-radius: 6px !important;\r\n        }\r\n\r\n        /* Force sidebar text to be white on ALL pages */\r\n        .sidebar,\r\n        .mobile-sidebar,\r\n        .modern-sidebar,\r\n        [class*=\"sidebar\"],\r\n        .ant-menu,\r\n        .ant-menu-item {\r\n          background: rgba(0, 0, 0, 0.95) !important;\r\n          color: #ffffff !important;\r\n          z-index: 8888 !important;\r\n        }\r\n\r\n        .sidebar *,\r\n        .mobile-sidebar *,\r\n        .modern-sidebar *,\r\n        [class*=\"sidebar\"] *,\r\n        .ant-menu *,\r\n        .ant-menu-item *,\r\n        .sidebar .menu-item,\r\n        .mobile-sidebar .menu-item,\r\n        .modern-sidebar .menu-item,\r\n        .sidebar a,\r\n        .mobile-sidebar a,\r\n        .modern-sidebar a,\r\n        .sidebar span,\r\n        .mobile-sidebar span,\r\n        .modern-sidebar span,\r\n        .sidebar div,\r\n        .mobile-sidebar div,\r\n        .modern-sidebar div,\r\n        .sidebar p,\r\n        .mobile-sidebar p,\r\n        .modern-sidebar p,\r\n        .sidebar h1, .sidebar h2, .sidebar h3, .sidebar h4, .sidebar h5, .sidebar h6,\r\n        .mobile-sidebar h1, .mobile-sidebar h2, .mobile-sidebar h3, .mobile-sidebar h4, .mobile-sidebar h5, .mobile-sidebar h6,\r\n        .modern-sidebar h1, .modern-sidebar h2, .modern-sidebar h3, .modern-sidebar h4, .modern-sidebar h5, .modern-sidebar h6,\r\n        .sidebar .text-gray-100,\r\n        .sidebar .text-gray-200,\r\n        .sidebar .text-gray-300,\r\n        .sidebar .text-gray-400,\r\n        .sidebar .text-gray-500,\r\n        .sidebar .text-gray-600,\r\n        .sidebar .text-gray-700,\r\n        .sidebar .text-gray-800,\r\n        .sidebar .text-gray-900,\r\n        .mobile-sidebar .text-gray-100,\r\n        .mobile-sidebar .text-gray-200,\r\n        .mobile-sidebar .text-gray-300,\r\n        .mobile-sidebar .text-gray-400,\r\n        .mobile-sidebar .text-gray-500,\r\n        .mobile-sidebar .text-gray-600,\r\n        .mobile-sidebar .text-gray-700,\r\n        .mobile-sidebar .text-gray-800,\r\n        .mobile-sidebar .text-gray-900,\r\n        .modern-sidebar .text-gray-100,\r\n        .modern-sidebar .text-gray-200,\r\n        .modern-sidebar .text-gray-300,\r\n        .modern-sidebar .text-gray-400,\r\n        .modern-sidebar .text-gray-500,\r\n        .modern-sidebar .text-gray-600,\r\n        .modern-sidebar .text-gray-700,\r\n        .modern-sidebar .text-gray-800,\r\n        .modern-sidebar .text-gray-900 {\r\n          color: #ffffff !important;\r\n          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;\r\n          font-weight: 500 !important;\r\n        }\r\n\r\n        /* Sidebar hover effects */\r\n        .sidebar .menu-item:hover,\r\n        .mobile-sidebar .menu-item:hover,\r\n        .modern-sidebar .menu-item:hover,\r\n        .sidebar a:hover,\r\n        .mobile-sidebar a:hover,\r\n        .modern-sidebar a:hover {\r\n          color: #ffffff !important;\r\n          background: rgba(255, 255, 255, 0.2) !important;\r\n          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;\r\n        }\r\n\r\n        /* Adjust ALL page content to not overlap with header */\r\n        .min-h-screen,\r\n        .study-material-modern,\r\n        .container-modern,\r\n        .layout,\r\n        .layout-modern,\r\n        main,\r\n        .body,\r\n        .video-lessons-container {\r\n          margin-top: 48px !important;\r\n          padding-top: 8px !important;\r\n        }\r\n\r\n        /* Remove gaps between header and content on ALL pages */\r\n        .modern-header,\r\n        .page-header,\r\n        .content-header {\r\n          margin-top: 0px !important;\r\n          padding-top: 8px !important;\r\n        }\r\n\r\n        .tabs-container,\r\n        .content-container,\r\n        .material-grid,\r\n        .pdf-grid {\r\n          margin-top: 0px !important;\r\n          padding-top: 8px !important;\r\n        }\r\n\r\n        /* Center ALL modals */\r\n        .ant-modal,\r\n        .modal,\r\n        .pdf-modal,\r\n        .study-modal,\r\n        .material-modal,\r\n        .quiz-modal,\r\n        .marking-modal,\r\n        .result-modal {\r\n          display: flex !important;\r\n          align-items: center !important;\r\n          justify-content: center !important;\r\n          top: 0 !important;\r\n          padding-top: 0 !important;\r\n        }\r\n\r\n        .ant-modal-content,\r\n        .modal-content,\r\n        .pdf-modal-content,\r\n        .study-modal-content,\r\n        .material-modal-content,\r\n        .quiz-modal-content,\r\n        .marking-modal-content,\r\n        .result-modal-content {\r\n          margin: 0 auto !important;\r\n          position: relative !important;\r\n          top: auto !important;\r\n          transform: none !important;\r\n        }\r\n\r\n        .ant-modal-wrap {\r\n          display: flex !important;\r\n          align-items: center !important;\r\n          justify-content: center !important;\r\n          min-height: 100vh !important;\r\n        }\r\n      }\r\n    `;\r\n    document.head.appendChild(globalMobileStyles);\r\n\r\n    return () => {\r\n      document.head.removeChild(globalMobileStyles);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <ErrorBoundary>\r\n      <ThemeProvider>\r\n        <LanguageProvider>\r\n          {loading && <Loader />}\r\n        <BrowserRouter>\r\n        <Routes>\r\n          {/* Common Routes */}\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route path=\"/register\" element={<Register />} />\r\n          <Route path=\"/\" element={<Home />} />\r\n          <Route path=\"/ranking-demo\" element={<RankingDemo />} />\r\n\r\n\r\n\r\n          <Route path=\"/test\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <Test />\r\n            </Suspense>\r\n          } />\r\n          <Route\r\n            path=\"/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Forum />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* User Routes */}\r\n          <Route\r\n            path=\"/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/subscription\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Subscription />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/subscription\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Subscription />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/hub\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Hub />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/quiz\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Quiz />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/write-exam/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <WriteExam />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/result\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <QuizResult />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* New Quiz Routes */}\r\n\r\n          <Route\r\n            path=\"/quiz/:id/play\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizPlay />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Math Test Route */}\r\n          <Route\r\n            path=\"/math-test\"\r\n            element={\r\n              <Suspense fallback={<FastLoader />}>\r\n                <MathTest />\r\n              </Suspense>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <UserReports />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/study-material\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <StudyMaterial />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/video-lessons\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <VideoLessons />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/skills\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Skills />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/ranking\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <RankingErrorBoundary>\r\n                    <Ranking />\r\n                  </RankingErrorBoundary>\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n\r\n          {/* Admin Routes */}\r\n          <Route\r\n            path=\"/admin/dashboard\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminDashboard />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/users\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Users />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Exams />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams/add\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/exams/edit/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/study-materials\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminStudyMaterials />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/skills\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminSkills />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/videos\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminVideos />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/video-lessons\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminVideoLessons />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminReports />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/notifications\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminNotifications />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminProfile />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminForum />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/debug\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <DebugAuth />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n        </Routes>\r\n      </BrowserRouter>\r\n        </LanguageProvider>\r\n    </ThemeProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,IAAI,QAAQ,OAAO;AAC7C,OAAO,yBAAyB;AAChC,OAAO,8BAA8B;AACrC,OAAO,gCAAgC;AACvC,OAAO,iCAAiC;AACxC,OAAO,qCAAqC;AAC5C,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAChC,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,mBAAmB,MAAM,kCAAkC;;AAElE;AACA,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,IAAI,MAAM,qBAAqB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,IAAI,gBAAGhB,IAAI,CAAAiB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAA/CF,IAAI;AACV,MAAMG,QAAQ,gBAAGnB,IAAI,CAAAoB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,GAAA,GAA5DF,QAAQ;AACd,MAAMG,UAAU,gBAAGtB,IAAI,CAAAuB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,GAAA,GAAhEF,UAAU;AAChB,MAAMG,KAAK,gBAAGzB,IAAI,CAAA0B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,GAAA,GAAlDF,KAAK;AACX,MAAMG,WAAW,gBAAG5B,IAAI,CAAA6B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAApEF,WAAW;AACjB,MAAMG,KAAK,gBAAG/B,IAAI,CAAAgC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAlDF,KAAK;AACX,MAAMG,cAAc,gBAAGlC,IAAI,CAAAmC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA/DF,cAAc;AAEpB,MAAMG,SAAS,gBAAGrC,IAAI,CAAAsC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAGxC,IAAI,CAAAyC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAACC,IAAA,GAA7DF,WAAW;AACjB,MAAMG,YAAY,gBAAG3C,IAAI,CAAA4C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAhEF,YAAY;AAClB,MAAMG,aAAa,gBAAG9C,IAAI,CAAA+C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAjEF,aAAa;AACnB,MAAMG,YAAY,gBAAGjD,IAAI,CAAAkD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAA/DF,YAAY;AAClB,MAAMG,MAAM,gBAAGpD,IAAI,CAAAqD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAnDF,MAAM;AACZ,MAAMG,OAAO,gBAAGvD,IAAI,CAAAwD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,oBAAoB,gBAAG1D,IAAI,CAAA2D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAA/EF,oBAAoB;AAC1B,MAAMG,OAAO,gBAAG7D,IAAI,CAAA8D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAvDF,OAAO;AAEb,MAAMG,KAAK,gBAAGhE,IAAI,CAAAiE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAnDF,KAAK;AACX,MAAMG,IAAI,gBAAGnE,IAAI,CAAAoE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAA/CF,IAAI;AACV,MAAMG,YAAY,gBAAGtE,IAAI,CAAAuE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAA/DF,YAAY;AAElB,MAAMG,GAAG,gBAAGzE,IAAI,CAAA0E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,IAAA,GAA7CF,GAAG;AACT,MAAMG,mBAAmB,gBAAG5E,IAAI,CAAA6E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,IAAA,GAAzEF,mBAAmB;AACzB,MAAMG,WAAW,gBAAG/E,IAAI,CAAAgF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAzDF,WAAW;AACjB,MAAMG,WAAW,gBAAGlF,IAAI,CAAAmF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAzDF,WAAW;AACjB,MAAMG,iBAAiB,gBAAGrF,IAAI,CAAAsF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAArEF,iBAAiB;AACvB,MAAMG,YAAY,gBAAGxF,IAAI,CAAAyF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAA3DF,YAAY;AAClB,MAAMG,kBAAkB,gBAAG3F,IAAI,CAAA4F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC,CAAC;AAACC,IAAA,GAA1FF,kBAAkB;AACxB,MAAMG,UAAU,gBAAG9F,IAAI,CAAA+F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAvDF,UAAU;AAChB,MAAMG,SAAS,gBAAGjG,IAAI,CAAAkG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAGpG,IAAI,CAAAqG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAApEF,WAAW;AACjB,MAAMG,QAAQ,gBAAGvG,IAAI,CAAAwG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;;AAE5D;AAAAC,IAAA,GAFMF,QAAQ;AAGdG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;EAC1C,IAAIA,KAAK,CAACC,OAAO,KACfD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,IAClEF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IACxDF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,CACzD,EAAE;IACDC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEJ,KAAK,CAACC,OAAO,CAAC;IAClED,KAAK,CAACK,cAAc,CAAC,CAAC;IACtB,OAAO,KAAK;EACd;AACF,CAAC,CAAC;;AAEF;AACAP,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;EACvD,IAAIA,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACM,MAAM,CAACL,OAAO,KACtCD,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,IACzEF,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IAC/DF,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,CAChE,EAAE;IACDC,OAAO,CAACC,IAAI,CAAC,iDAAiD,EAAEJ,KAAK,CAACM,MAAM,CAACL,OAAO,CAAC;IACrFD,KAAK,CAACK,cAAc,CAAC,CAAC;EACxB;AACF,CAAC,CAAC;AACF;AACA,MAAME,UAAU,GAAGA,CAAA,kBACjBpG,OAAA;EAAKqG,SAAS,EAAC,4FAA4F;EAAAC,QAAA,eACzGtG,OAAA;IAAKqG,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BtG,OAAA;MAAKqG,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBtG,OAAA;QAAKqG,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnG1G,OAAA;QAAKqG,SAAS,EAAC;MAA0F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7G,CAAC,eACN1G,OAAA;MAAGqG,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC5D1G,OAAA;MAAGqG,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/D;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,IAAA,GAXIP,UAAU;AAahB,SAASQ,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC;EAAQ,CAAC,GAAGvH,WAAW,CAAEwH,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;;EAExD;EACAjI,KAAK,CAACkI,SAAS,CAAC,MAAM;IACpB,MAAMC,kBAAkB,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC1DF,kBAAkB,CAACG,WAAW,GAAI;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,kBAAkB,CAAC;IAE7C,OAAO,MAAM;MACXC,QAAQ,CAACG,IAAI,CAACE,WAAW,CAACN,kBAAkB,CAAC;IAC/C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACElH,OAAA,CAACN,aAAa;IAAA4G,QAAA,eACZtG,OAAA,CAACR,aAAa;MAAA8G,QAAA,eACZtG,OAAA,CAACP,gBAAgB;QAAA6G,QAAA,GACdQ,OAAO,iBAAI9G,OAAA,CAACV,MAAM;UAAAiH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxB1G,OAAA,CAACd,aAAa;UAAAoH,QAAA,eACdtG,OAAA,CAACb,MAAM;YAAAmH,QAAA,gBAELtG,OAAA,CAACZ,KAAK;cAACqI,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAE1H,OAAA,CAACJ,KAAK;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3C1G,OAAA,CAACZ,KAAK;cAACqI,IAAI,EAAC,WAAW;cAACC,OAAO,eAAE1H,OAAA,CAACH,QAAQ;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD1G,OAAA,CAACZ,KAAK;cAACqI,IAAI,EAAC,GAAG;cAACC,OAAO,eAAE1H,OAAA,CAACF,IAAI;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrC1G,OAAA,CAACZ,KAAK;cAACqI,IAAI,EAAC,eAAe;cAACC,OAAO,eAAE1H,OAAA,CAACqF,WAAW;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAIxD1G,OAAA,CAACZ,KAAK;cAACqI,IAAI,EAAC,OAAO;cAACC,OAAO,eACzB1H,OAAA,CAAChB,QAAQ;gBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCtG,OAAA,CAACoD,IAAI;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJ1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,QAAQ;cACbC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAACiD,KAAK;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,UAAU;cACfC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAAC8C,OAAO;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAAC8C,OAAO;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAACuD,YAAY;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,oBAAoB;cACzBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAACuD,YAAY;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,WAAW;cAChBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAAC0D,GAAG;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,YAAY;cACjBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAACC,IAAI;oBAAAsG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,sBAAsB;cAC3BC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAACsB,SAAS;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,kBAAkB;cACvBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAACO,UAAU;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAIF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,gBAAgB;cACrBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACI,QAAQ;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,YAAY;cACjBC,OAAO,eACL1H,OAAA,CAAChB,QAAQ;gBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCtG,OAAA,CAACwF,QAAQ;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAACyB,WAAW;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,sBAAsB;cAC3BC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAAC+B,aAAa;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,qBAAqB;cAC1BC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAACkC,YAAY;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAACqC,MAAM;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAAChB,QAAQ;kBAAC2I,QAAQ,eAAE3H,OAAA,CAACoG,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjCtG,OAAA,CAAC2C,oBAAoB;oBAAA2D,QAAA,eACnBtG,OAAA,CAACwC,OAAO;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAIF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,kBAAkB;cACvBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACmB,cAAc;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACgB,KAAK;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACU,KAAK;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,kBAAkB;cACvBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACa,WAAW;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,uBAAuB;cAC5BC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACa,WAAW;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,wBAAwB;cAC7BC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAAC6D,mBAAmB;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACgE,WAAW;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACmE,WAAW;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,sBAAsB;cAC3BC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACsE,iBAAiB;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,gBAAgB;cACrBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAAC4B,YAAY;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,sBAAsB;cAC3BC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAAC4E,kBAAkB;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,gBAAgB;cACrBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACyE,YAAY;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAAC+E,UAAU;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEF1G,OAAA,CAACZ,KAAK;cACJqI,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL1H,OAAA,CAACX,cAAc;gBAAAiH,QAAA,eACbtG,OAAA,CAACL,mBAAmB;kBAAA2G,QAAA,eAClBtG,OAAA,CAACkF,SAAS;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB;AAACG,EAAA,CAnpBQD,GAAG;EAAA,QACUrH,WAAW;AAAA;AAAAqI,IAAA,GADxBhB,GAAG;AAqpBZ,eAAeA,GAAG;AAAC,IAAA1G,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAiB,IAAA,EAAAiB,IAAA;AAAAC,YAAA,CAAA3H,EAAA;AAAA2H,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA9G,IAAA;AAAA8G,YAAA,CAAA5G,IAAA;AAAA4G,YAAA,CAAA3G,IAAA;AAAA2G,YAAA,CAAAzG,IAAA;AAAAyG,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA/F,IAAA;AAAA+F,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA/C,IAAA;AAAA+C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA5C,IAAA;AAAA4C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAzC,IAAA;AAAAyC,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAAtC,IAAA;AAAAsC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAnC,IAAA;AAAAmC,YAAA,CAAAlB,IAAA;AAAAkB,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}