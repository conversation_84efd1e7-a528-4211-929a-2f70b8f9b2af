{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.user && state.user.user) {\n        console.log('✅ Found user in state.user.user');\n        return state.user.user;\n      }\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n  console.log('👤 Current user data:', user);\n  console.log('👤 User name fields:', {\n    name: user === null || user === void 0 ? void 0 : user.name,\n    firstName: user === null || user === void 0 ? void 0 : user.firstName,\n    lastName: user === null || user === void 0 ? void 0 : user.lastName,\n    username: user === null || user === void 0 ? void 0 : user.username,\n    level: user === null || user === void 0 ? void 0 : user.level,\n    class: user === null || user === void 0 ? void 0 : user.class\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    console.log('🔍 CHECKING THUMBNAIL FOR VIDEO:', video.title);\n    console.log('🔍 Full video object:', video);\n    console.log('🔍 video.thumbnail:', video.thumbnail);\n    console.log('🔍 video.thumbnailUrl:', video.thumbnailUrl);\n    console.log('🔍 video.image:', video.image);\n\n    // Priority order: Database thumbnails first, then educational video placeholder\n    const thumbnailFields = ['thumbnail', 'thumbnailUrl', 'image', 'imageUrl', 'poster', 'posterUrl', 'cover', 'coverImage', 'previewImage', 'videoThumbnail', 'thumb', 'preview'];\n\n    // Check for any valid thumbnail URL\n    for (const field of thumbnailFields) {\n      if (video[field] && typeof video[field] === 'string' && video[field].trim() !== '') {\n        const thumbnailUrl = video[field].trim();\n        console.log(`✅ FOUND THUMBNAIL in field \"${field}\":`, thumbnailUrl);\n        return thumbnailUrl;\n      }\n    }\n\n    // Generate YouTube thumbnail if it's a YouTube video\n    if (video.videoID && (video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be'))) {\n      let videoId = video.videoID;\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      const youtubeThumb = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n      console.log(`✅ GENERATED YOUTUBE THUMBNAIL:`, youtubeThumb);\n      return youtubeThumb;\n    }\n    console.log('❌ NO THUMBNAIL FOUND - using educational video placeholder');\n    // Educational video placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n  const formatTimeAgo = timestamp => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.username) || (user === null || user === void 0 ? void 0 : user.displayName) || 'Student';\n    const fullName = user !== null && user !== void 0 && user.firstName && user !== null && user !== void 0 && user.lastName ? `${user.firstName} ${user.lastName}` : userName;\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user === null || user === void 0 ? void 0 : user.name);\n    console.log('  - user?.firstName:', user === null || user === void 0 ? void 0 : user.firstName);\n    console.log('  - user?.lastName:', user === null || user === void 0 ? void 0 : user.lastName);\n    console.log('  - user?.username:', user === null || user === void 0 ? void 0 : user.username);\n    console.log('  - user?.displayName:', user === null || user === void 0 ? void 0 : user.displayName);\n    console.log('  - Final fullName:', fullName);\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userId: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userRole: user === null || user === void 0 ? void 0 : user.role,\n      isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user === null || user === void 0 ? void 0 : user.firstName,\n        lastName: user === null || user === void 0 ? void 0 : user.lastName,\n        username: user === null || user === void 0 ? void 0 : user.username,\n        email: user === null || user === void 0 ? void 0 : user.email,\n        role: user === null || user === void 0 ? void 0 : user.role,\n        avatar: (user === null || user === void 0 ? void 0 : user.avatar) || (user === null || user === void 0 ? void 0 : user.profilePicture)\n      }\n    };\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend using the correct API\n    try {\n      const videoId = video._id || video.id;\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          videoId: videoId,\n          text: newComment.trim(),\n          author: fullName,\n          avatar: (user === null || user === void 0 ? void 0 : user.avatar) || (user === null || user === void 0 ? void 0 : user.profilePicture) || fullName.charAt(0).toUpperCase(),\n          userLevel: (user === null || user === void 0 ? void 0 : user.level) || 'primary',\n          userClass: (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className)\n        })\n      });\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      const videoId = video._id || video.id;\n      setComments(prev => ({\n        ...prev,\n        [videoId]: [...(prev[videoId] || []), comment]\n      }));\n    }\n    setNewComment('');\n  };\n  const handleLikeComment = commentId => {\n    if (!(user !== null && user !== void 0 && user._id) && !(user !== null && user !== void 0 && user.id) || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    const userId = (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id);\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).map(comment => {\n        if (comment.id === commentId || comment._id === commentId) {\n          var _comment$likedBy;\n          const isLiked = (_comment$likedBy = comment.likedBy) === null || _comment$likedBy === void 0 ? void 0 : _comment$likedBy.includes(userId);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked ? comment.likedBy.filter(id => id !== userId) : [...(comment.likedBy || []), userId]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n  const handleDeleteComment = commentId => {\n    if (currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).filter(comment => comment.id !== commentId && comment._id !== commentId)\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n    setCommentsExpanded(false);\n    setNewComment('');\n    const videoId = video._id || video.id;\n\n    // Load existing comments from backend\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments/${videoId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const commentsData = await response.json();\n        console.log('✅ Loaded existing comments:', commentsData);\n        if (commentsData.success && commentsData.data && commentsData.data.comments) {\n          setComments(prev => ({\n            ...prev,\n            [videoId]: commentsData.data.comments\n          }));\n        }\n      } else {\n        console.log('⚠️ Failed to load comments from backend');\n      }\n    } catch (error) {\n      console.log('⚠️ Error loading comments:', error);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🎥 Attempting to fetch videos from database...');\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        var _response, _response2;\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n        if ((_response = response) !== null && _response !== void 0 && _response.success && (_response2 = response) !== null && _response2 !== void 0 && _response2.data) {\n          var _videos$;\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n          console.log('📹 Sample video data:', videos[0]);\n          console.log('🖼️ Sample video thumbnail:', (_videos$ = videos[0]) === null || _videos$ === void 0 ? void 0 : _videos$.thumbnail);\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - imageUrl:', videos[0].imageUrl);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n            console.log('  - thumb:', videos[0].thumb);\n            console.log('  - preview:', videos[0].preview);\n\n            // Check a few more videos to see if any have thumbnails\n            console.log('🔍 CHECKING MORE VIDEOS FOR THUMBNAILS:');\n            videos.slice(0, 5).forEach((video, index) => {\n              console.log(`Video ${index + 1} (${video.title}):`, {\n                thumbnail: video.thumbnail,\n                thumbnailUrl: video.thumbnailUrl,\n                image: video.image,\n                imageUrl: video.imageUrl,\n                poster: video.poster,\n                thumb: video.thumb,\n                preview: video.preview\n              });\n            });\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          var _response3, _response3$data, _response4, _response4$data;\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n          if ((_response3 = response) !== null && _response3 !== void 0 && (_response3$data = _response3.data) !== null && _response3$data !== void 0 && _response3$data.success && (_response4 = response) !== null && _response4 !== void 0 && (_response4$data = _response4.data) !== null && _response4$data !== void 0 && _response4$data.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          var _response5, _response5$data, _response6, _response6$data;\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n          if ((_response5 = response) !== null && _response5 !== void 0 && (_response5$data = _response5.data) !== null && _response5$data !== void 0 && _response5$data.success && (_response6 = response) !== null && _response6 !== void 0 && (_response6$data = _response6.data) !== null && _response6$data !== void 0 && _response6$data.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item => {\n              var _item$title;\n              return item.type === 'video' || item.videoUrl || item.videoID || ((_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.toLowerCase().includes('video'));\n            });\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel || !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass || !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject || !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => {\n          var _ref, _ref$charAt;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-item\",\n            children: currentVideoIndex === index ?\n            /*#__PURE__*/\n            /* Video Player - Replaces the thumbnail when playing */\n            _jsxDEV(\"div\", {\n              className: \"inline-video-player\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-style-layout\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-player\",\n                  children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    autoPlay: true,\n                    playsInline: true,\n                    preload: \"metadata\",\n                    width: \"100%\",\n                    height: \"100%\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      backgroundColor: '#000',\n                      objectFit: 'contain'\n                    },\n                    onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                    onCanPlay: () => setVideoError(null),\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 31\n                    }, this)), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 27\n                  }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                    title: video.title,\n                    frameBorder: \"0\",\n                    allowFullScreen: true,\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      border: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-error\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"error-icon\",\n                      children: \"\\u26A0\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: \"Video Unavailable\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError || \"This video cannot be played at the moment.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"youtube-video-title\",\n                    children: video.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: getSubjectName(video.subject)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 663,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Class \", video.className || video.class]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 664,\n                      columnNumber: 27\n                    }, this), video.level && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: video.level\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `youtube-action-btn ${commentsExpanded ? 'active' : ''}`,\n                      onClick: () => setCommentsExpanded(!commentsExpanded),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 677,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Comments\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDC4D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Like\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 682,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      onClick: () => setCurrentVideoIndex(null),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2715\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 688,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Close\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 689,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 23\n                }, this), commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-header\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [getCurrentVideoComments().length, \" Comments\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 698,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comment-input\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment-avatar\",\n                      children: user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: user.profileImage,\n                        alt: \"Profile\",\n                        style: {\n                          width: '100%',\n                          height: '100%',\n                          borderRadius: '50%',\n                          objectFit: 'cover'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 705,\n                        columnNumber: 33\n                      }, this) : (_ref = (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.username) || 'Student') === null || _ref === void 0 ? void 0 : (_ref$charAt = _ref.charAt(0)) === null || _ref$charAt === void 0 ? void 0 : _ref$charAt.toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 703,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                        className: \"youtube-comment-input-field\",\n                        value: newComment,\n                        onChange: e => setNewComment(e.target.value),\n                        placeholder: \"Add a comment...\",\n                        rows: \"1\",\n                        style: {\n                          minHeight: '20px',\n                          resize: 'none',\n                          overflow: 'hidden'\n                        },\n                        onInput: e => {\n                          e.target.style.height = 'auto';\n                          e.target.style.height = e.target.scrollHeight + 'px';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 715,\n                        columnNumber: 31\n                      }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-actions\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn cancel\",\n                          onClick: () => setNewComment(''),\n                          children: \"Cancel\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 733,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn submit\",\n                          onClick: handleAddComment,\n                          disabled: !newComment.trim(),\n                          children: \"Comment\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 739,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 732,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-list\",\n                    children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center',\n                        padding: '40px 0',\n                        color: '#606060'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '48px',\n                          marginBottom: '16px'\n                        },\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 755,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No comments yet. Be the first to share your thoughts!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 756,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 754,\n                      columnNumber: 31\n                    }, this) : getCurrentVideoComments().map(comment => {\n                      var _comment$author, _comment$author$charA, _comment$user, _comment$likedBy2, _comment$likedBy3;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-avatar\",\n                          children: [comment.avatar && comment.avatar.includes('http') ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: comment.avatar,\n                            alt: \"Profile\",\n                            style: {\n                              width: '100%',\n                              height: '100%',\n                              borderRadius: '50%',\n                              objectFit: 'cover'\n                            },\n                            onError: e => {\n                              e.target.style.display = 'none';\n                              e.target.nextSibling.style.display = 'flex';\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 763,\n                            columnNumber: 39\n                          }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: comment.avatar && comment.avatar.includes('http') ? 'none' : 'flex',\n                              width: '100%',\n                              height: '100%',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              fontSize: '16px',\n                              fontWeight: '600'\n                            },\n                            children: comment.avatar && !comment.avatar.includes('http') ? comment.avatar : ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 773,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 761,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-content\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-author\",\n                              children: comment.author || 'Anonymous'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 787,\n                              columnNumber: 39\n                            }, this), (comment.userRole === 'admin' || comment.isAdmin || ((_comment$user = comment.user) === null || _comment$user === void 0 ? void 0 : _comment$user.isAdmin)) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                              style: {\n                                color: '#1d9bf0',\n                                fontSize: '12px',\n                                marginLeft: '4px'\n                              },\n                              title: \"Verified Admin\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 791,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-time\",\n                              children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 793,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 786,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-text\",\n                            children: comment.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 797,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-actions\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => handleLikeComment(comment._id || comment.id),\n                              className: `youtube-comment-action ${(_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                children: (_comment$likedBy3 = comment.likedBy) !== null && _comment$likedBy3 !== void 0 && _comment$likedBy3.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 805,\n                                columnNumber: 41\n                              }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: comment.likes\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 806,\n                                columnNumber: 63\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 801,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: \"Reply\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 808,\n                              columnNumber: 39\n                            }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                children: \"Edit\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 813,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                onClick: () => {\n                                  if (window.confirm('Are you sure you want to delete this comment?')) {\n                                    handleDeleteComment(comment._id || comment.id);\n                                  }\n                                },\n                                children: \"Delete\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 816,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 800,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 785,\n                          columnNumber: 35\n                        }, this)]\n                      }, comment._id || comment.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 760,\n                        columnNumber: 33\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 752,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 19\n            }, this) :\n            /*#__PURE__*/\n            /* Video Card - Shows thumbnail when not playing */\n            _jsxDEV(\"div\", {\n              className: \"video-card\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-thumbnail\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getThumbnailUrl(video),\n                  alt: video.title,\n                  className: \"thumbnail-image\",\n                  loading: \"lazy\",\n                  onError: e => {\n                    // Use educational video placeholder instead of YouTube fallbacks\n                    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"play-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                    className: \"play-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 853,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 852,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-duration\",\n                  children: video.duration || \"Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 23\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 27\n                  }, this), \"CC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 867,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-tags\",\n                  children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"topic-tag\",\n                    children: video.topic\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 877,\n                    columnNumber: 41\n                  }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"shared-tag\",\n                    children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 879,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 897,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 894,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 535,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"sUl5UJEZU46h2aSoRLty1l9iUvM=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "getAllVideos", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoLessons", "_s", "user", "state", "console", "log", "Object", "keys", "users", "auth", "storedUser", "localStorage", "getItem", "JSON", "parse", "e", "error", "name", "firstName", "lastName", "username", "level", "class", "videos", "setVideos", "loading", "setLoading", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "comments", "setComments", "newComment", "setNewComment", "commentsExpanded", "setCommentsExpanded", "replyingTo", "setReplyingTo", "isKiswahili", "getThumbnailUrl", "video", "title", "thumbnail", "thumbnailUrl", "image", "thumbnailFields", "field", "trim", "videoID", "includes", "videoId", "match", "youtubeThumb", "getSubjectName", "subject", "subjectMap", "getCurrentVideoComments", "filteredAndSortedVideos", "_id", "id", "formatTimeAgo", "timestamp", "now", "Date", "time", "diffInSeconds", "Math", "floor", "handleAddComment", "userName", "displayName", "fullName", "comment", "toString", "text", "author", "userId", "userRole", "role", "isAdmin", "toISOString", "createdAt", "likes", "<PERSON><PERSON><PERSON>", "userProfile", "email", "avatar", "profilePicture", "response", "fetch", "process", "env", "REACT_APP_API_URL", "method", "headers", "body", "stringify", "char<PERSON>t", "toUpperCase", "userLevel", "userClass", "className", "ok", "savedComment", "json", "prev", "data", "handleLikeComment", "commentId", "map", "_comment$likedBy", "isLiked", "filter", "handleDeleteComment", "handleShowVideo", "index", "commentsData", "success", "videoUrl", "signedVideoUrl", "warn", "fetchVideos", "_response", "_response2", "_videos$", "length", "imageUrl", "poster", "posterUrl", "cover", "coverImage", "previewImage", "videoThumbnail", "thumb", "preview", "slice", "for<PERSON>ach", "message", "_response3", "_response3$data", "_response4", "_response4$data", "filters", "type", "_response5", "_response5$data", "_response6", "_response6$data", "allData", "item", "_item$title", "toLowerCase", "filtered", "matchesLevel", "matchesClass", "matchesSubject", "err", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "topic", "sort", "a", "b", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "target", "onClick", "_ref", "_ref$charAt", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "style", "backgroundColor", "objectFit", "onError", "onCanPlay", "crossOrigin", "src", "subtitles", "subtitle", "kind", "url", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "profileImage", "alt", "borderRadius", "flex", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "disabled", "textAlign", "padding", "color", "fontSize", "marginBottom", "_comment$author", "_comment$author$charA", "_comment$user", "_comment$likedBy2", "_comment$likedBy3", "display", "nextS<PERSON>ling", "alignItems", "justifyContent", "fontWeight", "marginLeft", "window", "confirm", "duration", "sharedFromClass", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.user && state.user.user) {\n        console.log('✅ Found user in state.user.user');\n        return state.user.user;\n      }\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  console.log('👤 Current user data:', user);\n  console.log('👤 User name fields:', {\n    name: user?.name,\n    firstName: user?.firstName,\n    lastName: user?.lastName,\n    username: user?.username,\n    level: user?.level,\n    class: user?.class\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    console.log('🔍 CHECKING THUMBNAIL FOR VIDEO:', video.title);\n    console.log('🔍 Full video object:', video);\n    console.log('🔍 video.thumbnail:', video.thumbnail);\n    console.log('🔍 video.thumbnailUrl:', video.thumbnailUrl);\n    console.log('🔍 video.image:', video.image);\n\n    // Priority order: Database thumbnails first, then educational video placeholder\n    const thumbnailFields = [\n      'thumbnail',\n      'thumbnailUrl',\n      'image',\n      'imageUrl',\n      'poster',\n      'posterUrl',\n      'cover',\n      'coverImage',\n      'previewImage',\n      'videoThumbnail',\n      'thumb',\n      'preview'\n    ];\n\n    // Check for any valid thumbnail URL\n    for (const field of thumbnailFields) {\n      if (video[field] && typeof video[field] === 'string' && video[field].trim() !== '') {\n        const thumbnailUrl = video[field].trim();\n        console.log(`✅ FOUND THUMBNAIL in field \"${field}\":`, thumbnailUrl);\n        return thumbnailUrl;\n      }\n    }\n\n    // Generate YouTube thumbnail if it's a YouTube video\n    if (video.videoID && (video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be'))) {\n      let videoId = video.videoID;\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      const youtubeThumb = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n      console.log(`✅ GENERATED YOUTUBE THUMBNAIL:`, youtubeThumb);\n      return youtubeThumb;\n    }\n\n    console.log('❌ NO THUMBNAIL FOUND - using educational video placeholder');\n    // Educational video placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = user?.name || user?.firstName || user?.username || user?.displayName || 'Student';\n    const fullName = user?.firstName && user?.lastName\n      ? `${user.firstName} ${user.lastName}`\n      : userName;\n\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user?.name);\n    console.log('  - user?.firstName:', user?.firstName);\n    console.log('  - user?.lastName:', user?.lastName);\n    console.log('  - user?.username:', user?.username);\n    console.log('  - user?.displayName:', user?.displayName);\n    console.log('  - Final fullName:', fullName);\n\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: user?._id || user?.id,\n      userId: user?._id || user?.id,\n      userRole: user?.role,\n      isAdmin: user?.role === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user?.firstName,\n        lastName: user?.lastName,\n        username: user?.username,\n        email: user?.email,\n        role: user?.role,\n        avatar: user?.avatar || user?.profilePicture\n      }\n    };\n\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend using the correct API\n    try {\n      const videoId = video._id || video.id;\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n        },\n        body: JSON.stringify({\n          videoId: videoId,\n          text: newComment.trim(),\n          author: fullName,\n          avatar: user?.avatar || user?.profilePicture || fullName.charAt(0).toUpperCase(),\n          userLevel: user?.level || 'primary',\n          userClass: user?.class || user?.className\n        })\n      });\n\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      const videoId = video._id || video.id;\n      setComments(prev => ({\n        ...prev,\n        [videoId]: [...(prev[videoId] || []), comment]\n      }));\n    }\n\n    setNewComment('');\n  };\n\n  const handleLikeComment = (commentId) => {\n    if (!user?._id && !user?.id || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n    const userId = user?._id || user?.id;\n\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).map(comment => {\n        if (comment.id === commentId || comment._id === commentId) {\n          const isLiked = comment.likedBy?.includes(userId);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked\n              ? comment.likedBy.filter(id => id !== userId)\n              : [...(comment.likedBy || []), userId]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n\n  const handleDeleteComment = (commentId) => {\n    if (currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).filter(comment =>\n        comment.id !== commentId && comment._id !== commentId\n      )\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n    setCommentsExpanded(false);\n    setNewComment('');\n\n    const videoId = video._id || video.id;\n\n    // Load existing comments from backend\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments/${videoId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n        },\n      });\n\n      if (response.ok) {\n        const commentsData = await response.json();\n        console.log('✅ Loaded existing comments:', commentsData);\n\n        if (commentsData.success && commentsData.data && commentsData.data.comments) {\n          setComments(prev => ({\n            ...prev,\n            [videoId]: commentsData.data.comments\n          }));\n        }\n      } else {\n        console.log('⚠️ Failed to load comments from backend');\n      }\n    } catch (error) {\n      console.log('⚠️ Error loading comments:', error);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🎥 Attempting to fetch videos from database...');\n\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n\n        if (response?.success && response?.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n          console.log('📹 Sample video data:', videos[0]);\n          console.log('🖼️ Sample video thumbnail:', videos[0]?.thumbnail);\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - imageUrl:', videos[0].imageUrl);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n            console.log('  - thumb:', videos[0].thumb);\n            console.log('  - preview:', videos[0].preview);\n\n            // Check a few more videos to see if any have thumbnails\n            console.log('🔍 CHECKING MORE VIDEOS FOR THUMBNAILS:');\n            videos.slice(0, 5).forEach((video, index) => {\n              console.log(`Video ${index + 1} (${video.title}):`, {\n                thumbnail: video.thumbnail,\n                thumbnailUrl: video.thumbnailUrl,\n                image: video.image,\n                imageUrl: video.imageUrl,\n                poster: video.poster,\n                thumb: video.thumb,\n                preview: video.preview\n              });\n            });\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item =>\n              item.type === 'video' ||\n              item.videoUrl ||\n              item.videoID ||\n              item.title?.toLowerCase().includes('video')\n            );\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' ||\n                              video.level === selectedLevel ||\n                              !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' ||\n                              video.className === selectedClass ||\n                              video.class === selectedClass ||\n                              !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' ||\n                                video.subject === selectedSubject ||\n                                !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {currentVideoIndex === index ? (\n                  /* Video Player - Replaces the thumbnail when playing */\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                          {video.level && (\n                            <>\n                              <span>•</span>\n                              <span>{video.level}</span>\n                            </>\n                          )}\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className={`youtube-action-btn ${commentsExpanded ? 'active' : ''}`}\n                            onClick={() => setCommentsExpanded(!commentsExpanded)}\n                          >\n                            <span>💬</span>\n                            <span>Comments</span>\n                          </button>\n                          <button className=\"youtube-action-btn\">\n                            <span>👍</span>\n                            <span>Like</span>\n                          </button>\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {commentsExpanded && (\n                        <div className=\"youtube-comments-section\">\n                          <div className=\"youtube-comments-header\">\n                            <span>{getCurrentVideoComments().length} Comments</span>\n                          </div>\n\n                          {/* Add Comment */}\n                          <div className=\"youtube-comment-input\">\n                            <div className=\"youtube-comment-avatar\">\n                              {user?.profileImage ? (\n                                <img\n                                  src={user.profileImage}\n                                  alt=\"Profile\"\n                                  style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }}\n                                />\n                              ) : (\n                                (user?.firstName || user?.name || user?.username || 'Student')?.charAt(0)?.toUpperCase()\n                              )}\n                            </div>\n                            <div style={{ flex: 1 }}>\n                              <textarea\n                                className=\"youtube-comment-input-field\"\n                                value={newComment}\n                                onChange={(e) => setNewComment(e.target.value)}\n                                placeholder=\"Add a comment...\"\n                                rows=\"1\"\n                                style={{\n                                  minHeight: '20px',\n                                  resize: 'none',\n                                  overflow: 'hidden'\n                                }}\n                                onInput={(e) => {\n                                  e.target.style.height = 'auto';\n                                  e.target.style.height = e.target.scrollHeight + 'px';\n                                }}\n                              />\n                              {newComment.trim() && (\n                                <div className=\"youtube-comment-actions\">\n                                  <button\n                                    className=\"youtube-comment-btn cancel\"\n                                    onClick={() => setNewComment('')}\n                                  >\n                                    Cancel\n                                  </button>\n                                  <button\n                                    className=\"youtube-comment-btn submit\"\n                                    onClick={handleAddComment}\n                                    disabled={!newComment.trim()}\n                                  >\n                                    Comment\n                                  </button>\n                                </div>\n                              )}\n                            </div>\n                          </div>\n\n                          {/* Comments List */}\n                          <div className=\"youtube-comments-list\">\n                            {getCurrentVideoComments().length === 0 ? (\n                              <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                                <p>No comments yet. Be the first to share your thoughts!</p>\n                              </div>\n                            ) : (\n                              getCurrentVideoComments().map((comment) => (\n                                <div key={comment._id || comment.id} className=\"youtube-comment\">\n                                  <div className=\"youtube-comment-avatar\">\n                                    {comment.avatar && comment.avatar.includes('http') ? (\n                                      <img\n                                        src={comment.avatar}\n                                        alt=\"Profile\"\n                                        style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }}\n                                        onError={(e) => {\n                                          e.target.style.display = 'none';\n                                          e.target.nextSibling.style.display = 'flex';\n                                        }}\n                                      />\n                                    ) : null}\n                                    <div style={{\n                                      display: comment.avatar && comment.avatar.includes('http') ? 'none' : 'flex',\n                                      width: '100%',\n                                      height: '100%',\n                                      alignItems: 'center',\n                                      justifyContent: 'center',\n                                      fontSize: '16px',\n                                      fontWeight: '600'\n                                    }}>\n                                      {comment.avatar && !comment.avatar.includes('http') ? comment.avatar : comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                    </div>\n                                  </div>\n                                  <div className=\"youtube-comment-content\">\n                                    <div className=\"youtube-comment-header\">\n                                      <span className=\"youtube-comment-author\">\n                                        {comment.author || 'Anonymous'}\n                                      </span>\n                                      {(comment.userRole === 'admin' || comment.isAdmin || comment.user?.isAdmin) && (\n                                        <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title=\"Verified Admin\" />\n                                      )}\n                                      <span className=\"youtube-comment-time\">\n                                        {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                      </span>\n                                    </div>\n                                    <div className=\"youtube-comment-text\">\n                                      {comment.text}\n                                    </div>\n                                    <div className=\"youtube-comment-actions\">\n                                      <button\n                                        onClick={() => handleLikeComment(comment._id || comment.id)}\n                                        className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                      >\n                                        <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                        {comment.likes > 0 && <span>{comment.likes}</span>}\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        Reply\n                                      </button>\n                                      {comment.user === user?._id && (\n                                        <>\n                                          <button className=\"youtube-comment-action\">\n                                            Edit\n                                          </button>\n                                          <button\n                                            className=\"youtube-comment-action\"\n                                            onClick={() => {\n                                              if (window.confirm('Are you sure you want to delete this comment?')) {\n                                                handleDeleteComment(comment._id || comment.id);\n                                              }\n                                            }}\n                                          >\n                                            Delete\n                                          </button>\n                                        </>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              ))\n                            )}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                ) : (\n                  /* Video Card - Shows thumbnail when not playing */\n                  <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                    <div className=\"video-card-thumbnail\">\n                      <img\n                        src={getThumbnailUrl(video)}\n                        alt={video.title}\n                        className=\"thumbnail-image\"\n                        loading=\"lazy\"\n                        onError={(e) => {\n                          // Use educational video placeholder instead of YouTube fallbacks\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n                        }}\n                      />\n                      <div className=\"play-overlay\">\n                        <FaPlayCircle className=\"play-icon\" />\n                      </div>\n                      <div className=\"video-duration\">\n                        {video.duration || \"Video\"}\n                      </div>\n                      {video.subtitles && video.subtitles.length > 0 && (\n                        <div className=\"subtitle-badge\">\n                          <TbInfoCircle />\n                          CC\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"video-card-content\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                        <span className=\"video-class\">\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                            : `Form ${video.className || video.class}`}\n                        </span>\n                      </div>\n                      <div className=\"video-tags\">\n                        {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                        {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                          <span className=\"shared-tag\">\n                            {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                            {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                              ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                              : `Form ${video.sharedFromClass}`}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,IAAI,GAAGd,WAAW,CAACe,KAAK,IAAI;IAChC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,KAAK,CAAC;MACpDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,MAAM,CAACC,IAAI,CAACJ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAIA,KAAK,IAAIA,KAAK,CAACD,IAAI,IAAIC,KAAK,CAACD,IAAI,CAACA,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACD,IAAI,CAACA,IAAI;MACxB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACN,IAAI,EAAE;QAC5CE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,OAAOF,KAAK,CAACK,KAAK,CAACN,IAAI;MACzB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAACP,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACM,IAAI,CAACP,IAAI;MACxB;MAEAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMK,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACd,IAAI;UACFN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,OAAOQ,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;QAC/B,CAAC,CAAC,OAAOK,CAAC,EAAE;UACVX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC,CAAC;EAEFZ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,IAAI,CAAC;EAC1CE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;IAClCY,IAAI,EAAEf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI;IAChBC,SAAS,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS;IAC1BC,QAAQ,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,QAAQ;IACxBC,QAAQ,EAAElB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ;IACxBC,KAAK,EAAEnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,KAAK;IAClBC,KAAK,EAAEpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,KAAK,EAAEW,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC0D,QAAQ,EAAEC,WAAW,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMkE,WAAW,GAAGpB,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMqB,eAAe,GAAIC,KAAK,IAAK;IACjChD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE+C,KAAK,CAACC,KAAK,CAAC;IAC5DjD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE+C,KAAK,CAAC;IAC3ChD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+C,KAAK,CAACE,SAAS,CAAC;IACnDlD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE+C,KAAK,CAACG,YAAY,CAAC;IACzDnD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+C,KAAK,CAACI,KAAK,CAAC;;IAE3C;IACA,MAAMC,eAAe,GAAG,CACtB,WAAW,EACX,cAAc,EACd,OAAO,EACP,UAAU,EACV,QAAQ,EACR,WAAW,EACX,OAAO,EACP,YAAY,EACZ,cAAc,EACd,gBAAgB,EAChB,OAAO,EACP,SAAS,CACV;;IAED;IACA,KAAK,MAAMC,KAAK,IAAID,eAAe,EAAE;MACnC,IAAIL,KAAK,CAACM,KAAK,CAAC,IAAI,OAAON,KAAK,CAACM,KAAK,CAAC,KAAK,QAAQ,IAAIN,KAAK,CAACM,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAClF,MAAMJ,YAAY,GAAGH,KAAK,CAACM,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC;QACxCvD,OAAO,CAACC,GAAG,CAAE,+BAA8BqD,KAAM,IAAG,EAAEH,YAAY,CAAC;QACnE,OAAOA,YAAY;MACrB;IACF;;IAEA;IACA,IAAIH,KAAK,CAACQ,OAAO,KAAKR,KAAK,CAACQ,OAAO,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIT,KAAK,CAACQ,OAAO,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;MAClG,IAAIC,OAAO,GAAGV,KAAK,CAACQ,OAAO;MAC3B,MAAMG,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;MACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACpC,MAAME,YAAY,GAAI,8BAA6BF,OAAQ,gBAAe;MAC1E1D,OAAO,CAACC,GAAG,CAAE,gCAA+B,EAAE2D,YAAY,CAAC;MAC3D,OAAOA,YAAY;IACrB;IAEA5D,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzE;IACA,OAAO,4cAA4c;EACrd,CAAC;EAED,MAAM4D,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEjB,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOiB,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIhC,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAMgB,KAAK,GAAGiB,uBAAuB,CAACjC,iBAAiB,CAAC;IACxD,MAAM0B,OAAO,GAAG,CAAAV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,GAAG,MAAIlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmB,EAAE;IACvCnE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyD,OAAO,CAAC;IACzD1D,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEqC,QAAQ,CAAC;IAC/C,OAAOA,QAAQ,CAACoB,OAAO,CAAC,IAAI,EAAE;EAChC,CAAC;EAED,MAAMU,aAAa,GAAIC,SAAS,IAAK;IACnC,IAAI,CAACA,SAAS,EAAE,OAAO,UAAU;IACjC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,SAAS,CAAC;IAChC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGE,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAE,cAAa;IAChF,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAE,YAAW;IACjF,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAE,WAAU;EACxD,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACpC,UAAU,CAACe,IAAI,CAAC,CAAC,IAAIvB,iBAAiB,KAAK,IAAI,EAAE;IAEtD,MAAMgB,KAAK,GAAGiB,uBAAuB,CAACjC,iBAAiB,CAAC;IAExDhC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,IAAI,CAAC;;IAEtD;IACA,MAAM+E,QAAQ,GAAG,CAAA/E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS,MAAIhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ,MAAIlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgF,WAAW,KAAI,SAAS;IAClG,MAAMC,QAAQ,GAAGjF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgB,SAAS,IAAIhB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiB,QAAQ,GAC7C,GAAEjB,IAAI,CAACgB,SAAU,IAAGhB,IAAI,CAACiB,QAAS,EAAC,GACpC8D,QAAQ;IAEZ7E,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvCD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI,CAAC;IAC1Cb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS,CAAC;IACpDd,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,QAAQ,CAAC;IAClDf,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ,CAAC;IAClDhB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgF,WAAW,CAAC;IACxD9E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE8E,QAAQ,CAAC;IAE5C,MAAMC,OAAO,GAAG;MACdb,EAAE,EAAEI,IAAI,CAACD,GAAG,CAAC,CAAC,CAACW,QAAQ,CAAC,CAAC;MACzBC,IAAI,EAAE1C,UAAU,CAACe,IAAI,CAAC,CAAC;MACvB4B,MAAM,EAAEJ,QAAQ;MAChBjF,IAAI,EAAE,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,GAAG,MAAIpE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,EAAE;MAC3BiB,MAAM,EAAE,CAAAtF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,GAAG,MAAIpE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,EAAE;MAC7BkB,QAAQ,EAAEvF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwF,IAAI;MACpBC,OAAO,EAAE,CAAAzF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwF,IAAI,MAAK,OAAO;MAC/BjB,SAAS,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;MACnCE,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE,EAAE;MACX;MACAC,WAAW,EAAE;QACX/E,IAAI,EAAEkE,QAAQ;QACdjE,SAAS,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS;QAC1BC,QAAQ,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,QAAQ;QACxBC,QAAQ,EAAElB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ;QACxB6E,KAAK,EAAE/F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F,KAAK;QAClBP,IAAI,EAAExF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwF,IAAI;QAChBQ,MAAM,EAAE,CAAAhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,MAAM,MAAIhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,cAAc;MAC9C;IACF,CAAC;IAED/F,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE+E,OAAO,CAAC;;IAElD;IACA,IAAI;MACF,MAAMtB,OAAO,GAAGV,KAAK,CAACkB,GAAG,IAAIlB,KAAK,CAACmB,EAAE;MACrC,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,qBAAoB,EAAE;QAC7GC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAAS/F,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACD+F,IAAI,EAAE9F,IAAI,CAAC+F,SAAS,CAAC;UACnB9C,OAAO,EAAEA,OAAO;UAChBwB,IAAI,EAAE1C,UAAU,CAACe,IAAI,CAAC,CAAC;UACvB4B,MAAM,EAAEJ,QAAQ;UAChBe,MAAM,EAAE,CAAAhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,MAAM,MAAIhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,cAAc,KAAIhB,QAAQ,CAAC0B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAChFC,SAAS,EAAE,CAAA7G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,KAAK,KAAI,SAAS;UACnC2F,SAAS,EAAE,CAAA9G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,MAAIpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+G,SAAS;QAC3C,CAAC;MACH,CAAC,CAAC;MAEF,IAAIb,QAAQ,CAACc,EAAE,EAAE;QACf,MAAMC,YAAY,GAAG,MAAMf,QAAQ,CAACgB,IAAI,CAAC,CAAC;QAC1ChH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE8G,YAAY,CAAC;;QAExD;QACAxE,WAAW,CAAC0E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAACvD,OAAO,GAAG,CAAC,IAAIuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,CAAC,EAAEqD,YAAY,CAACG,IAAI,IAAIH,YAAY;QACzE,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL/G,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D;QACAsC,WAAW,CAAC0E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAACvD,OAAO,GAAG,CAAC,IAAIuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,CAAC,EAAEsB,OAAO;QAC/C,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEW,KAAK,CAAC;MAC5D;MACA,MAAM8C,OAAO,GAAGV,KAAK,CAACkB,GAAG,IAAIlB,KAAK,CAACmB,EAAE;MACrC5B,WAAW,CAAC0E,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACvD,OAAO,GAAG,CAAC,IAAIuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,CAAC,EAAEsB,OAAO;MAC/C,CAAC,CAAC,CAAC;IACL;IAEAvC,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAM0E,iBAAiB,GAAIC,SAAS,IAAK;IACvC,IAAI,EAACtH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoE,GAAG,KAAI,EAACpE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqE,EAAE,KAAInC,iBAAiB,KAAK,IAAI,EAAE;IAE3D,MAAMgB,KAAK,GAAGiB,uBAAuB,CAACjC,iBAAiB,CAAC;IACxD,MAAM0B,OAAO,GAAG,CAAAV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,GAAG,MAAIlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmB,EAAE;IACvC,MAAMiB,MAAM,GAAG,CAAAtF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,GAAG,MAAIpE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,EAAE;IAEpC5B,WAAW,CAAC0E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACvD,OAAO,GAAG,CAACuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,EAAE2D,GAAG,CAACrC,OAAO,IAAI;QAC9C,IAAIA,OAAO,CAACb,EAAE,KAAKiD,SAAS,IAAIpC,OAAO,CAACd,GAAG,KAAKkD,SAAS,EAAE;UAAA,IAAAE,gBAAA;UACzD,MAAMC,OAAO,IAAAD,gBAAA,GAAGtC,OAAO,CAACW,OAAO,cAAA2B,gBAAA,uBAAfA,gBAAA,CAAiB7D,QAAQ,CAAC2B,MAAM,CAAC;UACjD,OAAO;YACL,GAAGJ,OAAO;YACVU,KAAK,EAAE6B,OAAO,GAAGvC,OAAO,CAACU,KAAK,GAAG,CAAC,GAAGV,OAAO,CAACU,KAAK,GAAG,CAAC;YACtDC,OAAO,EAAE4B,OAAO,GACZvC,OAAO,CAACW,OAAO,CAAC6B,MAAM,CAACrD,EAAE,IAAIA,EAAE,KAAKiB,MAAM,CAAC,GAC3C,CAAC,IAAIJ,OAAO,CAACW,OAAO,IAAI,EAAE,CAAC,EAAEP,MAAM;UACzC,CAAC;QACH;QACA,OAAOJ,OAAO;MAChB,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMyC,mBAAmB,GAAIL,SAAS,IAAK;IACzC,IAAIpF,iBAAiB,KAAK,IAAI,EAAE;IAEhC,MAAMgB,KAAK,GAAGiB,uBAAuB,CAACjC,iBAAiB,CAAC;IACxD,MAAM0B,OAAO,GAAG,CAAAV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,GAAG,MAAIlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmB,EAAE;IAEvC5B,WAAW,CAAC0E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACvD,OAAO,GAAG,CAACuD,IAAI,CAACvD,OAAO,CAAC,IAAI,EAAE,EAAE8D,MAAM,CAACxC,OAAO,IAC7CA,OAAO,CAACb,EAAE,KAAKiD,SAAS,IAAIpC,OAAO,CAACd,GAAG,KAAKkD,SAC9C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAM3E,KAAK,GAAGiB,uBAAuB,CAAC0D,KAAK,CAAC;IAC5C1F,oBAAoB,CAAC0F,KAAK,CAAC;IAC3BtF,aAAa,CAAC,IAAI,CAAC;IACnBM,mBAAmB,CAAC,KAAK,CAAC;IAC1BF,aAAa,CAAC,EAAE,CAAC;IAEjB,MAAMiB,OAAO,GAAGV,KAAK,CAACkB,GAAG,IAAIlB,KAAK,CAACmB,EAAE;;IAErC;IACA,IAAI;MACF,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,uBAAsB1C,OAAQ,EAAC,EAAE;QACxH2C,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAAS/F,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D;MACF,CAAC,CAAC;MAEF,IAAIwF,QAAQ,CAACc,EAAE,EAAE;QACf,MAAMc,YAAY,GAAG,MAAM5B,QAAQ,CAACgB,IAAI,CAAC,CAAC;QAC1ChH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE2H,YAAY,CAAC;QAExD,IAAIA,YAAY,CAACC,OAAO,IAAID,YAAY,CAACV,IAAI,IAAIU,YAAY,CAACV,IAAI,CAAC5E,QAAQ,EAAE;UAC3EC,WAAW,CAAC0E,IAAI,KAAK;YACnB,GAAGA,IAAI;YACP,CAACvD,OAAO,GAAGkE,YAAY,CAACV,IAAI,CAAC5E;UAC/B,CAAC,CAAC,CAAC;QACL;MACF,CAAC,MAAM;QACLtC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAAC;IAClD;;IAEA;IACA,IAAIoC,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE8E,QAAQ,KAAK9E,KAAK,CAAC8E,QAAQ,CAACrE,QAAQ,CAAC,eAAe,CAAC,IAAIT,KAAK,CAAC8E,QAAQ,CAACrE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAT,KAAK,CAAC+E,cAAc,GAAG/E,KAAK,CAAC8E,QAAQ;MACvC,CAAC,CAAC,OAAOlH,KAAK,EAAE;QACdZ,OAAO,CAACgI,IAAI,CAAC,8CAA8C,CAAC;QAC5DhF,KAAK,CAAC+E,cAAc,GAAG/E,KAAK,CAAC8E,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGlJ,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFuC,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MAEdvB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI+F,QAAQ,GAAG,IAAI;MACnB,IAAI7E,MAAM,GAAG,EAAE;;MAEf;MACA,IAAI;QAAA,IAAA+G,SAAA,EAAAC,UAAA;QACFnI,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD+F,QAAQ,GAAG,MAAMzG,YAAY,CAAC,CAAC;QAC/BS,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE+F,QAAQ,CAAC;QAE/C,IAAI,CAAAkC,SAAA,GAAAlC,QAAQ,cAAAkC,SAAA,eAARA,SAAA,CAAUL,OAAO,KAAAM,UAAA,GAAInC,QAAQ,cAAAmC,UAAA,eAARA,UAAA,CAAUjB,IAAI,EAAE;UAAA,IAAAkB,QAAA;UACvCjH,MAAM,GAAG6E,QAAQ,CAACkB,IAAI;UACtBlH,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEkB,MAAM,CAACkH,MAAM,CAAC;UAC7ErI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC;UAC/CnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,GAAAmI,QAAA,GAAEjH,MAAM,CAAC,CAAC,CAAC,cAAAiH,QAAA,uBAATA,QAAA,CAAWlF,SAAS,CAAC;;UAEhE;UACA,IAAI/B,MAAM,CAACkH,MAAM,GAAG,CAAC,EAAE;YACrBrI,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC;YACnDnB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,MAAM,CAACC,IAAI,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3DnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;YACzCD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC+B,SAAS,CAAC;YAClDlD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACgC,YAAY,CAAC;YACxDnD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACiC,KAAK,CAAC;YAC1CpD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACmH,QAAQ,CAAC;YAChDtI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACoH,MAAM,CAAC;YAC5CvI,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACqH,SAAS,CAAC;YAClDxI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACsH,KAAK,CAAC;YAC1CzI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACuH,UAAU,CAAC;YACpD1I,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACwH,YAAY,CAAC;YACxD3I,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAACyH,cAAc,CAAC;YAC5D5I,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC0H,KAAK,CAAC;YAC1C7I,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC2H,OAAO,CAAC;;YAE9C;YACA9I,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtDkB,MAAM,CAAC4H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAChG,KAAK,EAAE2E,KAAK,KAAK;cAC3C3H,OAAO,CAACC,GAAG,CAAE,SAAQ0H,KAAK,GAAG,CAAE,KAAI3E,KAAK,CAACC,KAAM,IAAG,EAAE;gBAClDC,SAAS,EAAEF,KAAK,CAACE,SAAS;gBAC1BC,YAAY,EAAEH,KAAK,CAACG,YAAY;gBAChCC,KAAK,EAAEJ,KAAK,CAACI,KAAK;gBAClBkF,QAAQ,EAAEtF,KAAK,CAACsF,QAAQ;gBACxBC,MAAM,EAAEvF,KAAK,CAACuF,MAAM;gBACpBM,KAAK,EAAE7F,KAAK,CAAC6F,KAAK;gBAClBC,OAAO,EAAE9F,KAAK,CAAC8F;cACjB,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC,OAAOlI,KAAK,EAAE;QACdZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEW,KAAK,CAACqI,OAAO,CAAC;MACtD;;MAEA;MACA,IAAI9H,MAAM,CAACkH,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAa,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFrJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,MAAMqJ,OAAO,GAAG;YACdrI,KAAK,EAAES,aAAa;YACpB6H,IAAI,EAAE;UACR,CAAC;UAEDvD,QAAQ,GAAG,MAAM1G,gBAAgB,CAACgK,OAAO,CAAC;UAC1CtJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE+F,QAAQ,CAAC;UAEnD,IAAI,CAAAkD,UAAA,GAAAlD,QAAQ,cAAAkD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUhC,IAAI,cAAAiC,eAAA,eAAdA,eAAA,CAAgBtB,OAAO,KAAAuB,UAAA,GAAIpD,QAAQ,cAAAoD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUlC,IAAI,cAAAmC,eAAA,eAAdA,eAAA,CAAgBnC,IAAI,EAAE;YACnD/F,MAAM,GAAG6E,QAAQ,CAACkB,IAAI,CAACA,IAAI;YAC3BlH,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEkB,MAAM,CAACkH,MAAM,CAAC;UACnF;QACF,CAAC,CAAC,OAAOzH,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAACqI,OAAO,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI9H,MAAM,CAACkH,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAmB,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACF3J,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D+F,QAAQ,GAAG,MAAM1G,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACrCU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE+F,QAAQ,CAAC;UAEhE,IAAI,CAAAwD,UAAA,GAAAxD,QAAQ,cAAAwD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUtC,IAAI,cAAAuC,eAAA,eAAdA,eAAA,CAAgB5B,OAAO,KAAA6B,UAAA,GAAI1D,QAAQ,cAAA0D,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUxC,IAAI,cAAAyC,eAAA,eAAdA,eAAA,CAAgBzC,IAAI,EAAE;YACnD;YACA,MAAM0C,OAAO,GAAG5D,QAAQ,CAACkB,IAAI,CAACA,IAAI;YAClC/F,MAAM,GAAGyI,OAAO,CAACpC,MAAM,CAACqC,IAAI;cAAA,IAAAC,WAAA;cAAA,OAC1BD,IAAI,CAACN,IAAI,KAAK,OAAO,IACrBM,IAAI,CAAC/B,QAAQ,IACb+B,IAAI,CAACrG,OAAO,MAAAsG,WAAA,GACZD,IAAI,CAAC5G,KAAK,cAAA6G,WAAA,uBAAVA,WAAA,CAAYC,WAAW,CAAC,CAAC,CAACtG,QAAQ,CAAC,OAAO,CAAC;YAAA,CAC7C,CAAC;YACDzD,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEkB,MAAM,CAACkH,MAAM,CAAC;UAC1E;QACF,CAAC,CAAC,OAAOzH,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEW,KAAK,CAACqI,OAAO,CAAC;QACvE;MACF;;MAEA;MACA,IAAI9H,MAAM,CAACkH,MAAM,GAAG,CAAC,EAAE;QACrB,MAAM2B,QAAQ,GAAG7I,MAAM,CAACqG,MAAM,CAACxE,KAAK,IAAI;UACtC,MAAMiH,YAAY,GAAGvI,aAAa,KAAK,KAAK,IACxBsB,KAAK,CAAC/B,KAAK,KAAKS,aAAa,IAC7B,CAACsB,KAAK,CAAC/B,KAAK,CAAC,CAAC;;UAElC,MAAMiJ,YAAY,GAAGtI,aAAa,KAAK,KAAK,IACxBoB,KAAK,CAAC6D,SAAS,KAAKjF,aAAa,IACjCoB,KAAK,CAAC9B,KAAK,KAAKU,aAAa,IAC7B,CAACoB,KAAK,CAAC6D,SAAS,CAAC,CAAC;;UAEtC,MAAMsD,cAAc,GAAGrI,eAAe,KAAK,KAAK,IAC1BkB,KAAK,CAACc,OAAO,KAAKhC,eAAe,IACjC,CAACkB,KAAK,CAACc,OAAO,CAAC,CAAC;;UAEtC,OAAOmG,YAAY,IAAIC,YAAY,IAAIC,cAAc;QACvD,CAAC,CAAC;QAEF/I,SAAS,CAAC4I,QAAQ,CAAC;QACnBhK,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+J,QAAQ,CAAC3B,MAAM,EAAE,QAAQ,CAAC;QAErE,IAAI2B,QAAQ,CAAC3B,MAAM,KAAK,CAAC,EAAE;UACzB9G,QAAQ,CAAC,wEAAwE,CAAC;QACpF;MACF,CAAC,MAAM;QACL;QACAvB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAElCsB,QAAQ,CAAC,yFAAyF,CAAC;QACnGH,SAAS,CAAC,EAAE,CAAC;MACf;IAEF,CAAC,CAAC,OAAOgJ,GAAG,EAAE;MACZpK,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEwJ,GAAG,CAAC;MACvD7I,QAAQ,CAAC,0DAA0D,CAAC;MACpEH,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACI,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAMmC,uBAAuB,GAAGnF,OAAO,CAAC,MAAM;IAC5C,IAAIkL,QAAQ,GAAG7I,MAAM,CAACqG,MAAM,CAACxE,KAAK,IAAI;MAAA,IAAAqH,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAAChJ,UAAU,MAAA6I,YAAA,GAC/BrH,KAAK,CAACC,KAAK,cAAAoH,YAAA,uBAAXA,YAAA,CAAaN,WAAW,CAAC,CAAC,CAACtG,QAAQ,CAACjC,UAAU,CAACuI,WAAW,CAAC,CAAC,CAAC,OAAAO,cAAA,GAC7DtH,KAAK,CAACc,OAAO,cAAAwG,cAAA,uBAAbA,cAAA,CAAeP,WAAW,CAAC,CAAC,CAACtG,QAAQ,CAACjC,UAAU,CAACuI,WAAW,CAAC,CAAC,CAAC,OAAAQ,YAAA,GAC/DvH,KAAK,CAACyH,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaR,WAAW,CAAC,CAAC,CAACtG,QAAQ,CAACjC,UAAU,CAACuI,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAME,YAAY,GAAGvI,aAAa,KAAK,KAAK,IAAIsB,KAAK,CAAC/B,KAAK,KAAKS,aAAa;MAC7E,MAAMwI,YAAY,GAAGtI,aAAa,KAAK,KAAK,IAAIoB,KAAK,CAAC6D,SAAS,KAAKjF,aAAa,IAAIoB,KAAK,CAAC9B,KAAK,KAAKU,aAAa;MAClH,MAAMuI,cAAc,GAAGrI,eAAe,KAAK,KAAK,IAAIkB,KAAK,CAACc,OAAO,KAAKhC,eAAe;MAErF,OAAO0I,aAAa,IAAIP,YAAY,IAAIC,YAAY,IAAIC,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOH,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrG,IAAI,CAACqG,CAAC,CAACnF,SAAS,CAAC,GAAG,IAAIlB,IAAI,CAACoG,CAAC,CAAClF,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAACtE,MAAM,EAAEK,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACAjD,SAAS,CAAC,MAAM;IACdoJ,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACApJ,SAAS,CAAC,MAAM;IACdoJ,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACvG,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAEmG,WAAW,CAAC,CAAC;EAEhE,oBACExI,OAAA;IAAKoH,SAAS,EAAC,yBAAyB;IAAAgE,QAAA,gBACtCpL,OAAA;MAAKoH,SAAS,EAAC,sBAAsB;MAAAgE,QAAA,gBACnCpL,OAAA;QAAAoL,QAAA,EAAK/H,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAAgI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DxL,OAAA;QAAAoL,QAAA,EAAI/H,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAAgI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGNxL,OAAA;MAAKoH,SAAS,EAAC,gBAAgB;MAAAgE,QAAA,gBAC7BpL,OAAA;QAAKoH,SAAS,EAAC,gBAAgB;QAAAgE,QAAA,eAC7BpL,OAAA;UACE8J,IAAI,EAAC,MAAM;UACX2B,WAAW,EAAEpI,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClEqI,KAAK,EAAE3J,UAAW;UAClB4J,QAAQ,EAAGzK,CAAC,IAAKc,aAAa,CAACd,CAAC,CAAC0K,MAAM,CAACF,KAAK,CAAE;UAC/CtE,SAAS,EAAC;QAAc;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENxL,OAAA;QAAKoH,SAAS,EAAC,gBAAgB;QAAAgE,QAAA,gBAC7BpL,OAAA;UACE0L,KAAK,EAAEzJ,aAAc;UACrB0J,QAAQ,EAAGzK,CAAC,IAAKgB,gBAAgB,CAAChB,CAAC,CAAC0K,MAAM,CAACF,KAAK,CAAE;UAClDtE,SAAS,EAAC,eAAe;UAAAgE,QAAA,gBAEzBpL,OAAA;YAAQ0L,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE/H,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrExL,OAAA;YAAQ0L,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAE/H,WAAW,GAAG,WAAW,GAAG;UAAW;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5ExL,OAAA;YAAQ0L,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE/H,WAAW,GAAG,KAAK,GAAG;UAAU;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAETxL,OAAA;UACE0L,KAAK,EAAEvJ,aAAc;UACrBwJ,QAAQ,EAAGzK,CAAC,IAAKkB,gBAAgB,CAAClB,CAAC,CAAC0K,MAAM,CAACF,KAAK,CAAE;UAClDtE,SAAS,EAAC,eAAe;UAAAgE,QAAA,eAEzBpL,OAAA;YAAQ0L,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE/H,WAAW,GAAG,eAAe,GAAG;UAAa;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAETxL,OAAA;UACE0L,KAAK,EAAErJ,eAAgB;UACvBsJ,QAAQ,EAAGzK,CAAC,IAAKoB,kBAAkB,CAACpB,CAAC,CAAC0K,MAAM,CAACF,KAAK,CAAE;UACpDtE,SAAS,EAAC,eAAe;UAAAgE,QAAA,eAEzBpL,OAAA;YAAQ0L,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE/H,WAAW,GAAG,aAAa,GAAG;UAAc;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxL,OAAA;MAAKoH,SAAS,EAAC,eAAe;MAAAgE,QAAA,EAC3BxJ,OAAO,gBACN5B,OAAA;QAAKoH,SAAS,EAAC,eAAe;QAAAgE,QAAA,gBAC5BpL,OAAA;UAAKoH,SAAS,EAAC;QAAiB;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCxL,OAAA;UAAAoL,QAAA,EAAI/H,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJrK,KAAK,gBACPnB,OAAA;QAAKoH,SAAS,EAAC,aAAa;QAAAgE,QAAA,gBAC1BpL,OAAA,CAACL,eAAe;UAACyH,SAAS,EAAC;QAAY;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CxL,OAAA;UAAAoL,QAAA,EAAK/H,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7ExL,OAAA;UAAAoL,QAAA,EAAIjK;QAAK;UAAAkK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdxL,OAAA;UAAQ6L,OAAO,EAAErD,WAAY;UAACpB,SAAS,EAAC,WAAW;UAAAgE,QAAA,EAChD/H,WAAW,GAAG,aAAa,GAAG;QAAW;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJhH,uBAAuB,CAACoE,MAAM,GAAG,CAAC,gBACpC5I,OAAA;QAAKoH,SAAS,EAAC,aAAa;QAAAgE,QAAA,EACzB5G,uBAAuB,CAACoD,GAAG,CAAC,CAACrE,KAAK,EAAE2E,KAAK;UAAA,IAAA4D,IAAA,EAAAC,WAAA;UAAA,oBACxC/L,OAAA;YAAiBoH,SAAS,EAAC,YAAY;YAAAgE,QAAA,EACpC7I,iBAAiB,KAAK2F,KAAK;YAAA;YAC1B;YACAlI,OAAA;cAAKoH,SAAS,EAAC,qBAAqB;cAAAgE,QAAA,eAClCpL,OAAA;gBAAKoH,SAAS,EAAC,sBAAsB;gBAAAgE,QAAA,gBACnCpL,OAAA;kBAAKoH,SAAS,EAAC,sBAAsB;kBAAAgE,QAAA,EAClC7H,KAAK,CAAC8E,QAAQ,gBACbrI,OAAA;oBACEgM,GAAG,EAAGA,GAAG,IAAKtJ,WAAW,CAACsJ,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,MAAM;oBACbxD,MAAM,EAAExF,eAAe,CAACC,KAAK,CAAE;oBAC/BgJ,KAAK,EAAE;sBACLF,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdE,eAAe,EAAE,MAAM;sBACvBC,SAAS,EAAE;oBACb,CAAE;oBACFC,OAAO,EAAGxL,CAAC,IAAK0B,aAAa,CAAE,yBAAwBW,KAAK,CAACC,KAAM,EAAC,CAAE;oBACtEmJ,SAAS,EAAEA,CAAA,KAAM/J,aAAa,CAAC,IAAI,CAAE;oBACrCgK,WAAW,EAAC,WAAW;oBAAAxB,QAAA,gBAEvBpL,OAAA;sBAAQ6M,GAAG,EAAEtJ,KAAK,CAAC+E,cAAc,IAAI/E,KAAK,CAAC8E,QAAS;sBAACyB,IAAI,EAAC;oBAAW;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvEjI,KAAK,CAACuJ,SAAS,IAAIvJ,KAAK,CAACuJ,SAAS,CAAClE,MAAM,GAAG,CAAC,IAAIrF,KAAK,CAACuJ,SAAS,CAAClF,GAAG,CAAC,CAACmF,QAAQ,EAAE7E,KAAK,kBACpFlI,OAAA;sBAEEgN,IAAI,EAAC,WAAW;sBAChBH,GAAG,EAAEE,QAAQ,CAACE,GAAI;sBAClBC,OAAO,EAAEH,QAAQ,CAACI,QAAS;sBAC3BC,KAAK,EAAEL,QAAQ,CAACM,YAAa;sBAC7BC,OAAO,EAAEP,QAAQ,CAACQ,SAAS,IAAIrF,KAAK,KAAK;oBAAE,GALrC,GAAE6E,QAAQ,CAACI,QAAS,IAAGjF,KAAM,EAAC;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,GACNjI,KAAK,CAACQ,OAAO,gBACf/D,OAAA;oBACE6M,GAAG,EAAG,iCAAgCtJ,KAAK,CAACQ,OAAQ,mBAAmB;oBACvEP,KAAK,EAAED,KAAK,CAACC,KAAM;oBACnBgK,WAAW,EAAC,GAAG;oBACfC,eAAe;oBACflB,KAAK,EAAE;sBAAEF,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,MAAM;sBAAEoB,MAAM,EAAE;oBAAO;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,gBAEVxL,OAAA;oBAAKoH,SAAS,EAAC,aAAa;oBAAAgE,QAAA,gBAC1BpL,OAAA;sBAAKoH,SAAS,EAAC,YAAY;sBAAAgE,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpCxL,OAAA;sBAAAoL,QAAA,EAAI;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1BxL,OAAA;sBAAAoL,QAAA,EAAIzI,UAAU,IAAI;oBAA4C;sBAAA0I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENxL,OAAA;kBAAKoH,SAAS,EAAC,oBAAoB;kBAAAgE,QAAA,gBACjCpL,OAAA;oBAAIoH,SAAS,EAAC,qBAAqB;oBAAAgE,QAAA,EAAE7H,KAAK,CAACC;kBAAK;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtDxL,OAAA;oBAAKoH,SAAS,EAAC,oBAAoB;oBAAAgE,QAAA,gBACjCpL,OAAA;sBAAAoL,QAAA,EAAOhH,cAAc,CAACb,KAAK,CAACc,OAAO;oBAAC;sBAAAgH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5CxL,OAAA;sBAAAoL,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACdxL,OAAA;sBAAAoL,QAAA,GAAM,QAAM,EAAC7H,KAAK,CAAC6D,SAAS,IAAI7D,KAAK,CAAC9B,KAAK;oBAAA;sBAAA4J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAClDjI,KAAK,CAAC/B,KAAK,iBACVxB,OAAA,CAAAE,SAAA;sBAAAkL,QAAA,gBACEpL,OAAA;wBAAAoL,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACdxL,OAAA;wBAAAoL,QAAA,EAAO7H,KAAK,CAAC/B;sBAAK;wBAAA6J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAC1B,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNxL,OAAA;oBAAKoH,SAAS,EAAC,uBAAuB;oBAAAgE,QAAA,gBACpCpL,OAAA;sBACEoH,SAAS,EAAG,sBAAqBnE,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;sBACpE4I,OAAO,EAAEA,CAAA,KAAM3I,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;sBAAAmI,QAAA,gBAEtDpL,OAAA;wBAAAoL,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACfxL,OAAA;wBAAAoL,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACTxL,OAAA;sBAAQoH,SAAS,EAAC,oBAAoB;sBAAAgE,QAAA,gBACpCpL,OAAA;wBAAAoL,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACfxL,OAAA;wBAAAoL,QAAA,EAAM;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACTxL,OAAA;sBACEoH,SAAS,EAAC,oBAAoB;sBAC9ByE,OAAO,EAAEA,CAAA,KAAMrJ,oBAAoB,CAAC,IAAI,CAAE;sBAAA4I,QAAA,gBAE1CpL,OAAA;wBAAAoL,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACdxL,OAAA;wBAAAoL,QAAA,EAAM;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLvI,gBAAgB,iBACfjD,OAAA;kBAAKoH,SAAS,EAAC,0BAA0B;kBAAAgE,QAAA,gBACvCpL,OAAA;oBAAKoH,SAAS,EAAC,yBAAyB;oBAAAgE,QAAA,eACtCpL,OAAA;sBAAAoL,QAAA,GAAO7G,uBAAuB,CAAC,CAAC,CAACqE,MAAM,EAAC,WAAS;oBAAA;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eAGNxL,OAAA;oBAAKoH,SAAS,EAAC,uBAAuB;oBAAAgE,QAAA,gBACpCpL,OAAA;sBAAKoH,SAAS,EAAC,wBAAwB;sBAAAgE,QAAA,EACpC/K,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsN,YAAY,gBACjB3N,OAAA;wBACE6M,GAAG,EAAExM,IAAI,CAACsN,YAAa;wBACvBC,GAAG,EAAC,SAAS;wBACbrB,KAAK,EAAE;0BAAEF,KAAK,EAAE,MAAM;0BAAEC,MAAM,EAAE,MAAM;0BAAEuB,YAAY,EAAE,KAAK;0BAAEpB,SAAS,EAAE;wBAAQ;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnF,CAAC,IAAAM,IAAA,GAED,CAAAzL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS,MAAIhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ,KAAI,SAAS,cAAAuK,IAAA,wBAAAC,WAAA,GAA7DD,IAAA,CAAgE9E,MAAM,CAAC,CAAC,CAAC,cAAA+E,WAAA,uBAAzEA,WAAA,CAA2E9E,WAAW,CAAC;oBACxF;sBAAAoE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACNxL,OAAA;sBAAKuM,KAAK,EAAE;wBAAEuB,IAAI,EAAE;sBAAE,CAAE;sBAAA1C,QAAA,gBACtBpL,OAAA;wBACEoH,SAAS,EAAC,6BAA6B;wBACvCsE,KAAK,EAAE3I,UAAW;wBAClB4I,QAAQ,EAAGzK,CAAC,IAAK8B,aAAa,CAAC9B,CAAC,CAAC0K,MAAM,CAACF,KAAK,CAAE;wBAC/CD,WAAW,EAAC,kBAAkB;wBAC9BsC,IAAI,EAAC,GAAG;wBACRxB,KAAK,EAAE;0BACLyB,SAAS,EAAE,MAAM;0BACjBC,MAAM,EAAE,MAAM;0BACdC,QAAQ,EAAE;wBACZ,CAAE;wBACFC,OAAO,EAAGjN,CAAC,IAAK;0BACdA,CAAC,CAAC0K,MAAM,CAACW,KAAK,CAACD,MAAM,GAAG,MAAM;0BAC9BpL,CAAC,CAAC0K,MAAM,CAACW,KAAK,CAACD,MAAM,GAAGpL,CAAC,CAAC0K,MAAM,CAACwC,YAAY,GAAG,IAAI;wBACtD;sBAAE;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACDzI,UAAU,CAACe,IAAI,CAAC,CAAC,iBAChB9D,OAAA;wBAAKoH,SAAS,EAAC,yBAAyB;wBAAAgE,QAAA,gBACtCpL,OAAA;0BACEoH,SAAS,EAAC,4BAA4B;0BACtCyE,OAAO,EAAEA,CAAA,KAAM7I,aAAa,CAAC,EAAE,CAAE;0BAAAoI,QAAA,EAClC;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTxL,OAAA;0BACEoH,SAAS,EAAC,4BAA4B;0BACtCyE,OAAO,EAAE1G,gBAAiB;0BAC1BkJ,QAAQ,EAAE,CAACtL,UAAU,CAACe,IAAI,CAAC,CAAE;0BAAAsH,QAAA,EAC9B;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNxL,OAAA;oBAAKoH,SAAS,EAAC,uBAAuB;oBAAAgE,QAAA,EACnC7G,uBAAuB,CAAC,CAAC,CAACqE,MAAM,KAAK,CAAC,gBACrC5I,OAAA;sBAAKuM,KAAK,EAAE;wBAAE+B,SAAS,EAAE,QAAQ;wBAAEC,OAAO,EAAE,QAAQ;wBAAEC,KAAK,EAAE;sBAAU,CAAE;sBAAApD,QAAA,gBACvEpL,OAAA;wBAAKuM,KAAK,EAAE;0BAAEkC,QAAQ,EAAE,MAAM;0BAAEC,YAAY,EAAE;wBAAO,CAAE;wBAAAtD,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChExL,OAAA;wBAAAoL,QAAA,EAAG;sBAAqD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,GAENjH,uBAAuB,CAAC,CAAC,CAACqD,GAAG,CAAErC,OAAO;sBAAA,IAAAoJ,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,iBAAA,EAAAC,iBAAA;sBAAA,oBACpC/O,OAAA;wBAAqCoH,SAAS,EAAC,iBAAiB;wBAAAgE,QAAA,gBAC9DpL,OAAA;0BAAKoH,SAAS,EAAC,wBAAwB;0BAAAgE,QAAA,GACpC7F,OAAO,CAACc,MAAM,IAAId,OAAO,CAACc,MAAM,CAACrC,QAAQ,CAAC,MAAM,CAAC,gBAChDhE,OAAA;4BACE6M,GAAG,EAAEtH,OAAO,CAACc,MAAO;4BACpBuH,GAAG,EAAC,SAAS;4BACbrB,KAAK,EAAE;8BAAEF,KAAK,EAAE,MAAM;8BAAEC,MAAM,EAAE,MAAM;8BAAEuB,YAAY,EAAE,KAAK;8BAAEpB,SAAS,EAAE;4BAAQ,CAAE;4BAClFC,OAAO,EAAGxL,CAAC,IAAK;8BACdA,CAAC,CAAC0K,MAAM,CAACW,KAAK,CAACyC,OAAO,GAAG,MAAM;8BAC/B9N,CAAC,CAAC0K,MAAM,CAACqD,WAAW,CAAC1C,KAAK,CAACyC,OAAO,GAAG,MAAM;4BAC7C;0BAAE;4BAAA3D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,GACA,IAAI,eACRxL,OAAA;4BAAKuM,KAAK,EAAE;8BACVyC,OAAO,EAAEzJ,OAAO,CAACc,MAAM,IAAId,OAAO,CAACc,MAAM,CAACrC,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,MAAM;8BAC5EqI,KAAK,EAAE,MAAM;8BACbC,MAAM,EAAE,MAAM;8BACd4C,UAAU,EAAE,QAAQ;8BACpBC,cAAc,EAAE,QAAQ;8BACxBV,QAAQ,EAAE,MAAM;8BAChBW,UAAU,EAAE;4BACd,CAAE;4BAAAhE,QAAA,EACC7F,OAAO,CAACc,MAAM,IAAI,CAACd,OAAO,CAACc,MAAM,CAACrC,QAAQ,CAAC,MAAM,CAAC,GAAGuB,OAAO,CAACc,MAAM,GAAG,EAAAsI,eAAA,GAAApJ,OAAO,CAACG,MAAM,cAAAiJ,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgB3H,MAAM,CAAC,CAAC,CAAC,cAAA4H,qBAAA,uBAAzBA,qBAAA,CAA2B3H,WAAW,CAAC,CAAC,KAAI;0BAAG;4BAAAoE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNxL,OAAA;0BAAKoH,SAAS,EAAC,yBAAyB;0BAAAgE,QAAA,gBACtCpL,OAAA;4BAAKoH,SAAS,EAAC,wBAAwB;4BAAAgE,QAAA,gBACrCpL,OAAA;8BAAMoH,SAAS,EAAC,wBAAwB;8BAAAgE,QAAA,EACrC7F,OAAO,CAACG,MAAM,IAAI;4BAAW;8BAAA2F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,EACN,CAACjG,OAAO,CAACK,QAAQ,KAAK,OAAO,IAAIL,OAAO,CAACO,OAAO,MAAA+I,aAAA,GAAItJ,OAAO,CAAClF,IAAI,cAAAwO,aAAA,uBAAZA,aAAA,CAAc/I,OAAO,mBACxE9F,OAAA,CAACJ,UAAU;8BAAC2M,KAAK,EAAE;gCAAEiC,KAAK,EAAE,SAAS;gCAAEC,QAAQ,EAAE,MAAM;gCAAEY,UAAU,EAAE;8BAAM,CAAE;8BAAC7L,KAAK,EAAC;4BAAgB;8BAAA6H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CACvG,eACDxL,OAAA;8BAAMoH,SAAS,EAAC,sBAAsB;8BAAAgE,QAAA,EACnCzG,aAAa,CAACY,OAAO,CAACS,SAAS,IAAIT,OAAO,CAACX,SAAS;4BAAC;8BAAAyG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACNxL,OAAA;4BAAKoH,SAAS,EAAC,sBAAsB;4BAAAgE,QAAA,EAClC7F,OAAO,CAACE;0BAAI;4BAAA4F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACNxL,OAAA;4BAAKoH,SAAS,EAAC,yBAAyB;4BAAAgE,QAAA,gBACtCpL,OAAA;8BACE6L,OAAO,EAAEA,CAAA,KAAMnE,iBAAiB,CAACnC,OAAO,CAACd,GAAG,IAAIc,OAAO,CAACb,EAAE,CAAE;8BAC5D0C,SAAS,EAAG,0BAAyB,CAAA0H,iBAAA,GAAAvJ,OAAO,CAACW,OAAO,cAAA4I,iBAAA,eAAfA,iBAAA,CAAiB9K,QAAQ,CAAC3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;8BAAA2G,QAAA,gBAE3FpL,OAAA;gCAAAoL,QAAA,EAAO,CAAA2D,iBAAA,GAAAxJ,OAAO,CAACW,OAAO,cAAA6I,iBAAA,eAAfA,iBAAA,CAAiB/K,QAAQ,CAAC3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,GAAG,CAAC,GAAG,IAAI,GAAG;8BAAI;gCAAA4G,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EAChEjG,OAAO,CAACU,KAAK,GAAG,CAAC,iBAAIjG,OAAA;gCAAAoL,QAAA,EAAO7F,OAAO,CAACU;8BAAK;gCAAAoF,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5C,CAAC,eACTxL,OAAA;8BAAQoH,SAAS,EAAC,wBAAwB;8BAAAgE,QAAA,EAAC;4BAE3C;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,EACRjG,OAAO,CAAClF,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,GAAG,kBACzBzE,OAAA,CAAAE,SAAA;8BAAAkL,QAAA,gBACEpL,OAAA;gCAAQoH,SAAS,EAAC,wBAAwB;gCAAAgE,QAAA,EAAC;8BAE3C;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACTxL,OAAA;gCACEoH,SAAS,EAAC,wBAAwB;gCAClCyE,OAAO,EAAEA,CAAA,KAAM;kCACb,IAAIyD,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;oCACnEvH,mBAAmB,CAACzC,OAAO,CAACd,GAAG,IAAIc,OAAO,CAACb,EAAE,CAAC;kCAChD;gCACF,CAAE;gCAAA0G,QAAA,EACH;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA,eACT,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA,GArEEjG,OAAO,CAACd,GAAG,IAAIc,OAAO,CAACb,EAAE;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAsE9B,CAAC;oBAAA,CACP;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACAxL,OAAA;cAAKoH,SAAS,EAAC,YAAY;cAACyE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAACC,KAAK,CAAE;cAAAkD,QAAA,gBAChEpL,OAAA;gBAAKoH,SAAS,EAAC,sBAAsB;gBAAAgE,QAAA,gBACnCpL,OAAA;kBACE6M,GAAG,EAAEvJ,eAAe,CAACC,KAAK,CAAE;kBAC5BqK,GAAG,EAAErK,KAAK,CAACC,KAAM;kBACjB4D,SAAS,EAAC,iBAAiB;kBAC3BxF,OAAO,EAAC,MAAM;kBACd8K,OAAO,EAAGxL,CAAC,IAAK;oBACd;oBACAA,CAAC,CAAC0K,MAAM,CAACiB,GAAG,GAAG,4cAA4c;kBAC7d;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFxL,OAAA;kBAAKoH,SAAS,EAAC,cAAc;kBAAAgE,QAAA,eAC3BpL,OAAA,CAACR,YAAY;oBAAC4H,SAAS,EAAC;kBAAW;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNxL,OAAA;kBAAKoH,SAAS,EAAC,gBAAgB;kBAAAgE,QAAA,EAC5B7H,KAAK,CAACiM,QAAQ,IAAI;gBAAO;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EACLjI,KAAK,CAACuJ,SAAS,IAAIvJ,KAAK,CAACuJ,SAAS,CAAClE,MAAM,GAAG,CAAC,iBAC5C5I,OAAA;kBAAKoH,SAAS,EAAC,gBAAgB;kBAAAgE,QAAA,gBAC7BpL,OAAA,CAACN,YAAY;oBAAA2L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,MAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENxL,OAAA;gBAAKoH,SAAS,EAAC,oBAAoB;gBAAAgE,QAAA,gBACjCpL,OAAA;kBAAIoH,SAAS,EAAC,aAAa;kBAAAgE,QAAA,EAAE7H,KAAK,CAACC;gBAAK;kBAAA6H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CxL,OAAA;kBAAKoH,SAAS,EAAC,YAAY;kBAAAgE,QAAA,gBACzBpL,OAAA;oBAAMoH,SAAS,EAAC,eAAe;oBAAAgE,QAAA,EAAEhH,cAAc,CAACb,KAAK,CAACc,OAAO;kBAAC;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtExL,OAAA;oBAAMoH,SAAS,EAAC,aAAa;oBAAAgE,QAAA,EAC1BnJ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAAC6D,SAAS,IAAI7D,KAAK,CAAC9B,KAAM,EAAC,GAAI,SAAQ8B,KAAK,CAAC6D,SAAS,IAAI7D,KAAK,CAAC9B,KAAM,EAAC,GACvG,QAAO8B,KAAK,CAAC6D,SAAS,IAAI7D,KAAK,CAAC9B,KAAM;kBAAC;oBAAA4J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxL,OAAA;kBAAKoH,SAAS,EAAC,YAAY;kBAAAgE,QAAA,GACxB7H,KAAK,CAACyH,KAAK,iBAAIhL,OAAA;oBAAMoH,SAAS,EAAC,WAAW;oBAAAgE,QAAA,EAAE7H,KAAK,CAACyH;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/DjI,KAAK,CAACkM,eAAe,IAAIlM,KAAK,CAACkM,eAAe,MAAMlM,KAAK,CAAC6D,SAAS,IAAI7D,KAAK,CAAC9B,KAAK,CAAC,iBAClFzB,OAAA;oBAAMoH,SAAS,EAAC,YAAY;oBAAAgE,QAAA,GACzB/H,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDpB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAACkM,eAAgB,EAAC,GAAI,SAAQlM,KAAK,CAACkM,eAAgB,EAAC,GACrF,QAAOlM,KAAK,CAACkM,eAAgB,EAAC;kBAAA;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GA9ROtD,KAAK;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+RV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENxL,OAAA;QAAKoH,SAAS,EAAC,aAAa;QAAAgE,QAAA,gBAC1BpL,OAAA,CAACP,eAAe;UAAC2H,SAAS,EAAC;QAAY;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CxL,OAAA;UAAAoL,QAAA,EAAK/H,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1ExL,OAAA;UAAAoL,QAAA,EAAI/H,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJxL,OAAA;UAAGoH,SAAS,EAAC,YAAY;UAAAgE,QAAA,EAAE/H,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpL,EAAA,CA/3BID,YAAY;EAAA,QAEHZ,WAAW;AAAA;AAAAmQ,EAAA,GAFpBvP,YAAY;AAi4BlB,eAAeA,YAAY;AAAC,IAAAuP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}