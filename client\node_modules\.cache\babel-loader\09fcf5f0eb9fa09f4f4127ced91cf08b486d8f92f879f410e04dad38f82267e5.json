{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\VideoGrid.js\";\nimport React from 'react';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoGrid = ({\n  paginatedVideos,\n  currentVideoIndex,\n  handleShowVideo,\n  getThumbnailUrl,\n  getSubjectName,\n  selectedLevel,\n  isKiswahili,\n  setVideoRef,\n  setVideoError,\n  videoError,\n  setCurrentVideoIndex,\n  commentsExpanded,\n  setCommentsExpanded,\n  getCurrentVideoComments,\n  newComment,\n  setNewComment,\n  handleAddComment,\n  handleLikeComment,\n  handleDeleteComment,\n  formatTimeAgo,\n  user\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"videos-grid\",\n    children: paginatedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: paginatedVideos.map((video, index) => {\n        var _user$name, _user$name$charAt;\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-thumbnail\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getThumbnailUrl(video),\n                  alt: video.title,\n                  className: \"thumbnail-image\",\n                  loading: \"lazy\",\n                  onError: e => {\n                    // Fallback logic for failed thumbnails\n                    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                      // For YouTube videos, try different quality thumbnails\n                      let videoId = video.videoID;\n                      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                        videoId = match ? match[1] : videoId;\n                      }\n                      const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                      const currentSrc = e.target.src;\n                      const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                      if (currentIndex < fallbacks.length - 1) {\n                        e.target.src = fallbacks[currentIndex + 1];\n                      }\n                    } else {\n                      e.target.src = '/api/placeholder/320/180';\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"play-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                    className: \"play-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 71,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-duration\",\n                  children: video.duration || \"Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 21\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 78,\n                    columnNumber: 25\n                  }, this), \"CC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 86,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-tags\",\n                  children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"topic-tag\",\n                    children: video.topic\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 94,\n                    columnNumber: 39\n                  }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"shared-tag\",\n                    children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 17\n            }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-video-player\",\n              children: [video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                ref: ref => setVideoRef(ref),\n                controls: true,\n                autoPlay: true,\n                playsInline: true,\n                preload: \"metadata\",\n                width: \"100%\",\n                height: \"100%\",\n                poster: getThumbnailUrl(video),\n                style: {\n                  width: '100%',\n                  height: '100%',\n                  backgroundColor: '#000',\n                  objectFit: 'contain'\n                },\n                onError: e => {\n                  setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                },\n                onCanPlay: () => {\n                  setVideoError(null);\n                },\n                crossOrigin: \"anonymous\",\n                children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                  src: video.signedVideoUrl || video.videoUrl,\n                  type: \"video/mp4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 25\n                }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, subIndex) => /*#__PURE__*/_jsxDEV(\"track\", {\n                  kind: \"subtitles\",\n                  src: subtitle.url,\n                  srcLang: subtitle.language,\n                  label: subtitle.languageName,\n                  default: subtitle.isDefault || subIndex === 0\n                }, `${subtitle.language}-${subIndex}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 27\n                }, this)), \"Your browser does not support the video tag.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 23\n              }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                title: video.title,\n                frameBorder: \"0\",\n                allowFullScreen: true,\n                style: {\n                  width: '100%',\n                  height: '100%',\n                  border: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-error\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-icon\",\n                  children: \"\\u26A0\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Video Unavailable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: videoError || \"This video cannot be played at the moment.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-actions-horizontal\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `youtube-action-btn-small ${commentsExpanded ? 'active' : ''}`,\n                  onClick: () => setCommentsExpanded(!commentsExpanded),\n                  children: [\"\\uD83D\\uDCAC Comments (\", getCurrentVideoComments().length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"youtube-action-btn-small\",\n                  children: \"\\uD83D\\uDC4D Like\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"youtube-action-btn-small close-btn\",\n                  onClick: () => setCurrentVideoIndex(null),\n                  children: \"\\u2715 Close\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 21\n              }, this), commentsExpanded && currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-comments-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [getCurrentVideoComments().length, \" Comments\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comment-input\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comment-avatar\",\n                    children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || \"A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                      className: \"youtube-comment-input-field\",\n                      value: newComment,\n                      onChange: e => setNewComment(e.target.value),\n                      placeholder: isKiswahili ? \"Andika maoni yako...\" : \"Add a comment...\",\n                      rows: \"1\",\n                      style: {\n                        minHeight: '20px',\n                        resize: 'none',\n                        overflow: 'hidden'\n                      },\n                      onInput: e => {\n                        e.target.style.height = 'auto';\n                        e.target.style.height = e.target.scrollHeight + 'px';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 29\n                    }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment-actions\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"youtube-comment-btn cancel\",\n                        onClick: () => setNewComment(''),\n                        children: isKiswahili ? 'Ghairi' : 'Cancel'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"youtube-comment-btn submit\",\n                        onClick: handleAddComment,\n                        disabled: !newComment.trim(),\n                        children: isKiswahili ? 'Tuma' : 'Comment'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-list\",\n                  children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      textAlign: 'center',\n                      padding: '40px 0',\n                      color: '#606060'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '48px',\n                        marginBottom: '16px'\n                      },\n                      children: \"\\uD83D\\uDCAC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: isKiswahili ? 'Hakuna maoni bado. Kuwa wa kwanza kushiriki mawazo yako!' : 'No comments yet. Be the first to share your thoughts!'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 29\n                  }, this) : getCurrentVideoComments().map(comment => {\n                    var _comment$author, _comment$author$charA, _comment$likedBy, _comment$likedBy2;\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-avatar\",\n                        children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 242,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-header\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"youtube-comment-author\",\n                            children: comment.author\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 247,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"youtube-comment-time\",\n                            children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 248,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 246,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-text\",\n                          children: comment.text\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 252,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-actions\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => handleLikeComment(comment._id || comment.id),\n                            className: `youtube-comment-action ${(_comment$likedBy = comment.likedBy) !== null && _comment$likedBy !== void 0 && _comment$likedBy.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: (_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 260,\n                              columnNumber: 39\n                            }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: comment.likes\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 261,\n                              columnNumber: 61\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 256,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"youtube-comment-action\",\n                            children: isKiswahili ? 'Jibu' : 'Reply'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 263,\n                            columnNumber: 37\n                          }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"youtube-comment-action\",\n                            onClick: () => {\n                              if (window.confirm(isKiswahili ? 'Una uhakika unataka kufuta maoni haya?' : 'Are you sure you want to delete this comment?')) {\n                                handleDeleteComment(comment._id || comment.id);\n                              }\n                            },\n                            children: isKiswahili ? 'Futa' : 'Delete'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 267,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 255,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 245,\n                        columnNumber: 33\n                      }, this)]\n                    }, comment._id || comment.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 31\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n        className: \"empty-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"suggestion\",\n        children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_c = VideoGrid;\nexport default VideoGrid;\nvar _c;\n$RefreshReg$(_c, \"VideoGrid\");", "map": {"version": 3, "names": ["React", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoGrid", "paginatedVideos", "currentVideoIndex", "handleShowVideo", "getThumbnailUrl", "getSubjectName", "selectedLevel", "isKiswahili", "setVideoRef", "setVideoError", "videoError", "setCurrentVideoIndex", "commentsExpanded", "setCommentsExpanded", "getCurrentVideoComments", "newComment", "setNewComment", "handleAddComment", "handleLikeComment", "handleDeleteComment", "formatTimeAgo", "user", "className", "children", "length", "map", "video", "index", "_user$name", "_user$name$charAt", "onClick", "src", "alt", "title", "loading", "onError", "e", "videoID", "includes", "videoId", "match", "fallbacks", "currentSrc", "target", "currentIndex", "findIndex", "url", "split", "pop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "duration", "subtitles", "subject", "class", "topic", "sharedFromClass", "videoUrl", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "signedVideoUrl", "type", "subtitle", "subIndex", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "name", "char<PERSON>t", "toUpperCase", "flex", "value", "onChange", "placeholder", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "trim", "disabled", "textAlign", "padding", "color", "fontSize", "marginBottom", "comment", "_comment$author", "_comment$author$charA", "_comment$likedBy", "_comment$likedBy2", "avatar", "author", "createdAt", "timestamp", "text", "_id", "id", "<PERSON><PERSON><PERSON>", "likes", "window", "confirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/VideoGrid.js"], "sourcesContent": ["import React from 'react';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle } from 'react-icons/tb';\n\nconst VideoGrid = ({\n  paginatedVideos,\n  currentVideoIndex,\n  handleShowVideo,\n  getThumbnailUrl,\n  getSubjectName,\n  selectedLevel,\n  isKiswahili,\n  setVideoRef,\n  setVideoError,\n  videoError,\n  setCurrentVideoIndex,\n  commentsExpanded,\n  setCommentsExpanded,\n  getCurrentVideoComments,\n  newComment,\n  setNewComment,\n  handleAddComment,\n  handleLikeComment,\n  handleDeleteComment,\n  formatTimeAgo,\n  user\n}) => {\n  return (\n    <div className=\"videos-grid\">\n      {paginatedVideos.length > 0 ? (\n        <>\n          {paginatedVideos.map((video, index) => (\n            <React.Fragment key={index}>\n              <div className=\"video-item\">\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = video.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            '/api/placeholder/320/180'\n                          ];\n\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          }\n                        } else {\n                          e.target.src = '/api/placeholder/320/180';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration\">\n                      {video.duration || \"Video\"}\n                    </div>\n                    {video.subtitles && video.subtitles.length > 0 && (\n                      <div className=\"subtitle-badge\">\n                        <TbInfoCircle />\n                        CC\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"video-card-content\">\n                    <h3 className=\"video-title\">{video.title}</h3>\n                    <div className=\"video-meta\">\n                      <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                      <span className=\"video-class\">\n                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                          (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`) :\n                          `Form ${video.className || video.class}`}\n                      </span>\n                    </div>\n                    <div className=\"video-tags\">\n                      {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                        <span className=\"shared-tag\">\n                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}{selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                            (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`) :\n                            `Form ${video.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    {video.videoUrl ? (\n                      <video\n                        ref={(ref) => setVideoRef(ref)}\n                        controls\n                        autoPlay\n                        playsInline\n                        preload=\"metadata\"\n                        width=\"100%\"\n                        height=\"100%\"\n                        poster={getThumbnailUrl(video)}\n                        style={{\n                          width: '100%',\n                          height: '100%',\n                          backgroundColor: '#000',\n                          objectFit: 'contain'\n                        }}\n                        onError={(e) => {\n                          setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                        }}\n                        onCanPlay={() => {\n                          setVideoError(null);\n                        }}\n                        crossOrigin=\"anonymous\"\n                      >\n                        <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                        {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, subIndex) => (\n                          <track\n                            key={`${subtitle.language}-${subIndex}`}\n                            kind=\"subtitles\"\n                            src={subtitle.url}\n                            srcLang={subtitle.language}\n                            label={subtitle.languageName}\n                            default={subtitle.isDefault || subIndex === 0}\n                          />\n                        ))}\n                        Your browser does not support the video tag.\n                      </video>\n                    ) : video.videoID ? (\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        style={{ \n                          width: '100%', \n                          height: '100%', \n                          border: 'none'\n                        }}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                      </div>\n                    )}\n                    \n                    {/* Horizontal Action Buttons */}\n                    <div className=\"youtube-video-actions-horizontal\">\n                      <button\n                        className={`youtube-action-btn-small ${commentsExpanded ? 'active' : ''}`}\n                        onClick={() => setCommentsExpanded(!commentsExpanded)}\n                      >\n                        💬 Comments ({getCurrentVideoComments().length})\n                      </button>\n                      <button className=\"youtube-action-btn-small\">\n                        👍 Like\n                      </button>\n                      <button\n                        className=\"youtube-action-btn-small close-btn\"\n                        onClick={() => setCurrentVideoIndex(null)}\n                      >\n                        ✕ Close\n                      </button>\n                    </div>\n\n                    {/* Comments Section */}\n                    {commentsExpanded && currentVideoIndex === index && (\n                      <div className=\"youtube-comments-section\">\n                        <div className=\"youtube-comments-header\">\n                          <span>{getCurrentVideoComments().length} Comments</span>\n                        </div>\n\n                        {/* Add Comment */}\n                        <div className=\"youtube-comment-input\">\n                          <div className=\"youtube-comment-avatar\">\n                            {user?.name?.charAt(0)?.toUpperCase() || \"A\"}\n                          </div>\n                          <div style={{ flex: 1 }}>\n                            <textarea\n                              className=\"youtube-comment-input-field\"\n                              value={newComment}\n                              onChange={(e) => setNewComment(e.target.value)}\n                              placeholder={isKiswahili ? \"Andika maoni yako...\" : \"Add a comment...\"}\n                              rows=\"1\"\n                              style={{\n                                minHeight: '20px',\n                                resize: 'none',\n                                overflow: 'hidden'\n                              }}\n                              onInput={(e) => {\n                                e.target.style.height = 'auto';\n                                e.target.style.height = e.target.scrollHeight + 'px';\n                              }}\n                            />\n                            {newComment.trim() && (\n                              <div className=\"youtube-comment-actions\">\n                                <button\n                                  className=\"youtube-comment-btn cancel\"\n                                  onClick={() => setNewComment('')}\n                                >\n                                  {isKiswahili ? 'Ghairi' : 'Cancel'}\n                                </button>\n                                <button\n                                  className=\"youtube-comment-btn submit\"\n                                  onClick={handleAddComment}\n                                  disabled={!newComment.trim()}\n                                >\n                                  {isKiswahili ? 'Tuma' : 'Comment'}\n                                </button>\n                              </div>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Comments List */}\n                        <div className=\"youtube-comments-list\">\n                          {getCurrentVideoComments().length === 0 ? (\n                            <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                              <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                              <p>{isKiswahili ? 'Hakuna maoni bado. Kuwa wa kwanza kushiriki mawazo yako!' : 'No comments yet. Be the first to share your thoughts!'}</p>\n                            </div>\n                          ) : (\n                            getCurrentVideoComments().map((comment) => (\n                              <div key={comment._id || comment.id} className=\"youtube-comment\">\n                                <div className=\"youtube-comment-avatar\">\n                                  {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                </div>\n                                <div className=\"youtube-comment-content\">\n                                  <div className=\"youtube-comment-header\">\n                                    <span className=\"youtube-comment-author\">{comment.author}</span>\n                                    <span className=\"youtube-comment-time\">\n                                      {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                    </span>\n                                  </div>\n                                  <div className=\"youtube-comment-text\">\n                                    {comment.text}\n                                  </div>\n                                  <div className=\"youtube-comment-actions\">\n                                    <button\n                                      onClick={() => handleLikeComment(comment._id || comment.id)}\n                                      className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                    >\n                                      <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                      {comment.likes > 0 && <span>{comment.likes}</span>}\n                                    </button>\n                                    <button className=\"youtube-comment-action\">\n                                      {isKiswahili ? 'Jibu' : 'Reply'}\n                                    </button>\n                                    {comment.user === user?._id && (\n                                      <button\n                                        className=\"youtube-comment-action\"\n                                        onClick={() => {\n                                          if (window.confirm(isKiswahili ? 'Una uhakika unataka kufuta maoni haya?' : 'Are you sure you want to delete this comment?')) {\n                                            handleDeleteComment(comment._id || comment.id);\n                                          }\n                                        }}\n                                      >\n                                        {isKiswahili ? 'Futa' : 'Delete'}\n                                      </button>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n                            ))\n                          )}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </React.Fragment>\n          ))}\n        </>\n      ) : (\n        <div className=\"empty-state\">\n          <FaGraduationCap className=\"empty-icon\" />\n          <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n          <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n          <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default VideoGrid;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,SAAS,GAAGA,CAAC;EACjBC,eAAe;EACfC,iBAAiB;EACjBC,eAAe;EACfC,eAAe;EACfC,cAAc;EACdC,aAAa;EACbC,WAAW;EACXC,WAAW;EACXC,aAAa;EACbC,UAAU;EACVC,oBAAoB;EACpBC,gBAAgB;EAChBC,mBAAmB;EACnBC,uBAAuB;EACvBC,UAAU;EACVC,aAAa;EACbC,gBAAgB;EAChBC,iBAAiB;EACjBC,mBAAmB;EACnBC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,oBACExB,OAAA;IAAKyB,SAAS,EAAC,aAAa;IAAAC,QAAA,EACzBtB,eAAe,CAACuB,MAAM,GAAG,CAAC,gBACzB3B,OAAA,CAAAE,SAAA;MAAAwB,QAAA,EACGtB,eAAe,CAACwB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK;QAAA,IAAAC,UAAA,EAAAC,iBAAA;QAAA,oBAChChC,OAAA,CAACL,KAAK,CAACM,QAAQ;UAAAyB,QAAA,eACb1B,OAAA;YAAKyB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1B,OAAA;cAAKyB,SAAS,EAAC,YAAY;cAACQ,OAAO,EAAEA,CAAA,KAAM3B,eAAe,CAACwB,KAAK,CAAE;cAAAJ,QAAA,gBAChE1B,OAAA;gBAAKyB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnC1B,OAAA;kBACEkC,GAAG,EAAE3B,eAAe,CAACsB,KAAK,CAAE;kBAC5BM,GAAG,EAAEN,KAAK,CAACO,KAAM;kBACjBX,SAAS,EAAC,iBAAiB;kBAC3BY,OAAO,EAAC,MAAM;kBACdC,OAAO,EAAGC,CAAC,IAAK;oBACd;oBACA,IAAIV,KAAK,CAACW,OAAO,IAAI,CAACX,KAAK,CAACW,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;sBAC7D;sBACA,IAAIC,OAAO,GAAGb,KAAK,CAACW,OAAO;sBAC3B,IAAIE,OAAO,CAACD,QAAQ,CAAC,aAAa,CAAC,IAAIC,OAAO,CAACD,QAAQ,CAAC,UAAU,CAAC,EAAE;wBACnE,MAAME,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;wBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;sBACtC;sBAEA,MAAME,SAAS,GAAG,CACf,8BAA6BF,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;sBAED,MAAMG,UAAU,GAAGN,CAAC,CAACO,MAAM,CAACZ,GAAG;sBAC/B,MAAMa,YAAY,GAAGH,SAAS,CAACI,SAAS,CAACC,GAAG,IAAIJ,UAAU,CAACJ,QAAQ,CAACQ,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;sBAE1F,IAAIJ,YAAY,GAAGH,SAAS,CAACjB,MAAM,GAAG,CAAC,EAAE;wBACvCY,CAAC,CAACO,MAAM,CAACZ,GAAG,GAAGU,SAAS,CAACG,YAAY,GAAG,CAAC,CAAC;sBAC5C;oBACF,CAAC,MAAM;sBACLR,CAAC,CAACO,MAAM,CAACZ,GAAG,GAAG,0BAA0B;oBAC3C;kBACF;gBAAE;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFvD,OAAA;kBAAKyB,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3B1B,OAAA,CAACJ,YAAY;oBAAC6B,SAAS,EAAC;kBAAW;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNvD,OAAA;kBAAKyB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAC5BG,KAAK,CAAC2B,QAAQ,IAAI;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EACL1B,KAAK,CAAC4B,SAAS,IAAI5B,KAAK,CAAC4B,SAAS,CAAC9B,MAAM,GAAG,CAAC,iBAC5C3B,OAAA;kBAAKyB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B1B,OAAA,CAACF,YAAY;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,MAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNvD,OAAA;gBAAKyB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC1B,OAAA;kBAAIyB,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEG,KAAK,CAACO;gBAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CvD,OAAA;kBAAKyB,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB1B,OAAA;oBAAMyB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAElB,cAAc,CAACqB,KAAK,CAAC6B,OAAO;kBAAC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtEvD,OAAA;oBAAMyB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAC1BjB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClEC,WAAW,GAAI,aAAYmB,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC8B,KAAM,EAAC,GAAI,SAAQ9B,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC8B,KAAM,EAAC,GACvG,QAAO9B,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC8B,KAAM;kBAAC;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNvD,OAAA;kBAAKyB,SAAS,EAAC,YAAY;kBAAAC,QAAA,GACxBG,KAAK,CAAC+B,KAAK,iBAAI5D,OAAA;oBAAMyB,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEG,KAAK,CAAC+B;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/D1B,KAAK,CAACgC,eAAe,IAAIhC,KAAK,CAACgC,eAAe,MAAMhC,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC8B,KAAK,CAAC,iBAClF3D,OAAA;oBAAMyB,SAAS,EAAC,YAAY;oBAAAC,QAAA,GACzBhB,WAAW,GAAG,qBAAqB,GAAG,cAAc,EAAED,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GACxHC,WAAW,GAAI,aAAYmB,KAAK,CAACgC,eAAgB,EAAC,GAAI,SAAQhC,KAAK,CAACgC,eAAgB,EAAC,GACrF,QAAOhC,KAAK,CAACgC,eAAgB,EAAC;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLlD,iBAAiB,KAAKyB,KAAK,iBAC1B9B,OAAA;cAAKyB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GACjCG,KAAK,CAACiC,QAAQ,gBACb9D,OAAA;gBACE+D,GAAG,EAAGA,GAAG,IAAKpD,WAAW,CAACoD,GAAG,CAAE;gBAC/BC,QAAQ;gBACRC,QAAQ;gBACRC,WAAW;gBACXC,OAAO,EAAC,UAAU;gBAClBC,KAAK,EAAC,MAAM;gBACZC,MAAM,EAAC,MAAM;gBACbC,MAAM,EAAE/D,eAAe,CAACsB,KAAK,CAAE;gBAC/B0C,KAAK,EAAE;kBACLH,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdG,eAAe,EAAE,MAAM;kBACvBC,SAAS,EAAE;gBACb,CAAE;gBACFnC,OAAO,EAAGC,CAAC,IAAK;kBACd3B,aAAa,CAAE,yBAAwBiB,KAAK,CAACO,KAAM,mCAAkC,CAAC;gBACxF,CAAE;gBACFsC,SAAS,EAAEA,CAAA,KAAM;kBACf9D,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACF+D,WAAW,EAAC,WAAW;gBAAAjD,QAAA,gBAEvB1B,OAAA;kBAAQkC,GAAG,EAAEL,KAAK,CAAC+C,cAAc,IAAI/C,KAAK,CAACiC,QAAS;kBAACe,IAAI,EAAC;gBAAW;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACvE1B,KAAK,CAAC4B,SAAS,IAAI5B,KAAK,CAAC4B,SAAS,CAAC9B,MAAM,GAAG,CAAC,IAAIE,KAAK,CAAC4B,SAAS,CAAC7B,GAAG,CAAC,CAACkD,QAAQ,EAAEC,QAAQ,kBACvF/E,OAAA;kBAEEgF,IAAI,EAAC,WAAW;kBAChB9C,GAAG,EAAE4C,QAAQ,CAAC7B,GAAI;kBAClBgC,OAAO,EAAEH,QAAQ,CAACI,QAAS;kBAC3BC,KAAK,EAAEL,QAAQ,CAACM,YAAa;kBAC7BC,OAAO,EAAEP,QAAQ,CAACQ,SAAS,IAAIP,QAAQ,KAAK;gBAAE,GALxC,GAAED,QAAQ,CAACI,QAAS,IAAGH,QAAS,EAAC;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMxC,CACF,CAAC,EAAC,8CAEL;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,GACN1B,KAAK,CAACW,OAAO,gBACfxC,OAAA;gBACEkC,GAAG,EAAG,iCAAgCL,KAAK,CAACW,OAAQ,mBAAmB;gBACvEJ,KAAK,EAAEP,KAAK,CAACO,KAAM;gBACnBmD,WAAW,EAAC,GAAG;gBACfC,eAAe;gBACfjB,KAAK,EAAE;kBACLH,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdoB,MAAM,EAAE;gBACV;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,gBAEVvD,OAAA;gBAAKyB,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B1B,OAAA;kBAAKyB,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAE;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpCvD,OAAA;kBAAA0B,QAAA,EAAI;gBAAiB;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BvD,OAAA;kBAAA0B,QAAA,EAAIb,UAAU,IAAI;gBAA4C;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACN,eAGDvD,OAAA;gBAAKyB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C1B,OAAA;kBACEyB,SAAS,EAAG,4BAA2BV,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAC1EkB,OAAO,EAAEA,CAAA,KAAMjB,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;kBAAAW,QAAA,GACvD,yBACc,EAACT,uBAAuB,CAAC,CAAC,CAACU,MAAM,EAAC,GACjD;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvD,OAAA;kBAAQyB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAE7C;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvD,OAAA;kBACEyB,SAAS,EAAC,oCAAoC;kBAC9CQ,OAAO,EAAEA,CAAA,KAAMnB,oBAAoB,CAAC,IAAI,CAAE;kBAAAY,QAAA,EAC3C;gBAED;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAGLxC,gBAAgB,IAAIV,iBAAiB,KAAKyB,KAAK,iBAC9C9B,OAAA;gBAAKyB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACvC1B,OAAA;kBAAKyB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,eACtC1B,OAAA;oBAAA0B,QAAA,GAAOT,uBAAuB,CAAC,CAAC,CAACU,MAAM,EAAC,WAAS;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eAGNvD,OAAA;kBAAKyB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpC1B,OAAA;oBAAKyB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EACpC,CAAAF,IAAI,aAAJA,IAAI,wBAAAO,UAAA,GAAJP,IAAI,CAAEkE,IAAI,cAAA3D,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAY4D,MAAM,CAAC,CAAC,CAAC,cAAA3D,iBAAA,uBAArBA,iBAAA,CAAuB4D,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACNvD,OAAA;oBAAKuE,KAAK,EAAE;sBAAEsB,IAAI,EAAE;oBAAE,CAAE;oBAAAnE,QAAA,gBACtB1B,OAAA;sBACEyB,SAAS,EAAC,6BAA6B;sBACvCqE,KAAK,EAAE5E,UAAW;sBAClB6E,QAAQ,EAAGxD,CAAC,IAAKpB,aAAa,CAACoB,CAAC,CAACO,MAAM,CAACgD,KAAK,CAAE;sBAC/CE,WAAW,EAAEtF,WAAW,GAAG,sBAAsB,GAAG,kBAAmB;sBACvEuF,IAAI,EAAC,GAAG;sBACR1B,KAAK,EAAE;wBACL2B,SAAS,EAAE,MAAM;wBACjBC,MAAM,EAAE,MAAM;wBACdC,QAAQ,EAAE;sBACZ,CAAE;sBACFC,OAAO,EAAG9D,CAAC,IAAK;wBACdA,CAAC,CAACO,MAAM,CAACyB,KAAK,CAACF,MAAM,GAAG,MAAM;wBAC9B9B,CAAC,CAACO,MAAM,CAACyB,KAAK,CAACF,MAAM,GAAG9B,CAAC,CAACO,MAAM,CAACwD,YAAY,GAAG,IAAI;sBACtD;oBAAE;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACDrC,UAAU,CAACqF,IAAI,CAAC,CAAC,iBAChBvG,OAAA;sBAAKyB,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACtC1B,OAAA;wBACEyB,SAAS,EAAC,4BAA4B;wBACtCQ,OAAO,EAAEA,CAAA,KAAMd,aAAa,CAAC,EAAE,CAAE;wBAAAO,QAAA,EAEhChB,WAAW,GAAG,QAAQ,GAAG;sBAAQ;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eACTvD,OAAA;wBACEyB,SAAS,EAAC,4BAA4B;wBACtCQ,OAAO,EAAEb,gBAAiB;wBAC1BoF,QAAQ,EAAE,CAACtF,UAAU,CAACqF,IAAI,CAAC,CAAE;wBAAA7E,QAAA,EAE5BhB,WAAW,GAAG,MAAM,GAAG;sBAAS;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNvD,OAAA;kBAAKyB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnCT,uBAAuB,CAAC,CAAC,CAACU,MAAM,KAAK,CAAC,gBACrC3B,OAAA;oBAAKuE,KAAK,EAAE;sBAAEkC,SAAS,EAAE,QAAQ;sBAAEC,OAAO,EAAE,QAAQ;sBAAEC,KAAK,EAAE;oBAAU,CAAE;oBAAAjF,QAAA,gBACvE1B,OAAA;sBAAKuE,KAAK,EAAE;wBAAEqC,QAAQ,EAAE,MAAM;wBAAEC,YAAY,EAAE;sBAAO,CAAE;sBAAAnF,QAAA,EAAC;oBAAE;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChEvD,OAAA;sBAAA0B,QAAA,EAAIhB,WAAW,GAAG,0DAA0D,GAAG;oBAAuD;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxI,CAAC,GAENtC,uBAAuB,CAAC,CAAC,CAACW,GAAG,CAAEkF,OAAO;oBAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA;oBAAA,oBACpClH,OAAA;sBAAqCyB,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9D1B,OAAA;wBAAKyB,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,EACpCoF,OAAO,CAACK,MAAM,MAAAJ,eAAA,GAAID,OAAO,CAACM,MAAM,cAAAL,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBpB,MAAM,CAAC,CAAC,CAAC,cAAAqB,qBAAA,uBAAzBA,qBAAA,CAA2BpB,WAAW,CAAC,CAAC,KAAI;sBAAG;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CAAC,eACNvD,OAAA;wBAAKyB,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC1B,OAAA;0BAAKyB,SAAS,EAAC,wBAAwB;0BAAAC,QAAA,gBACrC1B,OAAA;4BAAMyB,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,EAAEoF,OAAO,CAACM;0BAAM;4BAAAhE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAChEvD,OAAA;4BAAMyB,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EACnCH,aAAa,CAACuF,OAAO,CAACO,SAAS,IAAIP,OAAO,CAACQ,SAAS;0BAAC;4BAAAlE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNvD,OAAA;0BAAKyB,SAAS,EAAC,sBAAsB;0BAAAC,QAAA,EAClCoF,OAAO,CAACS;wBAAI;0BAAAnE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC,eACNvD,OAAA;0BAAKyB,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,gBACtC1B,OAAA;4BACEiC,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAACyF,OAAO,CAACU,GAAG,IAAIV,OAAO,CAACW,EAAE,CAAE;4BAC5DhG,SAAS,EAAG,0BAAyB,CAAAwF,gBAAA,GAAAH,OAAO,CAACY,OAAO,cAAAT,gBAAA,eAAfA,gBAAA,CAAiBxE,QAAQ,CAACjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;4BAAA9F,QAAA,gBAE3F1B,OAAA;8BAAA0B,QAAA,EAAO,CAAAwF,iBAAA,GAAAJ,OAAO,CAACY,OAAO,cAAAR,iBAAA,eAAfA,iBAAA,CAAiBzE,QAAQ,CAACjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,GAAG,CAAC,GAAG,IAAI,GAAG;4BAAI;8BAAApE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,EAChEuD,OAAO,CAACa,KAAK,GAAG,CAAC,iBAAI3H,OAAA;8BAAA0B,QAAA,EAAOoF,OAAO,CAACa;4BAAK;8BAAAvE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5C,CAAC,eACTvD,OAAA;4BAAQyB,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,EACvChB,WAAW,GAAG,MAAM,GAAG;0BAAO;4BAAA0C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB,CAAC,EACRuD,OAAO,CAACtF,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,GAAG,kBACzBxH,OAAA;4BACEyB,SAAS,EAAC,wBAAwB;4BAClCQ,OAAO,EAAEA,CAAA,KAAM;8BACb,IAAI2F,MAAM,CAACC,OAAO,CAACnH,WAAW,GAAG,wCAAwC,GAAG,+CAA+C,CAAC,EAAE;gCAC5HY,mBAAmB,CAACwF,OAAO,CAACU,GAAG,IAAIV,OAAO,CAACW,EAAE,CAAC;8BAChD;4BACF,CAAE;4BAAA/F,QAAA,EAEDhB,WAAW,GAAG,MAAM,GAAG;0BAAQ;4BAAA0C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B,CACT;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,GAtCEuD,OAAO,CAACU,GAAG,IAAIV,OAAO,CAACW,EAAE;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuC9B,CAAC;kBAAA,CACP;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA/PazB,KAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgQV,CAAC;MAAA,CAClB;IAAC,gBACF,CAAC,gBAEHvD,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B1B,OAAA,CAACH,eAAe;QAAC4B,SAAS,EAAC;MAAY;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CvD,OAAA;QAAA0B,QAAA,EAAKhB,WAAW,GAAG,6BAA6B,GAAG;MAAiB;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1EvD,OAAA;QAAA0B,QAAA,EAAIhB,WAAW,GAAG,kEAAkE,GAAG;MAA4D;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxJvD,OAAA;QAAGyB,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEhB,WAAW,GAAG,yCAAyC,GAAG;MAA6C;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpI;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACuE,EAAA,GAzSI3H,SAAS;AA2Sf,eAAeA,SAAS;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}