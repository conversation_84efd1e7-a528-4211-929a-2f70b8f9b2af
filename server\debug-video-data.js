const mongoose = require('mongoose');
require('dotenv').config();

const debugVideoData = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Check what's actually in the database
    const db = mongoose.connection.db;
    const videos = await db.collection('studyvideos').find().limit(3).toArray();
    
    console.log(`\n📹 Found ${videos.length} videos in database:`);
    
    videos.forEach((video, index) => {
      console.log(`\n${index + 1}. Video: ${video.title || 'No title'}`);
      console.log(`   - _id: ${video._id}`);
      console.log(`   - Subject: ${video.subject || 'No subject'}`);
      console.log(`   - Class: ${video.className || 'No class'}`);
      console.log(`   - Level: ${video.level || 'No level'}`);
      console.log(`   - VideoID: ${video.videoID || 'No videoID'}`);
      console.log(`   - VideoURL: ${video.videoUrl || 'No videoUrl'}`);
      console.log(`   - Thumbnail: ${video.thumbnail || 'NO THUMBNAIL'}`);
      console.log(`   - All fields: ${Object.keys(video).join(', ')}`);
      
      if (video.thumbnail) {
        console.log(`   - Thumbnail type: ${typeof video.thumbnail}`);
        console.log(`   - Thumbnail length: ${video.thumbnail.length}`);
        console.log(`   - Thumbnail preview: ${video.thumbnail.substring(0, 100)}...`);
      }
    });
    
    // Test the API query that the frontend uses
    console.log('\n🔍 Testing API query structure...');
    
    // Simulate the videos-subtitle-status query
    const apiVideos = await db.collection('studyvideos').find({}, {
      title: 1,
      subject: 1,
      className: 1,
      level: 1,
      videoUrl: 1,
      videoID: 1,
      thumbnail: 1,
      subtitles: 1,
      hasSubtitles: 1,
      subtitleGenerationStatus: 1,
      createdAt: 1
    }).sort({ createdAt: -1 }).limit(3).toArray();
    
    console.log(`\n📡 API query returned ${apiVideos.length} videos:`);
    apiVideos.forEach((video, index) => {
      console.log(`\n${index + 1}. API Video: ${video.title}`);
      console.log(`   - Has thumbnail: ${video.thumbnail ? 'YES' : 'NO'}`);
      if (video.thumbnail) {
        console.log(`   - Thumbnail: ${video.thumbnail.substring(0, 100)}...`);
      }
    });
    
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
};

debugVideoData();
