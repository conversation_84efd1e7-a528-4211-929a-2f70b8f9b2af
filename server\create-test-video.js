const mongoose = require('mongoose');
require('dotenv').config();

const createTestVideo = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    
    // Create a test video with a real YouTube thumbnail
    const testVideo = {
      className: '1',
      subject: 'Mathematics',
      title: 'Introduction to Numbers',
      level: 'primary',
      videoID: 'dQw4w9WgXcQ', // Rick Roll video ID for testing
      videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/hqdefault.jpg'
    };
    
    console.log('🎥 Creating test video with real thumbnail...');
    const result = await db.collection('studyvideos').insertOne(testVideo);
    console.log('✅ Test video created:', result.insertedId);
    
    // Verify the video was created
    const createdVideo = await db.collection('studyvideos').findOne({ _id: result.insertedId });
    console.log('\n📹 Created video details:');
    console.log(`   - Title: ${createdVideo.title}`);
    console.log(`   - Thumbnail: ${createdVideo.thumbnail}`);
    console.log(`   - VideoID: ${createdVideo.videoID}`);
    
    // Test if the thumbnail URL is accessible
    console.log('\n🔍 Testing thumbnail URL accessibility...');
    try {
      const fetch = require('node-fetch');
      const response = await fetch(createdVideo.thumbnail);
      console.log(`   - Thumbnail HTTP status: ${response.status}`);
      console.log(`   - Thumbnail accessible: ${response.ok ? 'YES' : 'NO'}`);
    } catch (error) {
      console.log(`   - Thumbnail test failed: ${error.message}`);
    }
    
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
};

createTestVideo();
