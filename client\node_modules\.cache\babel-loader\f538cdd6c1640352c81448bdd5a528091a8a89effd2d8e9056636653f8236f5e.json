{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    console.log('🔍 CHECKING THUMBNAIL FOR VIDEO:', video.title);\n    console.log('🔍 video.thumbnail:', video.thumbnail);\n    console.log('🔍 video.thumbnailUrl:', video.thumbnailUrl);\n    console.log('🔍 video.image:', video.image);\n    console.log('🔍 video.poster:', video.poster);\n    console.log('🔍 video.cover:', video.cover);\n    console.log('🔍 ALL VIDEO FIELDS:', Object.keys(video));\n\n    // Check all possible thumbnail fields\n    const thumbnailFields = ['thumbnail', 'thumbnailUrl', 'image', 'imageUrl', 'poster', 'posterUrl', 'cover', 'coverImage', 'previewImage', 'videoThumbnail', 'thumb', 'preview'];\n    for (const field of thumbnailFields) {\n      if (video[field] && typeof video[field] === 'string' && video[field].trim() !== '') {\n        console.log(`✅ FOUND THUMBNAIL in field \"${field}\":`, video[field]);\n        return video[field];\n      }\n    }\n    console.log('❌ NO THUMBNAIL FOUND - using placeholder');\n    // Clean placeholder for videos without database thumbnails\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n  const formatTimeAgo = timestamp => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.username) || (user === null || user === void 0 ? void 0 : user.displayName) || 'Student';\n    const fullName = user !== null && user !== void 0 && user.firstName && user !== null && user !== void 0 && user.lastName ? `${user.firstName} ${user.lastName}` : userName;\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user === null || user === void 0 ? void 0 : user.name);\n    console.log('  - user?.firstName:', user === null || user === void 0 ? void 0 : user.firstName);\n    console.log('  - user?.lastName:', user === null || user === void 0 ? void 0 : user.lastName);\n    console.log('  - user?.username:', user === null || user === void 0 ? void 0 : user.username);\n    console.log('  - user?.displayName:', user === null || user === void 0 ? void 0 : user.displayName);\n    console.log('  - Final fullName:', fullName);\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userId: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userRole: user === null || user === void 0 ? void 0 : user.role,\n      isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user === null || user === void 0 ? void 0 : user.firstName,\n        lastName: user === null || user === void 0 ? void 0 : user.lastName,\n        username: user === null || user === void 0 ? void 0 : user.username,\n        email: user === null || user === void 0 ? void 0 : user.email,\n        role: user === null || user === void 0 ? void 0 : user.role,\n        avatar: (user === null || user === void 0 ? void 0 : user.avatar) || (user === null || user === void 0 ? void 0 : user.profilePicture)\n      }\n    };\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend using the correct API\n    try {\n      const videoId = video._id || video.id;\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          videoId: videoId,\n          text: newComment.trim(),\n          author: fullName,\n          avatar: (user === null || user === void 0 ? void 0 : user.avatar) || (user === null || user === void 0 ? void 0 : user.profilePicture) || fullName.charAt(0).toUpperCase(),\n          userLevel: (user === null || user === void 0 ? void 0 : user.level) || 'primary',\n          userClass: (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className)\n        })\n      });\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      const videoId = video._id || video.id;\n      setComments(prev => ({\n        ...prev,\n        [videoId]: [...(prev[videoId] || []), comment]\n      }));\n    }\n    setNewComment('');\n  };\n  const handleLikeComment = commentId => {\n    if (!(user !== null && user !== void 0 && user._id) && !(user !== null && user !== void 0 && user.id) || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    const userId = (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id);\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).map(comment => {\n        if (comment.id === commentId || comment._id === commentId) {\n          var _comment$likedBy;\n          const isLiked = (_comment$likedBy = comment.likedBy) === null || _comment$likedBy === void 0 ? void 0 : _comment$likedBy.includes(userId);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked ? comment.likedBy.filter(id => id !== userId) : [...(comment.likedBy || []), userId]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n  const handleDeleteComment = commentId => {\n    if (currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).filter(comment => comment.id !== commentId && comment._id !== commentId)\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n    setCommentsExpanded(false);\n    setNewComment('');\n    const videoId = video._id || video.id;\n\n    // Load existing comments from backend\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments/${videoId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const commentsData = await response.json();\n        console.log('✅ Loaded existing comments:', commentsData);\n        if (commentsData.success && commentsData.data && commentsData.data.comments) {\n          setComments(prev => ({\n            ...prev,\n            [videoId]: commentsData.data.comments\n          }));\n        }\n      } else {\n        console.log('⚠️ Failed to load comments from backend');\n      }\n    } catch (error) {\n      console.log('⚠️ Error loading comments:', error);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🎥 Attempting to fetch videos from database...');\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        var _response, _response2;\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n        if ((_response = response) !== null && _response !== void 0 && _response.success && (_response2 = response) !== null && _response2 !== void 0 && _response2.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - imageUrl:', videos[0].imageUrl);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n            console.log('  - thumb:', videos[0].thumb);\n            console.log('  - preview:', videos[0].preview);\n\n            // Check a few more videos to see if any have thumbnails\n            console.log('🔍 CHECKING MORE VIDEOS FOR THUMBNAILS:');\n            videos.slice(0, 5).forEach((video, index) => {\n              console.log(`Video ${index + 1} (${video.title}):`, {\n                thumbnail: video.thumbnail,\n                thumbnailUrl: video.thumbnailUrl,\n                image: video.image,\n                imageUrl: video.imageUrl,\n                poster: video.poster,\n                thumb: video.thumb,\n                preview: video.preview\n              });\n            });\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          var _response3, _response3$data, _response4, _response4$data;\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n          if ((_response3 = response) !== null && _response3 !== void 0 && (_response3$data = _response3.data) !== null && _response3$data !== void 0 && _response3$data.success && (_response4 = response) !== null && _response4 !== void 0 && (_response4$data = _response4.data) !== null && _response4$data !== void 0 && _response4$data.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          var _response5, _response5$data, _response6, _response6$data;\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n          if ((_response5 = response) !== null && _response5 !== void 0 && (_response5$data = _response5.data) !== null && _response5$data !== void 0 && _response5$data.success && (_response6 = response) !== null && _response6 !== void 0 && (_response6$data = _response6.data) !== null && _response6$data !== void 0 && _response6$data.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item => {\n              var _item$title;\n              return item.type === 'video' || item.videoUrl || item.videoID || ((_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.toLowerCase().includes('video'));\n            });\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel || !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass || !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject || !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => {\n          var _ref, _ref$charAt;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-item\",\n            children: currentVideoIndex === index ?\n            /*#__PURE__*/\n            /* Video Player - Replaces the thumbnail when playing */\n            _jsxDEV(\"div\", {\n              className: \"inline-video-player\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-style-layout\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-player\",\n                  children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    autoPlay: true,\n                    playsInline: true,\n                    preload: \"metadata\",\n                    width: \"100%\",\n                    height: \"100%\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      backgroundColor: '#000',\n                      objectFit: 'contain'\n                    },\n                    onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                    onCanPlay: () => setVideoError(null),\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 607,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 609,\n                      columnNumber: 31\n                    }, this)), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 27\n                  }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                    title: video.title,\n                    frameBorder: \"0\",\n                    allowFullScreen: true,\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      border: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-error\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"error-icon\",\n                      children: \"\\u26A0\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: \"Video Unavailable\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError || \"This video cannot be played at the moment.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 632,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"youtube-video-title\",\n                    children: video.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: getSubjectName(video.subject)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 640,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Class \", video.className || video.class]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 27\n                    }, this), video.level && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: video.level\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `youtube-action-btn ${commentsExpanded ? 'active' : ''}`,\n                      onClick: () => setCommentsExpanded(!commentsExpanded),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 655,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Comments\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 656,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 651,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDC4D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 659,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Like\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 658,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      onClick: () => setCurrentVideoIndex(null),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2715\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 666,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Close\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 23\n                }, this), commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-header\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [getCurrentVideoComments().length, \" Comments\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comment-input\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment-avatar\",\n                      children: (_ref = (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.username) || 'Student') === null || _ref === void 0 ? void 0 : (_ref$charAt = _ref.charAt(0)) === null || _ref$charAt === void 0 ? void 0 : _ref$charAt.toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                        className: \"youtube-comment-input-field\",\n                        value: newComment,\n                        onChange: e => setNewComment(e.target.value),\n                        placeholder: \"Add a comment...\",\n                        rows: \"1\",\n                        style: {\n                          minHeight: '20px',\n                          resize: 'none',\n                          overflow: 'hidden'\n                        },\n                        onInput: e => {\n                          e.target.style.height = 'auto';\n                          e.target.style.height = e.target.scrollHeight + 'px';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 685,\n                        columnNumber: 31\n                      }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-actions\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn cancel\",\n                          onClick: () => setNewComment(''),\n                          children: \"Cancel\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 703,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn submit\",\n                          onClick: handleAddComment,\n                          disabled: !newComment.trim(),\n                          children: \"Comment\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 709,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 702,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-list\",\n                    children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center',\n                        padding: '40px 0',\n                        color: '#606060'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '48px',\n                          marginBottom: '16px'\n                        },\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No comments yet. Be the first to share your thoughts!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 726,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 31\n                    }, this) : getCurrentVideoComments().map(comment => {\n                      var _comment$author, _comment$author$charA, _comment$likedBy2, _comment$likedBy3;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-avatar\",\n                          children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 731,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-content\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-author\",\n                              children: comment.author\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 736,\n                              columnNumber: 39\n                            }, this), (comment.userRole === 'admin' || comment.isAdmin) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                              style: {\n                                color: '#1d9bf0',\n                                fontSize: '12px',\n                                marginLeft: '4px'\n                              },\n                              title: \"Verified Admin\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 738,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-time\",\n                              children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 740,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 735,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-text\",\n                            children: comment.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 744,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-actions\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => handleLikeComment(comment._id || comment.id),\n                              className: `youtube-comment-action ${(_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                children: (_comment$likedBy3 = comment.likedBy) !== null && _comment$likedBy3 !== void 0 && _comment$likedBy3.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 752,\n                                columnNumber: 41\n                              }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: comment.likes\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 753,\n                                columnNumber: 63\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 748,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: \"\\uD83D\\uDC4E\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 756,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 755,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: \"Reply\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 758,\n                              columnNumber: 39\n                            }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                children: \"Edit\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 763,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                onClick: () => {\n                                  if (window.confirm('Are you sure you want to delete this comment?')) {\n                                    handleDeleteComment(comment._id || comment.id);\n                                  }\n                                },\n                                children: \"Delete\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 766,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 747,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 734,\n                          columnNumber: 35\n                        }, this)]\n                      }, comment._id || comment.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 33\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 19\n            }, this) :\n            /*#__PURE__*/\n            /* Video Card - Shows thumbnail when not playing */\n            _jsxDEV(\"div\", {\n              className: \"video-card\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-thumbnail\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getThumbnailUrl(video),\n                  alt: video.title,\n                  className: \"thumbnail-image\",\n                  loading: \"lazy\",\n                  onError: e => {\n                    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                      let videoId = video.videoID;\n                      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                      videoId = match ? match[1] : videoId;\n                      const fallbacks = [`https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`, `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4='];\n                      const currentSrc = e.target.src;\n                      const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop().split('.')[0]));\n                      if (currentIndex < fallbacks.length - 1) {\n                        e.target.src = fallbacks[currentIndex + 1];\n                      }\n                    } else {\n                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"play-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                    className: \"play-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-duration\",\n                  children: video.duration || \"Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 23\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 27\n                  }, this), \"CC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 834,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 836,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-tags\",\n                  children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"topic-tag\",\n                    children: video.topic\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 41\n                  }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"shared-tag\",\n                    children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 513,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"sUl5UJEZU46h2aSoRLty1l9iUvM=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "getAllVideos", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoLessons", "_s", "user", "state", "console", "log", "Object", "keys", "users", "auth", "storedUser", "localStorage", "getItem", "JSON", "parse", "e", "error", "videos", "setVideos", "loading", "setLoading", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "comments", "setComments", "newComment", "setNewComment", "commentsExpanded", "setCommentsExpanded", "replyingTo", "setReplyingTo", "isKiswahili", "getThumbnailUrl", "video", "title", "thumbnail", "thumbnailUrl", "image", "poster", "cover", "thumbnailFields", "field", "trim", "getSubjectName", "subject", "subjectMap", "getCurrentVideoComments", "filteredAndSortedVideos", "videoId", "_id", "id", "formatTimeAgo", "timestamp", "now", "Date", "time", "diffInSeconds", "Math", "floor", "handleAddComment", "userName", "name", "firstName", "username", "displayName", "fullName", "lastName", "comment", "toString", "text", "author", "userId", "userRole", "role", "isAdmin", "toISOString", "createdAt", "likes", "<PERSON><PERSON><PERSON>", "userProfile", "email", "avatar", "profilePicture", "response", "fetch", "process", "env", "REACT_APP_API_URL", "method", "headers", "body", "stringify", "char<PERSON>t", "toUpperCase", "userLevel", "level", "userClass", "class", "className", "ok", "savedComment", "json", "prev", "data", "handleLikeComment", "commentId", "map", "_comment$likedBy", "isLiked", "includes", "filter", "handleDeleteComment", "handleShowVideo", "index", "commentsData", "success", "videoUrl", "signedVideoUrl", "warn", "fetchVideos", "_response", "_response2", "length", "imageUrl", "posterUrl", "coverImage", "previewImage", "videoThumbnail", "thumb", "preview", "slice", "for<PERSON>ach", "message", "_response3", "_response3$data", "_response4", "_response4$data", "filters", "type", "_response5", "_response5$data", "_response6", "_response6$data", "allData", "item", "_item$title", "videoID", "toLowerCase", "filtered", "matchesLevel", "matchesClass", "matchesSubject", "err", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "topic", "sort", "a", "b", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "target", "onClick", "_ref", "_ref$charAt", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "style", "backgroundColor", "objectFit", "onError", "onCanPlay", "crossOrigin", "src", "subtitles", "subtitle", "kind", "url", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "flex", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "disabled", "textAlign", "padding", "color", "fontSize", "marginBottom", "_comment$author", "_comment$author$charA", "_comment$likedBy2", "_comment$likedBy3", "marginLeft", "window", "confirm", "alt", "match", "fallbacks", "currentSrc", "currentIndex", "findIndex", "split", "pop", "duration", "sharedFromClass", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    console.log('🔍 CHECKING THUMBNAIL FOR VIDEO:', video.title);\n    console.log('🔍 video.thumbnail:', video.thumbnail);\n    console.log('🔍 video.thumbnailUrl:', video.thumbnailUrl);\n    console.log('🔍 video.image:', video.image);\n    console.log('🔍 video.poster:', video.poster);\n    console.log('🔍 video.cover:', video.cover);\n    console.log('🔍 ALL VIDEO FIELDS:', Object.keys(video));\n\n    // Check all possible thumbnail fields\n    const thumbnailFields = [\n      'thumbnail',\n      'thumbnailUrl',\n      'image',\n      'imageUrl',\n      'poster',\n      'posterUrl',\n      'cover',\n      'coverImage',\n      'previewImage',\n      'videoThumbnail',\n      'thumb',\n      'preview'\n    ];\n\n    for (const field of thumbnailFields) {\n      if (video[field] && typeof video[field] === 'string' && video[field].trim() !== '') {\n        console.log(`✅ FOUND THUMBNAIL in field \"${field}\":`, video[field]);\n        return video[field];\n      }\n    }\n\n    console.log('❌ NO THUMBNAIL FOUND - using placeholder');\n    // Clean placeholder for videos without database thumbnails\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = user?.name || user?.firstName || user?.username || user?.displayName || 'Student';\n    const fullName = user?.firstName && user?.lastName\n      ? `${user.firstName} ${user.lastName}`\n      : userName;\n\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user?.name);\n    console.log('  - user?.firstName:', user?.firstName);\n    console.log('  - user?.lastName:', user?.lastName);\n    console.log('  - user?.username:', user?.username);\n    console.log('  - user?.displayName:', user?.displayName);\n    console.log('  - Final fullName:', fullName);\n\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: user?._id || user?.id,\n      userId: user?._id || user?.id,\n      userRole: user?.role,\n      isAdmin: user?.role === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user?.firstName,\n        lastName: user?.lastName,\n        username: user?.username,\n        email: user?.email,\n        role: user?.role,\n        avatar: user?.avatar || user?.profilePicture\n      }\n    };\n\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend using the correct API\n    try {\n      const videoId = video._id || video.id;\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n        },\n        body: JSON.stringify({\n          videoId: videoId,\n          text: newComment.trim(),\n          author: fullName,\n          avatar: user?.avatar || user?.profilePicture || fullName.charAt(0).toUpperCase(),\n          userLevel: user?.level || 'primary',\n          userClass: user?.class || user?.className\n        })\n      });\n\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      const videoId = video._id || video.id;\n      setComments(prev => ({\n        ...prev,\n        [videoId]: [...(prev[videoId] || []), comment]\n      }));\n    }\n\n    setNewComment('');\n  };\n\n  const handleLikeComment = (commentId) => {\n    if (!user?._id && !user?.id || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n    const userId = user?._id || user?.id;\n\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).map(comment => {\n        if (comment.id === commentId || comment._id === commentId) {\n          const isLiked = comment.likedBy?.includes(userId);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked\n              ? comment.likedBy.filter(id => id !== userId)\n              : [...(comment.likedBy || []), userId]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n\n  const handleDeleteComment = (commentId) => {\n    if (currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).filter(comment =>\n        comment.id !== commentId && comment._id !== commentId\n      )\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n    setCommentsExpanded(false);\n    setNewComment('');\n\n    const videoId = video._id || video.id;\n\n    // Load existing comments from backend\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments/${videoId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n        },\n      });\n\n      if (response.ok) {\n        const commentsData = await response.json();\n        console.log('✅ Loaded existing comments:', commentsData);\n\n        if (commentsData.success && commentsData.data && commentsData.data.comments) {\n          setComments(prev => ({\n            ...prev,\n            [videoId]: commentsData.data.comments\n          }));\n        }\n      } else {\n        console.log('⚠️ Failed to load comments from backend');\n      }\n    } catch (error) {\n      console.log('⚠️ Error loading comments:', error);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🎥 Attempting to fetch videos from database...');\n\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n\n        if (response?.success && response?.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - imageUrl:', videos[0].imageUrl);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n            console.log('  - thumb:', videos[0].thumb);\n            console.log('  - preview:', videos[0].preview);\n\n            // Check a few more videos to see if any have thumbnails\n            console.log('🔍 CHECKING MORE VIDEOS FOR THUMBNAILS:');\n            videos.slice(0, 5).forEach((video, index) => {\n              console.log(`Video ${index + 1} (${video.title}):`, {\n                thumbnail: video.thumbnail,\n                thumbnailUrl: video.thumbnailUrl,\n                image: video.image,\n                imageUrl: video.imageUrl,\n                poster: video.poster,\n                thumb: video.thumb,\n                preview: video.preview\n              });\n            });\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item =>\n              item.type === 'video' ||\n              item.videoUrl ||\n              item.videoID ||\n              item.title?.toLowerCase().includes('video')\n            );\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' ||\n                              video.level === selectedLevel ||\n                              !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' ||\n                              video.className === selectedClass ||\n                              video.class === selectedClass ||\n                              !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' ||\n                                video.subject === selectedSubject ||\n                                !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {currentVideoIndex === index ? (\n                  /* Video Player - Replaces the thumbnail when playing */\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                          {video.level && (\n                            <>\n                              <span>•</span>\n                              <span>{video.level}</span>\n                            </>\n                          )}\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className={`youtube-action-btn ${commentsExpanded ? 'active' : ''}`}\n                            onClick={() => setCommentsExpanded(!commentsExpanded)}\n                          >\n                            <span>💬</span>\n                            <span>Comments</span>\n                          </button>\n                          <button className=\"youtube-action-btn\">\n                            <span>👍</span>\n                            <span>Like</span>\n                          </button>\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {commentsExpanded && (\n                        <div className=\"youtube-comments-section\">\n                          <div className=\"youtube-comments-header\">\n                            <span>{getCurrentVideoComments().length} Comments</span>\n                          </div>\n\n                          {/* Add Comment */}\n                          <div className=\"youtube-comment-input\">\n                            <div className=\"youtube-comment-avatar\">\n                              {(user?.name || user?.firstName || user?.username || 'Student')?.charAt(0)?.toUpperCase()}\n                            </div>\n                            <div style={{ flex: 1 }}>\n                              <textarea\n                                className=\"youtube-comment-input-field\"\n                                value={newComment}\n                                onChange={(e) => setNewComment(e.target.value)}\n                                placeholder=\"Add a comment...\"\n                                rows=\"1\"\n                                style={{\n                                  minHeight: '20px',\n                                  resize: 'none',\n                                  overflow: 'hidden'\n                                }}\n                                onInput={(e) => {\n                                  e.target.style.height = 'auto';\n                                  e.target.style.height = e.target.scrollHeight + 'px';\n                                }}\n                              />\n                              {newComment.trim() && (\n                                <div className=\"youtube-comment-actions\">\n                                  <button\n                                    className=\"youtube-comment-btn cancel\"\n                                    onClick={() => setNewComment('')}\n                                  >\n                                    Cancel\n                                  </button>\n                                  <button\n                                    className=\"youtube-comment-btn submit\"\n                                    onClick={handleAddComment}\n                                    disabled={!newComment.trim()}\n                                  >\n                                    Comment\n                                  </button>\n                                </div>\n                              )}\n                            </div>\n                          </div>\n\n                          {/* Comments List */}\n                          <div className=\"youtube-comments-list\">\n                            {getCurrentVideoComments().length === 0 ? (\n                              <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                                <p>No comments yet. Be the first to share your thoughts!</p>\n                              </div>\n                            ) : (\n                              getCurrentVideoComments().map((comment) => (\n                                <div key={comment._id || comment.id} className=\"youtube-comment\">\n                                  <div className=\"youtube-comment-avatar\">\n                                    {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                  </div>\n                                  <div className=\"youtube-comment-content\">\n                                    <div className=\"youtube-comment-header\">\n                                      <span className=\"youtube-comment-author\">{comment.author}</span>\n                                      {(comment.userRole === 'admin' || comment.isAdmin) && (\n                                        <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title=\"Verified Admin\" />\n                                      )}\n                                      <span className=\"youtube-comment-time\">\n                                        {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                      </span>\n                                    </div>\n                                    <div className=\"youtube-comment-text\">\n                                      {comment.text}\n                                    </div>\n                                    <div className=\"youtube-comment-actions\">\n                                      <button\n                                        onClick={() => handleLikeComment(comment._id || comment.id)}\n                                        className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                      >\n                                        <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                        {comment.likes > 0 && <span>{comment.likes}</span>}\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        <span>👎</span>\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        Reply\n                                      </button>\n                                      {comment.user === user?._id && (\n                                        <>\n                                          <button className=\"youtube-comment-action\">\n                                            Edit\n                                          </button>\n                                          <button\n                                            className=\"youtube-comment-action\"\n                                            onClick={() => {\n                                              if (window.confirm('Are you sure you want to delete this comment?')) {\n                                                handleDeleteComment(comment._id || comment.id);\n                                              }\n                                            }}\n                                          >\n                                            Delete\n                                          </button>\n                                        </>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              ))\n                            )}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                ) : (\n                  /* Video Card - Shows thumbnail when not playing */\n                  <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                    <div className=\"video-card-thumbnail\">\n                      <img\n                        src={getThumbnailUrl(video)}\n                        alt={video.title}\n                        className=\"thumbnail-image\"\n                        loading=\"lazy\"\n                        onError={(e) => {\n                          if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                            let videoId = video.videoID;\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                            const fallbacks = [\n                              `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n                              `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                              `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                              `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                              'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4='\n                            ];\n                            const currentSrc = e.target.src;\n                            const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop().split('.')[0]));\n                            if (currentIndex < fallbacks.length - 1) {\n                              e.target.src = fallbacks[currentIndex + 1];\n                            }\n                          } else {\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                          }\n                        }}\n                      />\n                      <div className=\"play-overlay\">\n                        <FaPlayCircle className=\"play-icon\" />\n                      </div>\n                      <div className=\"video-duration\">\n                        {video.duration || \"Video\"}\n                      </div>\n                      {video.subtitles && video.subtitles.length > 0 && (\n                        <div className=\"subtitle-badge\">\n                          <TbInfoCircle />\n                          CC\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"video-card-content\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                        <span className=\"video-class\">\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                            : `Form ${video.className || video.class}`}\n                        </span>\n                      </div>\n                      <div className=\"video-tags\">\n                        {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                        {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                          <span className=\"shared-tag\">\n                            {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                            {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                              ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                              : `Form ${video.sharedFromClass}`}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,IAAI,GAAGd,WAAW,CAACe,KAAK,IAAI;IAChC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,KAAK,CAAC;MACpDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,MAAM,CAACC,IAAI,CAACJ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAIA,KAAK,IAAIA,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACN,IAAI,EAAE;QAC5CE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,OAAOF,KAAK,CAACK,KAAK,CAACN,IAAI;MACzB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACD,IAAI,EAAE;QACvBE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,OAAOF,KAAK,CAACD,IAAI;MACnB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAACP,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACM,IAAI,CAACP,IAAI;MACxB;MAEAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMK,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACd,IAAI;UACFN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,OAAOQ,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;QAC/B,CAAC,CAAC,OAAOK,CAAC,EAAE;UACVX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,KAAK,EAAEK,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM4D,WAAW,GAAGpB,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMqB,eAAe,GAAIC,KAAK,IAAK;IACjC1C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEyC,KAAK,CAACC,KAAK,CAAC;IAC5D3C,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyC,KAAK,CAACE,SAAS,CAAC;IACnD5C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyC,KAAK,CAACG,YAAY,CAAC;IACzD7C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEyC,KAAK,CAACI,KAAK,CAAC;IAC3C9C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyC,KAAK,CAACK,MAAM,CAAC;IAC7C/C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEyC,KAAK,CAACM,KAAK,CAAC;IAC3ChD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,MAAM,CAACC,IAAI,CAACuC,KAAK,CAAC,CAAC;;IAEvD;IACA,MAAMO,eAAe,GAAG,CACtB,WAAW,EACX,cAAc,EACd,OAAO,EACP,UAAU,EACV,QAAQ,EACR,WAAW,EACX,OAAO,EACP,YAAY,EACZ,cAAc,EACd,gBAAgB,EAChB,OAAO,EACP,SAAS,CACV;IAED,KAAK,MAAMC,KAAK,IAAID,eAAe,EAAE;MACnC,IAAIP,KAAK,CAACQ,KAAK,CAAC,IAAI,OAAOR,KAAK,CAACQ,KAAK,CAAC,KAAK,QAAQ,IAAIR,KAAK,CAACQ,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAClFnD,OAAO,CAACC,GAAG,CAAE,+BAA8BiD,KAAM,IAAG,EAAER,KAAK,CAACQ,KAAK,CAAC,CAAC;QACnE,OAAOR,KAAK,CAACQ,KAAK,CAAC;MACrB;IACF;IAEAlD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvD;IACA,OAAO,4cAA4c;EACrd,CAAC;EAED,MAAMmD,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEd,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOc,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI7B,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAMgB,KAAK,GAAGc,uBAAuB,CAAC9B,iBAAiB,CAAC;IACxD,MAAM+B,OAAO,GAAG,CAAAf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,GAAG,MAAIhB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,EAAE;IACvC3D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEwD,OAAO,CAAC;IACzDzD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE+B,QAAQ,CAAC;IAC/C,OAAOA,QAAQ,CAACyB,OAAO,CAAC,IAAI,EAAE;EAChC,CAAC;EAED,MAAMG,aAAa,GAAIC,SAAS,IAAK;IACnC,IAAI,CAACA,SAAS,EAAE,OAAO,UAAU;IACjC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,SAAS,CAAC;IAChC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGE,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAE,cAAa;IAChF,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAE,YAAW;IACjF,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAE,WAAU;EACxD,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAClC,UAAU,CAACiB,IAAI,CAAC,CAAC,IAAIzB,iBAAiB,KAAK,IAAI,EAAE;IAEtD,MAAMgB,KAAK,GAAGc,uBAAuB,CAAC9B,iBAAiB,CAAC;IAExD1B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,IAAI,CAAC;;IAEtD;IACA,MAAMuE,QAAQ,GAAG,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,IAAI,MAAIxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,SAAS,MAAIzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,QAAQ,MAAI1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,WAAW,KAAI,SAAS;IAClG,MAAMC,QAAQ,GAAG5E,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyE,SAAS,IAAIzE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6E,QAAQ,GAC7C,GAAE7E,IAAI,CAACyE,SAAU,IAAGzE,IAAI,CAAC6E,QAAS,EAAC,GACpCN,QAAQ;IAEZrE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvCD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,IAAI,CAAC;IAC1CtE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,SAAS,CAAC;IACpDvE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E,QAAQ,CAAC;IAClD3E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,QAAQ,CAAC;IAClDxE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,WAAW,CAAC;IACxDzE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyE,QAAQ,CAAC;IAE5C,MAAME,OAAO,GAAG;MACdjB,EAAE,EAAEI,IAAI,CAACD,GAAG,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC;MACzBC,IAAI,EAAE5C,UAAU,CAACiB,IAAI,CAAC,CAAC;MACvB4B,MAAM,EAAEL,QAAQ;MAChB5E,IAAI,EAAE,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,GAAG,MAAI5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,EAAE;MAC3BqB,MAAM,EAAE,CAAAlF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,GAAG,MAAI5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,EAAE;MAC7BsB,QAAQ,EAAEnF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,IAAI;MACpBC,OAAO,EAAE,CAAArF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,IAAI,MAAK,OAAO;MAC/BrB,SAAS,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACqB,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAItB,IAAI,CAAC,CAAC,CAACqB,WAAW,CAAC,CAAC;MACnCE,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE,EAAE;MACX;MACAC,WAAW,EAAE;QACXlB,IAAI,EAAEI,QAAQ;QACdH,SAAS,EAAEzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,SAAS;QAC1BI,QAAQ,EAAE7E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E,QAAQ;QACxBH,QAAQ,EAAE1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,QAAQ;QACxBiB,KAAK,EAAE3F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2F,KAAK;QAClBP,IAAI,EAAEpF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,IAAI;QAChBQ,MAAM,EAAE,CAAA5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4F,MAAM,MAAI5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,cAAc;MAC9C;IACF,CAAC;IAED3F,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE2E,OAAO,CAAC;;IAElD;IACA,IAAI;MACF,MAAMnB,OAAO,GAAGf,KAAK,CAACgB,GAAG,IAAIhB,KAAK,CAACiB,EAAE;MACrC,MAAMiC,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,qBAAoB,EAAE;QAC7GC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAAS3F,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACD2F,IAAI,EAAE1F,IAAI,CAAC2F,SAAS,CAAC;UACnB3C,OAAO,EAAEA,OAAO;UAChBqB,IAAI,EAAE5C,UAAU,CAACiB,IAAI,CAAC,CAAC;UACvB4B,MAAM,EAAEL,QAAQ;UAChBgB,MAAM,EAAE,CAAA5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4F,MAAM,MAAI5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,cAAc,KAAIjB,QAAQ,CAAC2B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAChFC,SAAS,EAAE,CAAAzG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,KAAK,KAAI,SAAS;UACnCC,SAAS,EAAE,CAAA3G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4G,KAAK,MAAI5G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6G,SAAS;QAC3C,CAAC;MACH,CAAC,CAAC;MAEF,IAAIf,QAAQ,CAACgB,EAAE,EAAE;QACf,MAAMC,YAAY,GAAG,MAAMjB,QAAQ,CAACkB,IAAI,CAAC,CAAC;QAC1C9G,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE4G,YAAY,CAAC;;QAExD;QACA5E,WAAW,CAAC8E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAACtD,OAAO,GAAG,CAAC,IAAIsD,IAAI,CAACtD,OAAO,CAAC,IAAI,EAAE,CAAC,EAAEoD,YAAY,CAACG,IAAI,IAAIH,YAAY;QACzE,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL7G,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D;QACAgC,WAAW,CAAC8E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAACtD,OAAO,GAAG,CAAC,IAAIsD,IAAI,CAACtD,OAAO,CAAC,IAAI,EAAE,CAAC,EAAEmB,OAAO;QAC/C,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEW,KAAK,CAAC;MAC5D;MACA,MAAM6C,OAAO,GAAGf,KAAK,CAACgB,GAAG,IAAIhB,KAAK,CAACiB,EAAE;MACrC1B,WAAW,CAAC8E,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACtD,OAAO,GAAG,CAAC,IAAIsD,IAAI,CAACtD,OAAO,CAAC,IAAI,EAAE,CAAC,EAAEmB,OAAO;MAC/C,CAAC,CAAC,CAAC;IACL;IAEAzC,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAM8E,iBAAiB,GAAIC,SAAS,IAAK;IACvC,IAAI,EAACpH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4D,GAAG,KAAI,EAAC5D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6D,EAAE,KAAIjC,iBAAiB,KAAK,IAAI,EAAE;IAE3D,MAAMgB,KAAK,GAAGc,uBAAuB,CAAC9B,iBAAiB,CAAC;IACxD,MAAM+B,OAAO,GAAG,CAAAf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,GAAG,MAAIhB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,EAAE;IACvC,MAAMqB,MAAM,GAAG,CAAAlF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,GAAG,MAAI5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,EAAE;IAEpC1B,WAAW,CAAC8E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACtD,OAAO,GAAG,CAACsD,IAAI,CAACtD,OAAO,CAAC,IAAI,EAAE,EAAE0D,GAAG,CAACvC,OAAO,IAAI;QAC9C,IAAIA,OAAO,CAACjB,EAAE,KAAKuD,SAAS,IAAItC,OAAO,CAAClB,GAAG,KAAKwD,SAAS,EAAE;UAAA,IAAAE,gBAAA;UACzD,MAAMC,OAAO,IAAAD,gBAAA,GAAGxC,OAAO,CAACW,OAAO,cAAA6B,gBAAA,uBAAfA,gBAAA,CAAiBE,QAAQ,CAACtC,MAAM,CAAC;UACjD,OAAO;YACL,GAAGJ,OAAO;YACVU,KAAK,EAAE+B,OAAO,GAAGzC,OAAO,CAACU,KAAK,GAAG,CAAC,GAAGV,OAAO,CAACU,KAAK,GAAG,CAAC;YACtDC,OAAO,EAAE8B,OAAO,GACZzC,OAAO,CAACW,OAAO,CAACgC,MAAM,CAAC5D,EAAE,IAAIA,EAAE,KAAKqB,MAAM,CAAC,GAC3C,CAAC,IAAIJ,OAAO,CAACW,OAAO,IAAI,EAAE,CAAC,EAAEP,MAAM;UACzC,CAAC;QACH;QACA,OAAOJ,OAAO;MAChB,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4C,mBAAmB,GAAIN,SAAS,IAAK;IACzC,IAAIxF,iBAAiB,KAAK,IAAI,EAAE;IAEhC,MAAMgB,KAAK,GAAGc,uBAAuB,CAAC9B,iBAAiB,CAAC;IACxD,MAAM+B,OAAO,GAAG,CAAAf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,GAAG,MAAIhB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,EAAE;IAEvC1B,WAAW,CAAC8E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACtD,OAAO,GAAG,CAACsD,IAAI,CAACtD,OAAO,CAAC,IAAI,EAAE,EAAE8D,MAAM,CAAC3C,OAAO,IAC7CA,OAAO,CAACjB,EAAE,KAAKuD,SAAS,IAAItC,OAAO,CAAClB,GAAG,KAAKwD,SAC9C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMO,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMhF,KAAK,GAAGc,uBAAuB,CAACkE,KAAK,CAAC;IAC5C/F,oBAAoB,CAAC+F,KAAK,CAAC;IAC3B3F,aAAa,CAAC,IAAI,CAAC;IACnBM,mBAAmB,CAAC,KAAK,CAAC;IAC1BF,aAAa,CAAC,EAAE,CAAC;IAEjB,MAAMsB,OAAO,GAAGf,KAAK,CAACgB,GAAG,IAAIhB,KAAK,CAACiB,EAAE;;IAErC;IACA,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,uBAAsBvC,OAAQ,EAAC,EAAE;QACxHwC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAAS3F,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D;MACF,CAAC,CAAC;MAEF,IAAIoF,QAAQ,CAACgB,EAAE,EAAE;QACf,MAAMe,YAAY,GAAG,MAAM/B,QAAQ,CAACkB,IAAI,CAAC,CAAC;QAC1C9G,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0H,YAAY,CAAC;QAExD,IAAIA,YAAY,CAACC,OAAO,IAAID,YAAY,CAACX,IAAI,IAAIW,YAAY,CAACX,IAAI,CAAChF,QAAQ,EAAE;UAC3EC,WAAW,CAAC8E,IAAI,KAAK;YACnB,GAAGA,IAAI;YACP,CAACtD,OAAO,GAAGkE,YAAY,CAACX,IAAI,CAAChF;UAC/B,CAAC,CAAC,CAAC;QACL;MACF,CAAC,MAAM;QACLhC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAAC;IAClD;;IAEA;IACA,IAAI8B,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEmF,QAAQ,KAAKnF,KAAK,CAACmF,QAAQ,CAACP,QAAQ,CAAC,eAAe,CAAC,IAAI5E,KAAK,CAACmF,QAAQ,CAACP,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACA5E,KAAK,CAACoF,cAAc,GAAGpF,KAAK,CAACmF,QAAQ;MACvC,CAAC,CAAC,OAAOjH,KAAK,EAAE;QACdZ,OAAO,CAAC+H,IAAI,CAAC,8CAA8C,CAAC;QAC5DrF,KAAK,CAACoF,cAAc,GAAGpF,KAAK,CAACmF,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGjJ,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFiC,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MAEdjB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI2F,QAAQ,GAAG,IAAI;MACnB,IAAI/E,MAAM,GAAG,EAAE;;MAEf;MACA,IAAI;QAAA,IAAAoH,SAAA,EAAAC,UAAA;QACFlI,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD2F,QAAQ,GAAG,MAAMrG,YAAY,CAAC,CAAC;QAC/BS,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE2F,QAAQ,CAAC;QAE/C,IAAI,CAAAqC,SAAA,GAAArC,QAAQ,cAAAqC,SAAA,eAARA,SAAA,CAAUL,OAAO,KAAAM,UAAA,GAAItC,QAAQ,cAAAsC,UAAA,eAARA,UAAA,CAAUlB,IAAI,EAAE;UACvCnG,MAAM,GAAG+E,QAAQ,CAACoB,IAAI;UACtBhH,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEY,MAAM,CAACsH,MAAM,CAAC;;UAE7E;UACA,IAAItH,MAAM,CAACsH,MAAM,GAAG,CAAC,EAAE;YACrBnI,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC;YACnDb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,MAAM,CAACC,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3Db,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;YACzCD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC+B,SAAS,CAAC;YAClD5C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACgC,YAAY,CAAC;YACxD7C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACiC,KAAK,CAAC;YAC1C9C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACuH,QAAQ,CAAC;YAChDpI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACkC,MAAM,CAAC;YAC5C/C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACwH,SAAS,CAAC;YAClDrI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACmC,KAAK,CAAC;YAC1ChD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACyH,UAAU,CAAC;YACpDtI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC0H,YAAY,CAAC;YACxDvI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC2H,cAAc,CAAC;YAC5DxI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC4H,KAAK,CAAC;YAC1CzI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC6H,OAAO,CAAC;;YAE9C;YACA1I,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtDY,MAAM,CAAC8H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAClG,KAAK,EAAEgF,KAAK,KAAK;cAC3C1H,OAAO,CAACC,GAAG,CAAE,SAAQyH,KAAK,GAAG,CAAE,KAAIhF,KAAK,CAACC,KAAM,IAAG,EAAE;gBAClDC,SAAS,EAAEF,KAAK,CAACE,SAAS;gBAC1BC,YAAY,EAAEH,KAAK,CAACG,YAAY;gBAChCC,KAAK,EAAEJ,KAAK,CAACI,KAAK;gBAClBsF,QAAQ,EAAE1F,KAAK,CAAC0F,QAAQ;gBACxBrF,MAAM,EAAEL,KAAK,CAACK,MAAM;gBACpB0F,KAAK,EAAE/F,KAAK,CAAC+F,KAAK;gBAClBC,OAAO,EAAEhG,KAAK,CAACgG;cACjB,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC,OAAO9H,KAAK,EAAE;QACdZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEW,KAAK,CAACiI,OAAO,CAAC;MACtD;;MAEA;MACA,IAAIhI,MAAM,CAACsH,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAW,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFjJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,MAAMiJ,OAAO,GAAG;YACd1C,KAAK,EAAEpF,aAAa;YACpB+H,IAAI,EAAE;UACR,CAAC;UAEDvD,QAAQ,GAAG,MAAMtG,gBAAgB,CAAC4J,OAAO,CAAC;UAC1ClJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE2F,QAAQ,CAAC;UAEnD,IAAI,CAAAkD,UAAA,GAAAlD,QAAQ,cAAAkD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAU9B,IAAI,cAAA+B,eAAA,eAAdA,eAAA,CAAgBnB,OAAO,KAAAoB,UAAA,GAAIpD,QAAQ,cAAAoD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUhC,IAAI,cAAAiC,eAAA,eAAdA,eAAA,CAAgBjC,IAAI,EAAE;YACnDnG,MAAM,GAAG+E,QAAQ,CAACoB,IAAI,CAACA,IAAI;YAC3BhH,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEY,MAAM,CAACsH,MAAM,CAAC;UACnF;QACF,CAAC,CAAC,OAAOvH,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAACiI,OAAO,CAAC;QAC1D;MACF;;MAEA;MACA,IAAIhI,MAAM,CAACsH,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAiB,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFvJ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D2F,QAAQ,GAAG,MAAMtG,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACrCU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE2F,QAAQ,CAAC;UAEhE,IAAI,CAAAwD,UAAA,GAAAxD,QAAQ,cAAAwD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUpC,IAAI,cAAAqC,eAAA,eAAdA,eAAA,CAAgBzB,OAAO,KAAA0B,UAAA,GAAI1D,QAAQ,cAAA0D,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUtC,IAAI,cAAAuC,eAAA,eAAdA,eAAA,CAAgBvC,IAAI,EAAE;YACnD;YACA,MAAMwC,OAAO,GAAG5D,QAAQ,CAACoB,IAAI,CAACA,IAAI;YAClCnG,MAAM,GAAG2I,OAAO,CAACjC,MAAM,CAACkC,IAAI;cAAA,IAAAC,WAAA;cAAA,OAC1BD,IAAI,CAACN,IAAI,KAAK,OAAO,IACrBM,IAAI,CAAC5B,QAAQ,IACb4B,IAAI,CAACE,OAAO,MAAAD,WAAA,GACZD,IAAI,CAAC9G,KAAK,cAAA+G,WAAA,uBAAVA,WAAA,CAAYE,WAAW,CAAC,CAAC,CAACtC,QAAQ,CAAC,OAAO,CAAC;YAAA,CAC7C,CAAC;YACDtH,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,MAAM,CAACsH,MAAM,CAAC;UAC1E;QACF,CAAC,CAAC,OAAOvH,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEW,KAAK,CAACiI,OAAO,CAAC;QACvE;MACF;;MAEA;MACA,IAAIhI,MAAM,CAACsH,MAAM,GAAG,CAAC,EAAE;QACrB,MAAM0B,QAAQ,GAAGhJ,MAAM,CAAC0G,MAAM,CAAC7E,KAAK,IAAI;UACtC,MAAMoH,YAAY,GAAG1I,aAAa,KAAK,KAAK,IACxBsB,KAAK,CAAC8D,KAAK,KAAKpF,aAAa,IAC7B,CAACsB,KAAK,CAAC8D,KAAK,CAAC,CAAC;;UAElC,MAAMuD,YAAY,GAAGzI,aAAa,KAAK,KAAK,IACxBoB,KAAK,CAACiE,SAAS,KAAKrF,aAAa,IACjCoB,KAAK,CAACgE,KAAK,KAAKpF,aAAa,IAC7B,CAACoB,KAAK,CAACiE,SAAS,CAAC,CAAC;;UAEtC,MAAMqD,cAAc,GAAGxI,eAAe,KAAK,KAAK,IAC1BkB,KAAK,CAACW,OAAO,KAAK7B,eAAe,IACjC,CAACkB,KAAK,CAACW,OAAO,CAAC,CAAC;;UAEtC,OAAOyG,YAAY,IAAIC,YAAY,IAAIC,cAAc;QACvD,CAAC,CAAC;QAEFlJ,SAAS,CAAC+I,QAAQ,CAAC;QACnB7J,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE4J,QAAQ,CAAC1B,MAAM,EAAE,QAAQ,CAAC;QAErE,IAAI0B,QAAQ,CAAC1B,MAAM,KAAK,CAAC,EAAE;UACzBlH,QAAQ,CAAC,wEAAwE,CAAC;QACpF;MACF,CAAC,MAAM;QACL;QACAjB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAElCgB,QAAQ,CAAC,yFAAyF,CAAC;QACnGH,SAAS,CAAC,EAAE,CAAC;MACf;IAEF,CAAC,CAAC,OAAOmJ,GAAG,EAAE;MACZjK,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEqJ,GAAG,CAAC;MACvDhJ,QAAQ,CAAC,0DAA0D,CAAC;MACpEH,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACI,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAMgC,uBAAuB,GAAG1E,OAAO,CAAC,MAAM;IAC5C,IAAI+K,QAAQ,GAAGhJ,MAAM,CAAC0G,MAAM,CAAC7E,KAAK,IAAI;MAAA,IAAAwH,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAACnJ,UAAU,MAAAgJ,YAAA,GAC/BxH,KAAK,CAACC,KAAK,cAAAuH,YAAA,uBAAXA,YAAA,CAAaN,WAAW,CAAC,CAAC,CAACtC,QAAQ,CAACpG,UAAU,CAAC0I,WAAW,CAAC,CAAC,CAAC,OAAAO,cAAA,GAC7DzH,KAAK,CAACW,OAAO,cAAA8G,cAAA,uBAAbA,cAAA,CAAeP,WAAW,CAAC,CAAC,CAACtC,QAAQ,CAACpG,UAAU,CAAC0I,WAAW,CAAC,CAAC,CAAC,OAAAQ,YAAA,GAC/D1H,KAAK,CAAC4H,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaR,WAAW,CAAC,CAAC,CAACtC,QAAQ,CAACpG,UAAU,CAAC0I,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAME,YAAY,GAAG1I,aAAa,KAAK,KAAK,IAAIsB,KAAK,CAAC8D,KAAK,KAAKpF,aAAa;MAC7E,MAAM2I,YAAY,GAAGzI,aAAa,KAAK,KAAK,IAAIoB,KAAK,CAACiE,SAAS,KAAKrF,aAAa,IAAIoB,KAAK,CAACgE,KAAK,KAAKpF,aAAa;MAClH,MAAM0I,cAAc,GAAGxI,eAAe,KAAK,KAAK,IAAIkB,KAAK,CAACW,OAAO,KAAK7B,eAAe;MAErF,OAAO6I,aAAa,IAAIP,YAAY,IAAIC,YAAY,IAAIC,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOH,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI1G,IAAI,CAAC0G,CAAC,CAACpF,SAAS,CAAC,GAAG,IAAItB,IAAI,CAACyG,CAAC,CAACnF,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAACxE,MAAM,EAAEK,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACA3C,SAAS,CAAC,MAAM;IACdmJ,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACAnJ,SAAS,CAAC,MAAM;IACdmJ,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC5G,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAEwG,WAAW,CAAC,CAAC;EAEhE,oBACEvI,OAAA;IAAKkH,SAAS,EAAC,yBAAyB;IAAA+D,QAAA,gBACtCjL,OAAA;MAAKkH,SAAS,EAAC,sBAAsB;MAAA+D,QAAA,gBACnCjL,OAAA;QAAAiL,QAAA,EAAKlI,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAAmI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DrL,OAAA;QAAAiL,QAAA,EAAIlI,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAAmI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGNrL,OAAA;MAAKkH,SAAS,EAAC,gBAAgB;MAAA+D,QAAA,gBAC7BjL,OAAA;QAAKkH,SAAS,EAAC,gBAAgB;QAAA+D,QAAA,eAC7BjL,OAAA;UACE0J,IAAI,EAAC,MAAM;UACX4B,WAAW,EAAEvI,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClEwI,KAAK,EAAE9J,UAAW;UAClB+J,QAAQ,EAAGtK,CAAC,IAAKQ,aAAa,CAACR,CAAC,CAACuK,MAAM,CAACF,KAAK,CAAE;UAC/CrE,SAAS,EAAC;QAAc;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENrL,OAAA;QAAKkH,SAAS,EAAC,gBAAgB;QAAA+D,QAAA,gBAC7BjL,OAAA;UACEuL,KAAK,EAAE5J,aAAc;UACrB6J,QAAQ,EAAGtK,CAAC,IAAKU,gBAAgB,CAACV,CAAC,CAACuK,MAAM,CAACF,KAAK,CAAE;UAClDrE,SAAS,EAAC,eAAe;UAAA+D,QAAA,gBAEzBjL,OAAA;YAAQuL,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAElI,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrErL,OAAA;YAAQuL,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAElI,WAAW,GAAG,WAAW,GAAG;UAAW;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5ErL,OAAA;YAAQuL,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAElI,WAAW,GAAG,KAAK,GAAG;UAAU;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAETrL,OAAA;UACEuL,KAAK,EAAE1J,aAAc;UACrB2J,QAAQ,EAAGtK,CAAC,IAAKY,gBAAgB,CAACZ,CAAC,CAACuK,MAAM,CAACF,KAAK,CAAE;UAClDrE,SAAS,EAAC,eAAe;UAAA+D,QAAA,eAEzBjL,OAAA;YAAQuL,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAElI,WAAW,GAAG,eAAe,GAAG;UAAa;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAETrL,OAAA;UACEuL,KAAK,EAAExJ,eAAgB;UACvByJ,QAAQ,EAAGtK,CAAC,IAAKc,kBAAkB,CAACd,CAAC,CAACuK,MAAM,CAACF,KAAK,CAAE;UACpDrE,SAAS,EAAC,eAAe;UAAA+D,QAAA,eAEzBjL,OAAA;YAAQuL,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAElI,WAAW,GAAG,aAAa,GAAG;UAAc;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrL,OAAA;MAAKkH,SAAS,EAAC,eAAe;MAAA+D,QAAA,EAC3B3J,OAAO,gBACNtB,OAAA;QAAKkH,SAAS,EAAC,eAAe;QAAA+D,QAAA,gBAC5BjL,OAAA;UAAKkH,SAAS,EAAC;QAAiB;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCrL,OAAA;UAAAiL,QAAA,EAAIlI,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJlK,KAAK,gBACPnB,OAAA;QAAKkH,SAAS,EAAC,aAAa;QAAA+D,QAAA,gBAC1BjL,OAAA,CAACL,eAAe;UAACuH,SAAS,EAAC;QAAY;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CrL,OAAA;UAAAiL,QAAA,EAAKlI,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7ErL,OAAA;UAAAiL,QAAA,EAAI9J;QAAK;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdrL,OAAA;UAAQ0L,OAAO,EAAEnD,WAAY;UAACrB,SAAS,EAAC,WAAW;UAAA+D,QAAA,EAChDlI,WAAW,GAAG,aAAa,GAAG;QAAW;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJtH,uBAAuB,CAAC2E,MAAM,GAAG,CAAC,gBACpC1I,OAAA;QAAKkH,SAAS,EAAC,aAAa;QAAA+D,QAAA,EACzBlH,uBAAuB,CAAC2D,GAAG,CAAC,CAACzE,KAAK,EAAEgF,KAAK;UAAA,IAAA0D,IAAA,EAAAC,WAAA;UAAA,oBACxC5L,OAAA;YAAiBkH,SAAS,EAAC,YAAY;YAAA+D,QAAA,EACpChJ,iBAAiB,KAAKgG,KAAK;YAAA;YAC1B;YACAjI,OAAA;cAAKkH,SAAS,EAAC,qBAAqB;cAAA+D,QAAA,eAClCjL,OAAA;gBAAKkH,SAAS,EAAC,sBAAsB;gBAAA+D,QAAA,gBACnCjL,OAAA;kBAAKkH,SAAS,EAAC,sBAAsB;kBAAA+D,QAAA,EAClChI,KAAK,CAACmF,QAAQ,gBACbpI,OAAA;oBACE6L,GAAG,EAAGA,GAAG,IAAKzJ,WAAW,CAACyJ,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,MAAM;oBACb7I,MAAM,EAAEN,eAAe,CAACC,KAAK,CAAE;oBAC/BmJ,KAAK,EAAE;sBACLF,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdE,eAAe,EAAE,MAAM;sBACvBC,SAAS,EAAE;oBACb,CAAE;oBACFC,OAAO,EAAGrL,CAAC,IAAKoB,aAAa,CAAE,yBAAwBW,KAAK,CAACC,KAAM,EAAC,CAAE;oBACtEsJ,SAAS,EAAEA,CAAA,KAAMlK,aAAa,CAAC,IAAI,CAAE;oBACrCmK,WAAW,EAAC,WAAW;oBAAAxB,QAAA,gBAEvBjL,OAAA;sBAAQ0M,GAAG,EAAEzJ,KAAK,CAACoF,cAAc,IAAIpF,KAAK,CAACmF,QAAS;sBAACsB,IAAI,EAAC;oBAAW;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvEpI,KAAK,CAAC0J,SAAS,IAAI1J,KAAK,CAAC0J,SAAS,CAACjE,MAAM,GAAG,CAAC,IAAIzF,KAAK,CAAC0J,SAAS,CAACjF,GAAG,CAAC,CAACkF,QAAQ,EAAE3E,KAAK,kBACpFjI,OAAA;sBAEE6M,IAAI,EAAC,WAAW;sBAChBH,GAAG,EAAEE,QAAQ,CAACE,GAAI;sBAClBC,OAAO,EAAEH,QAAQ,CAACI,QAAS;sBAC3BC,KAAK,EAAEL,QAAQ,CAACM,YAAa;sBAC7BC,OAAO,EAAEP,QAAQ,CAACQ,SAAS,IAAInF,KAAK,KAAK;oBAAE,GALrC,GAAE2E,QAAQ,CAACI,QAAS,IAAG/E,KAAM,EAAC;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,GACNpI,KAAK,CAACiH,OAAO,gBACflK,OAAA;oBACE0M,GAAG,EAAG,iCAAgCzJ,KAAK,CAACiH,OAAQ,mBAAmB;oBACvEhH,KAAK,EAAED,KAAK,CAACC,KAAM;oBACnBmK,WAAW,EAAC,GAAG;oBACfC,eAAe;oBACflB,KAAK,EAAE;sBAAEF,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,MAAM;sBAAEoB,MAAM,EAAE;oBAAO;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,gBAEVrL,OAAA;oBAAKkH,SAAS,EAAC,aAAa;oBAAA+D,QAAA,gBAC1BjL,OAAA;sBAAKkH,SAAS,EAAC,YAAY;sBAAA+D,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpCrL,OAAA;sBAAAiL,QAAA,EAAI;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1BrL,OAAA;sBAAAiL,QAAA,EAAI5I,UAAU,IAAI;oBAA4C;sBAAA6I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENrL,OAAA;kBAAKkH,SAAS,EAAC,oBAAoB;kBAAA+D,QAAA,gBACjCjL,OAAA;oBAAIkH,SAAS,EAAC,qBAAqB;oBAAA+D,QAAA,EAAEhI,KAAK,CAACC;kBAAK;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtDrL,OAAA;oBAAKkH,SAAS,EAAC,oBAAoB;oBAAA+D,QAAA,gBACjCjL,OAAA;sBAAAiL,QAAA,EAAOtH,cAAc,CAACV,KAAK,CAACW,OAAO;oBAAC;sBAAAsH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5CrL,OAAA;sBAAAiL,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACdrL,OAAA;sBAAAiL,QAAA,GAAM,QAAM,EAAChI,KAAK,CAACiE,SAAS,IAAIjE,KAAK,CAACgE,KAAK;oBAAA;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAClDpI,KAAK,CAAC8D,KAAK,iBACV/G,OAAA,CAAAE,SAAA;sBAAA+K,QAAA,gBACEjL,OAAA;wBAAAiL,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACdrL,OAAA;wBAAAiL,QAAA,EAAOhI,KAAK,CAAC8D;sBAAK;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAC1B,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNrL,OAAA;oBAAKkH,SAAS,EAAC,uBAAuB;oBAAA+D,QAAA,gBACpCjL,OAAA;sBACEkH,SAAS,EAAG,sBAAqBvE,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;sBACpE+I,OAAO,EAAEA,CAAA,KAAM9I,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;sBAAAsI,QAAA,gBAEtDjL,OAAA;wBAAAiL,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACfrL,OAAA;wBAAAiL,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACTrL,OAAA;sBAAQkH,SAAS,EAAC,oBAAoB;sBAAA+D,QAAA,gBACpCjL,OAAA;wBAAAiL,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACfrL,OAAA;wBAAAiL,QAAA,EAAM;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACTrL,OAAA;sBACEkH,SAAS,EAAC,oBAAoB;sBAC9BwE,OAAO,EAAEA,CAAA,KAAMxJ,oBAAoB,CAAC,IAAI,CAAE;sBAAA+I,QAAA,gBAE1CjL,OAAA;wBAAAiL,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACdrL,OAAA;wBAAAiL,QAAA,EAAM;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL1I,gBAAgB,iBACf3C,OAAA;kBAAKkH,SAAS,EAAC,0BAA0B;kBAAA+D,QAAA,gBACvCjL,OAAA;oBAAKkH,SAAS,EAAC,yBAAyB;oBAAA+D,QAAA,eACtCjL,OAAA;sBAAAiL,QAAA,GAAOnH,uBAAuB,CAAC,CAAC,CAAC4E,MAAM,EAAC,WAAS;oBAAA;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eAGNrL,OAAA;oBAAKkH,SAAS,EAAC,uBAAuB;oBAAA+D,QAAA,gBACpCjL,OAAA;sBAAKkH,SAAS,EAAC,wBAAwB;sBAAA+D,QAAA,GAAAU,IAAA,GACnC,CAAAtL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,IAAI,MAAIxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,SAAS,MAAIzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,QAAQ,KAAI,SAAS,cAAA4G,IAAA,wBAAAC,WAAA,GAA7DD,IAAA,CAAgE/E,MAAM,CAAC,CAAC,CAAC,cAAAgF,WAAA,uBAAzEA,WAAA,CAA2E/E,WAAW,CAAC;oBAAC;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF,CAAC,eACNrL,OAAA;sBAAKoM,KAAK,EAAE;wBAAEoB,IAAI,EAAE;sBAAE,CAAE;sBAAAvC,QAAA,gBACtBjL,OAAA;wBACEkH,SAAS,EAAC,6BAA6B;wBACvCqE,KAAK,EAAE9I,UAAW;wBAClB+I,QAAQ,EAAGtK,CAAC,IAAKwB,aAAa,CAACxB,CAAC,CAACuK,MAAM,CAACF,KAAK,CAAE;wBAC/CD,WAAW,EAAC,kBAAkB;wBAC9BmC,IAAI,EAAC,GAAG;wBACRrB,KAAK,EAAE;0BACLsB,SAAS,EAAE,MAAM;0BACjBC,MAAM,EAAE,MAAM;0BACdC,QAAQ,EAAE;wBACZ,CAAE;wBACFC,OAAO,EAAG3M,CAAC,IAAK;0BACdA,CAAC,CAACuK,MAAM,CAACW,KAAK,CAACD,MAAM,GAAG,MAAM;0BAC9BjL,CAAC,CAACuK,MAAM,CAACW,KAAK,CAACD,MAAM,GAAGjL,CAAC,CAACuK,MAAM,CAACqC,YAAY,GAAG,IAAI;wBACtD;sBAAE;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACD5I,UAAU,CAACiB,IAAI,CAAC,CAAC,iBAChB1D,OAAA;wBAAKkH,SAAS,EAAC,yBAAyB;wBAAA+D,QAAA,gBACtCjL,OAAA;0BACEkH,SAAS,EAAC,4BAA4B;0BACtCwE,OAAO,EAAEA,CAAA,KAAMhJ,aAAa,CAAC,EAAE,CAAE;0BAAAuI,QAAA,EAClC;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTrL,OAAA;0BACEkH,SAAS,EAAC,4BAA4B;0BACtCwE,OAAO,EAAE/G,gBAAiB;0BAC1BoJ,QAAQ,EAAE,CAACtL,UAAU,CAACiB,IAAI,CAAC,CAAE;0BAAAuH,QAAA,EAC9B;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrL,OAAA;oBAAKkH,SAAS,EAAC,uBAAuB;oBAAA+D,QAAA,EACnCnH,uBAAuB,CAAC,CAAC,CAAC4E,MAAM,KAAK,CAAC,gBACrC1I,OAAA;sBAAKoM,KAAK,EAAE;wBAAE4B,SAAS,EAAE,QAAQ;wBAAEC,OAAO,EAAE,QAAQ;wBAAEC,KAAK,EAAE;sBAAU,CAAE;sBAAAjD,QAAA,gBACvEjL,OAAA;wBAAKoM,KAAK,EAAE;0BAAE+B,QAAQ,EAAE,MAAM;0BAAEC,YAAY,EAAE;wBAAO,CAAE;wBAAAnD,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChErL,OAAA;wBAAAiL,QAAA,EAAG;sBAAqD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,GAENvH,uBAAuB,CAAC,CAAC,CAAC4D,GAAG,CAAEvC,OAAO;sBAAA,IAAAkJ,eAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,iBAAA;sBAAA,oBACpCxO,OAAA;wBAAqCkH,SAAS,EAAC,iBAAiB;wBAAA+D,QAAA,gBAC9DjL,OAAA;0BAAKkH,SAAS,EAAC,wBAAwB;0BAAA+D,QAAA,EACpC9F,OAAO,CAACc,MAAM,MAAAoI,eAAA,GAAIlJ,OAAO,CAACG,MAAM,cAAA+I,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBzH,MAAM,CAAC,CAAC,CAAC,cAAA0H,qBAAA,uBAAzBA,qBAAA,CAA2BzH,WAAW,CAAC,CAAC,KAAI;wBAAG;0BAAAqE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D,CAAC,eACNrL,OAAA;0BAAKkH,SAAS,EAAC,yBAAyB;0BAAA+D,QAAA,gBACtCjL,OAAA;4BAAKkH,SAAS,EAAC,wBAAwB;4BAAA+D,QAAA,gBACrCjL,OAAA;8BAAMkH,SAAS,EAAC,wBAAwB;8BAAA+D,QAAA,EAAE9F,OAAO,CAACG;4BAAM;8BAAA4F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,EAC/D,CAAClG,OAAO,CAACK,QAAQ,KAAK,OAAO,IAAIL,OAAO,CAACO,OAAO,kBAC/C1F,OAAA,CAACJ,UAAU;8BAACwM,KAAK,EAAE;gCAAE8B,KAAK,EAAE,SAAS;gCAAEC,QAAQ,EAAE,MAAM;gCAAEM,UAAU,EAAE;8BAAM,CAAE;8BAACvL,KAAK,EAAC;4BAAgB;8BAAAgI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CACvG,eACDrL,OAAA;8BAAMkH,SAAS,EAAC,sBAAsB;8BAAA+D,QAAA,EACnC9G,aAAa,CAACgB,OAAO,CAACS,SAAS,IAAIT,OAAO,CAACf,SAAS;4BAAC;8BAAA8G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACNrL,OAAA;4BAAKkH,SAAS,EAAC,sBAAsB;4BAAA+D,QAAA,EAClC9F,OAAO,CAACE;0BAAI;4BAAA6F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACNrL,OAAA;4BAAKkH,SAAS,EAAC,yBAAyB;4BAAA+D,QAAA,gBACtCjL,OAAA;8BACE0L,OAAO,EAAEA,CAAA,KAAMlE,iBAAiB,CAACrC,OAAO,CAAClB,GAAG,IAAIkB,OAAO,CAACjB,EAAE,CAAE;8BAC5DgD,SAAS,EAAG,0BAAyB,CAAAqH,iBAAA,GAAApJ,OAAO,CAACW,OAAO,cAAAyI,iBAAA,eAAfA,iBAAA,CAAiB1G,QAAQ,CAACxH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;8BAAAgH,QAAA,gBAE3FjL,OAAA;gCAAAiL,QAAA,EAAO,CAAAuD,iBAAA,GAAArJ,OAAO,CAACW,OAAO,cAAA0I,iBAAA,eAAfA,iBAAA,CAAiB3G,QAAQ,CAACxH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,GAAG,CAAC,GAAG,IAAI,GAAG;8BAAI;gCAAAiH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EAChElG,OAAO,CAACU,KAAK,GAAG,CAAC,iBAAI7F,OAAA;gCAAAiL,QAAA,EAAO9F,OAAO,CAACU;8BAAK;gCAAAqF,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5C,CAAC,eACTrL,OAAA;8BAAQkH,SAAS,EAAC,wBAAwB;8BAAA+D,QAAA,eACxCjL,OAAA;gCAAAiL,QAAA,EAAM;8BAAE;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACTrL,OAAA;8BAAQkH,SAAS,EAAC,wBAAwB;8BAAA+D,QAAA,EAAC;4BAE3C;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,EACRlG,OAAO,CAAC9E,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,GAAG,kBACzBjE,OAAA,CAAAE,SAAA;8BAAA+K,QAAA,gBACEjL,OAAA;gCAAQkH,SAAS,EAAC,wBAAwB;gCAAA+D,QAAA,EAAC;8BAE3C;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACTrL,OAAA;gCACEkH,SAAS,EAAC,wBAAwB;gCAClCwE,OAAO,EAAEA,CAAA,KAAM;kCACb,IAAIgD,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;oCACnE5G,mBAAmB,CAAC5C,OAAO,CAAClB,GAAG,IAAIkB,OAAO,CAACjB,EAAE,CAAC;kCAChD;gCACF,CAAE;gCAAA+G,QAAA,EACH;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA,eACT,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA,GAjDElG,OAAO,CAAClB,GAAG,IAAIkB,OAAO,CAACjB,EAAE;wBAAAgH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAkD9B,CAAC;oBAAA,CACP;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACArL,OAAA;cAAKkH,SAAS,EAAC,YAAY;cAACwE,OAAO,EAAEA,CAAA,KAAM1D,eAAe,CAACC,KAAK,CAAE;cAAAgD,QAAA,gBAChEjL,OAAA;gBAAKkH,SAAS,EAAC,sBAAsB;gBAAA+D,QAAA,gBACnCjL,OAAA;kBACE0M,GAAG,EAAE1J,eAAe,CAACC,KAAK,CAAE;kBAC5B2L,GAAG,EAAE3L,KAAK,CAACC,KAAM;kBACjBgE,SAAS,EAAC,iBAAiB;kBAC3B5F,OAAO,EAAC,MAAM;kBACdiL,OAAO,EAAGrL,CAAC,IAAK;oBACd,IAAI+B,KAAK,CAACiH,OAAO,IAAI,CAACjH,KAAK,CAACiH,OAAO,CAACrC,QAAQ,CAAC,eAAe,CAAC,EAAE;sBAC7D,IAAI7D,OAAO,GAAGf,KAAK,CAACiH,OAAO;sBAC3B,MAAM2E,KAAK,GAAG7K,OAAO,CAAC6K,KAAK,CAAC,oDAAoD,CAAC;sBACjF7K,OAAO,GAAG6K,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG7K,OAAO;sBACpC,MAAM8K,SAAS,GAAG,CACf,8BAA6B9K,OAAQ,oBAAmB,EACxD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,4cAA4c,CAC7c;sBACD,MAAM+K,UAAU,GAAG7N,CAAC,CAACuK,MAAM,CAACiB,GAAG;sBAC/B,MAAMsC,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACnC,GAAG,IAAIiC,UAAU,CAAClH,QAAQ,CAACiF,GAAG,CAACoC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxG,IAAIF,YAAY,GAAGF,SAAS,CAACpG,MAAM,GAAG,CAAC,EAAE;wBACvCxH,CAAC,CAACuK,MAAM,CAACiB,GAAG,GAAGoC,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;sBAC5C;oBACF,CAAC,MAAM;sBACL9N,CAAC,CAACuK,MAAM,CAACiB,GAAG,GAAG,4cAA4c;oBAC7d;kBACF;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFrL,OAAA;kBAAKkH,SAAS,EAAC,cAAc;kBAAA+D,QAAA,eAC3BjL,OAAA,CAACR,YAAY;oBAAC0H,SAAS,EAAC;kBAAW;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNrL,OAAA;kBAAKkH,SAAS,EAAC,gBAAgB;kBAAA+D,QAAA,EAC5BhI,KAAK,CAACmM,QAAQ,IAAI;gBAAO;kBAAAlE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EACLpI,KAAK,CAAC0J,SAAS,IAAI1J,KAAK,CAAC0J,SAAS,CAACjE,MAAM,GAAG,CAAC,iBAC5C1I,OAAA;kBAAKkH,SAAS,EAAC,gBAAgB;kBAAA+D,QAAA,gBAC7BjL,OAAA,CAACN,YAAY;oBAAAwL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,MAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENrL,OAAA;gBAAKkH,SAAS,EAAC,oBAAoB;gBAAA+D,QAAA,gBACjCjL,OAAA;kBAAIkH,SAAS,EAAC,aAAa;kBAAA+D,QAAA,EAAEhI,KAAK,CAACC;gBAAK;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CrL,OAAA;kBAAKkH,SAAS,EAAC,YAAY;kBAAA+D,QAAA,gBACzBjL,OAAA;oBAAMkH,SAAS,EAAC,eAAe;oBAAA+D,QAAA,EAAEtH,cAAc,CAACV,KAAK,CAACW,OAAO;kBAAC;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtErL,OAAA;oBAAMkH,SAAS,EAAC,aAAa;oBAAA+D,QAAA,EAC1BtJ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAACiE,SAAS,IAAIjE,KAAK,CAACgE,KAAM,EAAC,GAAI,SAAQhE,KAAK,CAACiE,SAAS,IAAIjE,KAAK,CAACgE,KAAM,EAAC,GACvG,QAAOhE,KAAK,CAACiE,SAAS,IAAIjE,KAAK,CAACgE,KAAM;kBAAC;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNrL,OAAA;kBAAKkH,SAAS,EAAC,YAAY;kBAAA+D,QAAA,GACxBhI,KAAK,CAAC4H,KAAK,iBAAI7K,OAAA;oBAAMkH,SAAS,EAAC,WAAW;oBAAA+D,QAAA,EAAEhI,KAAK,CAAC4H;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/DpI,KAAK,CAACoM,eAAe,IAAIpM,KAAK,CAACoM,eAAe,MAAMpM,KAAK,CAACiE,SAAS,IAAIjE,KAAK,CAACgE,KAAK,CAAC,iBAClFjH,OAAA;oBAAMkH,SAAS,EAAC,YAAY;oBAAA+D,QAAA,GACzBlI,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDpB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAACoM,eAAgB,EAAC,GAAI,SAAQpM,KAAK,CAACoM,eAAgB,EAAC,GACrF,QAAOpM,KAAK,CAACoM,eAAgB,EAAC;kBAAA;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GAnROpD,KAAK;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoRV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENrL,OAAA;QAAKkH,SAAS,EAAC,aAAa;QAAA+D,QAAA,gBAC1BjL,OAAA,CAACP,eAAe;UAACyH,SAAS,EAAC;QAAY;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CrL,OAAA;UAAAiL,QAAA,EAAKlI,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1ErL,OAAA;UAAAiL,QAAA,EAAIlI,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJrL,OAAA;UAAGkH,SAAS,EAAC,YAAY;UAAA+D,QAAA,EAAElI,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjL,EAAA,CA91BID,YAAY;EAAA,QAEHZ,WAAW;AAAA;AAAA+P,EAAA,GAFpBnP,YAAY;AAg2BlB,eAAeA,YAAY;AAAC,IAAAmP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}