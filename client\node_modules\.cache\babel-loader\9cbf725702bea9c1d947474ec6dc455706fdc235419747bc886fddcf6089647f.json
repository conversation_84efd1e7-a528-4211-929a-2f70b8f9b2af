{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo, useRef } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { getVideoComments, addVideoComment, addCommentReply, likeComment, deleteVideoComment } from \"../../../apicalls/videoComments\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { message } from \"antd\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { MdVerified } from 'react-icons/md';\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst IconComponents = {\n  FaPlayCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\u25B6\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 23\n  }, this),\n  FaGraduationCap: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83C\\uDF93\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 26\n  }, this),\n  FaTimes: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 18\n  }, this),\n  FaExpand: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 19\n  }, this),\n  FaCompress: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 21\n  }, this),\n  TbVideo: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83D\\uDCF9\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 18\n  }, this),\n  TbInfoCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u2139\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 23\n  }, this),\n  TbAlertTriangle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u26A0\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 26\n  }, this),\n  TbFilter: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 19\n  }, this),\n  TbSortAscending: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2191\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 26\n  }, this),\n  TbSearch: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 19\n  }, this),\n  TbX: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 14\n  }, this),\n  TbDownload: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u21BB\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 21\n  }, this)\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\nfunction VideoLessons() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili,\n    getClassName,\n    getSubjectName\n  } = useLanguage();\n  const dispatch = useDispatch();\n\n  // State management with localStorage persistence\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState((user === null || user === void 0 ? void 0 : user.level) || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(() => {\n    // Restore from localStorage or use user's class as default\n    return localStorage.getItem('video-lessons-selected-class') || (user === null || user === void 0 ? void 0 : user.class) || \"all\";\n  });\n  const [selectedSubject, setSelectedSubject] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-selected-subject') || \"all\";\n  });\n  const [searchTerm, setSearchTerm] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-search-term') || \"\";\n  });\n  const [sortBy, setSortBy] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-sort-by') || \"newest\";\n  });\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [videosPerPage, setVideosPerPage] = useState(12);\n  const [totalVideos, setTotalVideos] = useState(0);\n\n  // Comments state - store comments per video\n  const [videoComments, setVideoComments] = useState({});\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [editingComment, setEditingComment] = useState(null);\n  const [editCommentText, setEditCommentText] = useState(\"\");\n\n  // Get comments for current video\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const currentVideo = paginatedVideos[currentVideoIndex];\n    if (!currentVideo) return [];\n\n    // Try both id and _id fields\n    const videoId = currentVideo.id || currentVideo._id;\n    return videoComments[videoId] || [];\n  };\n\n  // Set comments for current video\n  const setCurrentVideoComments = comments => {\n    if (currentVideoIndex === null) return;\n    const currentVideo = paginatedVideos[currentVideoIndex];\n    if (!currentVideo) return;\n\n    // Use the same videoId logic as getCurrentVideoComments\n    const videoId = currentVideo.id || currentVideo._id;\n    setVideoComments(prev => ({\n      ...prev,\n      [videoId]: comments\n    }));\n  };\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n      const filters = {\n        level: selectedLevel,\n        className: \"all\",\n        // Get all classes for the level\n        subject: \"all\",\n        // Get all subjects for the level\n        content: \"videos\"\n      };\n      const response = await getStudyMaterial(filters);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        const videoData = response.data.data || [];\n        setVideos(videoData);\n\n        // Load comments for all videos\n        await loadAllVideoComments(videoData);\n      } else {\n        var _response$data2;\n        setError((response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video => {\n        var _video$title, _video$subject, _video$topic;\n        return ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchLower)) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchLower)) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    // Update total count\n    setTotalVideos(sorted.length);\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Paginated videos\n  const paginatedVideos = useMemo(() => {\n    const startIndex = (currentPage - 1) * videosPerPage;\n    const endIndex = startIndex + videosPerPage;\n    return filteredAndSortedVideos.slice(startIndex, endIndex);\n  }, [filteredAndSortedVideos, currentPage, videosPerPage]);\n\n  // Pagination calculations\n  const totalPages = Math.ceil(totalVideos / videosPerPage);\n  const startItem = (currentPage - 1) * videosPerPage + 1;\n  const endItem = Math.min(currentPage * videosPerPage, totalVideos);\n\n  // Pagination handlers\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    setCurrentVideoIndex(null); // Close any open video when changing pages\n  };\n\n  const handlePageSizeChange = newSize => {\n    setVideosPerPage(newSize);\n    setCurrentPage(1); // Reset to first page when changing page size\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = paginatedVideos[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Load comments for this video if not already loaded\n    const videoId = (video === null || video === void 0 ? void 0 : video.id) || (video === null || video === void 0 ? void 0 : video._id);\n    if (videoId && !videoComments[videoId]) {\n      loadVideoComments(videoId);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = video => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Handle filter changes with localStorage persistence\n  const handleClassChange = value => {\n    setSelectedClass(value);\n    localStorage.setItem('video-lessons-selected-class', value);\n  };\n  const handleSubjectChange = value => {\n    setSelectedSubject(value);\n    localStorage.setItem('video-lessons-selected-subject', value);\n  };\n  const handleSearchChange = value => {\n    setSearchTerm(value);\n    localStorage.setItem('video-lessons-search-term', value);\n  };\n  const handleSortChange = value => {\n    setSortBy(value);\n    localStorage.setItem('video-lessons-sort-by', value);\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      setSelectedLevel(user.level);\n    }\n    // Only set user's class as default if no saved preference exists\n    if (user !== null && user !== void 0 && user.class && !localStorage.getItem('video-lessons-selected-class')) {\n      handleClassChange(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    handleSearchChange(\"\");\n  };\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n  const handleClearAll = () => {\n    handleSearchChange(\"\");\n    handleSubjectChange(\"all\");\n    handleClassChange(\"all\");\n    handleSortChange(\"newest\");\n    fetchVideos();\n  };\n\n  // Load comments for all videos\n  const loadAllVideoComments = async videoList => {\n    try {\n      console.log('📹 Loading comments for all videos:', videoList.length);\n      const commentsMap = {};\n\n      // Load comments for each video\n      for (const video of videoList) {\n        const videoId = video.id || video._id;\n        if (videoId) {\n          try {\n            const response = await getVideoComments(videoId);\n            if (response.success) {\n              commentsMap[videoId] = response.data.comments;\n              console.log(`📝 Loaded ${response.data.comments.length} comments for video ${videoId}`);\n            }\n          } catch (error) {\n            console.error(`Error loading comments for video ${videoId}:`, error);\n          }\n        }\n      }\n      setVideoComments(commentsMap);\n      console.log('✅ All video comments loaded:', commentsMap);\n    } catch (error) {\n      console.error(\"Error loading all video comments:\", error);\n    }\n  };\n\n  // Load comments for current video\n  const loadVideoComments = async videoId => {\n    try {\n      const response = await getVideoComments(videoId);\n      if (response.success) {\n        setVideoComments(prev => ({\n          ...prev,\n          [videoId]: response.data.comments\n        }));\n      }\n    } catch (error) {\n      console.error(\"Error loading comments:\", error);\n    }\n  };\n\n  // Comment functions\n  const handleAddComment = async () => {\n    if (newComment.trim()) {\n      const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n      if (!currentVideo) return;\n      try {\n        console.log('📹 Current video object:', currentVideo);\n        console.log('📹 Video keys:', Object.keys(currentVideo || {}));\n        console.log('📹 Video id field:', currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo.id);\n        console.log('📹 Video _id field:', currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo._id);\n\n        // Use _id if id doesn't exist\n        const videoId = currentVideo.id || currentVideo._id;\n        const commentData = {\n          videoId: videoId,\n          text: newComment.trim()\n        };\n        console.log('📝 Sending video comment:', commentData);\n        console.log('📝 Comment data keys:', Object.keys(commentData));\n        console.log('📝 videoId value:', videoId, '(type:', typeof videoId, ')');\n        console.log('📝 text value:', newComment.trim(), '(type:', typeof newComment.trim(), ')');\n        const response = await addVideoComment(commentData);\n        if (response.success) {\n          // Add comment to local state immediately for better UX\n          const comment = {\n            _id: response.data._id,\n            text: response.data.text,\n            author: response.data.author,\n            avatar: response.data.avatar,\n            createdAt: response.data.createdAt,\n            replies: [],\n            likes: 0,\n            likedBy: []\n          };\n          const currentComments = getCurrentVideoComments();\n          setCurrentVideoComments([comment, ...currentComments]);\n          setNewComment(\"\");\n          message.success(\"Comment added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add comment\");\n        }\n      } catch (error) {\n        console.error(\"Error adding comment:\", error);\n        message.error(\"Failed to add comment\");\n      }\n    }\n  };\n  const handleAddReply = async commentId => {\n    if (replyText.trim()) {\n      try {\n        const response = await addCommentReply(commentId, {\n          text: replyText.trim()\n        });\n        if (response.success) {\n          // Update local state with the new reply\n          const currentComments = getCurrentVideoComments();\n          const updatedComments = currentComments.map(comment => comment._id === commentId || comment.id === commentId ? {\n            ...comment,\n            replies: response.data.replies\n          } : comment);\n          setCurrentVideoComments(updatedComments);\n          setReplyText(\"\");\n          setReplyingTo(null);\n          message.success(\"Reply added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add reply\");\n        }\n      } catch (error) {\n        console.error(\"Error adding reply:\", error);\n        message.error(\"Failed to add reply\");\n      }\n    }\n  };\n  const handleLikeComment = async (commentId, isReply = false, replyId = null) => {\n    try {\n      const response = await likeComment(commentId, {\n        isReply,\n        replyId\n      });\n      if (response.success) {\n        // Update local state with the updated comment\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.map(comment => comment._id === commentId || comment.id === commentId ? response.data : comment);\n        setCurrentVideoComments(updatedComments);\n      } else {\n        message.error(response.message || \"Failed to update like\");\n      }\n    } catch (error) {\n      console.error(\"Error updating like:\", error);\n      message.error(\"Failed to update like\");\n    }\n  };\n  const handleDeleteComment = async commentId => {\n    try {\n      const response = await deleteVideoComment(commentId);\n      if (response.success) {\n        // Remove comment from local state\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.filter(comment => comment._id !== commentId && comment.id !== commentId);\n        setCurrentVideoComments(updatedComments);\n        message.success(\"Comment deleted successfully!\");\n      } else {\n        message.error(response.message || \"Failed to delete comment\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting comment:\", error);\n      message.error(\"Failed to delete comment\");\n    }\n  };\n  const handleEditComment = comment => {\n    setEditingComment(comment._id || comment.id);\n    setEditCommentText(comment.text);\n  };\n  const handleSaveEditComment = async () => {\n    if (!editCommentText.trim()) {\n      message.error(\"Comment cannot be empty\");\n      return;\n    }\n    try {\n      // TODO: Add API call to update comment\n      // const response = await updateVideoComment(editingComment, { text: editCommentText.trim() });\n\n      // For now, update local state\n      const currentComments = getCurrentVideoComments();\n      const updatedComments = currentComments.map(comment => {\n        if ((comment._id || comment.id) === editingComment) {\n          return {\n            ...comment,\n            text: editCommentText.trim()\n          };\n        }\n        return comment;\n      });\n      setCurrentVideoComments(updatedComments);\n      setEditingComment(null);\n      setEditCommentText(\"\");\n      message.success(\"Comment updated successfully!\");\n    } catch (error) {\n      console.error(\"Error updating comment:\", error);\n      message.error(\"Failed to update comment\");\n    }\n  };\n  const handleCancelEdit = () => {\n    setEditingComment(null);\n    setEditCommentText(\"\");\n  };\n  const formatTimeAgo = timestamp => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n\n  // Render loading state\n  const renderLoadingState = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 633,\n    columnNumber: 5\n  }, this);\n\n  // Render error state\n  const renderErrorState = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-state\",\n    children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n      className: \"error-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: fetchVideos,\n      className: \"retry-btn\",\n      children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 641,\n    columnNumber: 5\n  }, this);\n\n  // Render empty state\n  const renderEmptyState = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n      className: \"empty-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"suggestion\",\n      children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 653,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-main\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-icon\",\n            children: /*#__PURE__*/_jsxDEV(TbVideo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Video Lessons\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Watch educational videos to enhance your learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"level-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-level\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-label\",\n              children: \"Level:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-value\",\n              children: selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-class\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-label\",\n              children: \"Your Class:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-value\",\n              children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'secondary' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'advance' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : 'Not Set'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedClass,\n              onChange: e => handleClassChange(e.target.value),\n              className: \"control-select class-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this), availableClasses.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cls,\n                children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${cls}` : `Class ${cls}` : `Form ${cls}`\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this), \"Subject\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => handleSubjectChange(e.target.value),\n              className: \"control-select subject-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this), availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSortAscending, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this), \"Sort\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => handleSortChange(e.target.value),\n              className: \"control-select sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"oldest\",\n                children: \"Oldest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Title A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subject\",\n                children: \"Subject A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos by title, subject, or topic...\",\n              value: searchTerm,\n              onChange: e => handleSearchChange(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 15\n            }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearSearch,\n              className: \"clear-search-btn\",\n              children: [/*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 19\n              }, this), \"Clear Search\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: \"refresh-btn\",\n            children: [/*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this), \"Refresh All\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 697,\n        columnNumber: 9\n      }, this), loading && renderLoadingState(), !loading && error && renderErrorState(), !loading && !error && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"videos-grid\",\n          children: paginatedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: paginatedVideos.map((video, index) => {\n              var _user$name, _user$name$charAt;\n              return /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-card\",\n                    onClick: () => handleShowVideo(index),\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"video-card-thumbnail\",\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: getThumbnailUrl(video),\n                        alt: video.title,\n                        className: \"thumbnail-image\",\n                        loading: \"lazy\",\n                        onError: e => {\n                          // Fallback logic for failed thumbnails\n                          if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                            // For YouTube videos, try different quality thumbnails\n                            let videoId = video.videoID;\n                            if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                              const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                              videoId = match ? match[1] : videoId;\n                            }\n                            const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                            const currentSrc = e.target.src;\n                            const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                            if (currentIndex < fallbacks.length - 1) {\n                              e.target.src = fallbacks[currentIndex + 1];\n                            }\n                          } else {\n                            e.target.src = '/api/placeholder/320/180';\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 800,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"play-overlay\",\n                        children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                          className: \"play-icon\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 834,\n                          columnNumber: 21\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 833,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"video-duration\",\n                        children: video.duration || \"Video\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 836,\n                        columnNumber: 19\n                      }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"subtitle-badge\",\n                        children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 841,\n                          columnNumber: 23\n                        }, this), \"CC\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 840,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 799,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"video-card-content\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"video-title\",\n                        children: video.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"video-meta\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"video-subject\",\n                          children: getSubjectName(video.subject)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 850,\n                          columnNumber: 21\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"video-class\",\n                          children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 851,\n                          columnNumber: 21\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 849,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"video-tags\",\n                        children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"topic-tag\",\n                          children: video.topic\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 858,\n                          columnNumber: 37\n                        }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"shared-tag\",\n                          children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 860,\n                          columnNumber: 23\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 857,\n                        columnNumber: 19\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 847,\n                      columnNumber: 17\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 17\n                  }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"inline-video-player\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-style-layout\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-video-player\",\n                        children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            position: 'relative',\n                            width: '100%',\n                            height: '100%'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                            ref: ref => setVideoRef(ref),\n                            controls: true,\n                            autoPlay: true,\n                            playsInline: true,\n                            preload: \"metadata\",\n                            width: \"100%\",\n                            height: \"100%\",\n                            poster: getThumbnailUrl(video),\n                            style: {\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            },\n                            onError: e => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            },\n                            onCanPlay: () => {\n                              setVideoError(null);\n                            },\n                            crossOrigin: \"anonymous\",\n                            children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                              src: video.signedVideoUrl || video.videoUrl,\n                              type: \"video/mp4\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 901,\n                              columnNumber: 31\n                            }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                              kind: \"subtitles\",\n                              src: subtitle.url,\n                              srcLang: subtitle.language,\n                              label: subtitle.languageName,\n                              default: subtitle.isDefault || index === 0\n                            }, `${subtitle.language}-${index}`, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 903,\n                              columnNumber: 33\n                            }, this)), \"Your browser does not support the video tag.\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 878,\n                            columnNumber: 29\n                          }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"subtitle-indicator\",\n                            children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {\n                              className: \"subtitle-icon\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 917,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: [\"Subtitles available in \", video.subtitles.length, \" language(s)\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 918,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 916,\n                            columnNumber: 31\n                          }, this), videoError && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"video-error-overlay\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"error-content\",\n                              children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                                className: \"error-icon\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 925,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                children: videoError\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 926,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                onClick: () => setVideoError(null),\n                                className: \"dismiss-error-btn\",\n                                children: \"Dismiss\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 927,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 924,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 923,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 877,\n                          columnNumber: 27\n                        }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                          src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                          title: video.title,\n                          frameBorder: \"0\",\n                          allowFullScreen: true,\n                          style: {\n                            width: '100%',\n                            height: '100%',\n                            border: 'none'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 935,\n                          columnNumber: 27\n                        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"video-error\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"error-icon\",\n                            children: \"\\u26A0\\uFE0F\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 944,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                            children: \"Video Unavailable\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 945,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: videoError || \"This video cannot be played at the moment.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 946,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 943,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 875,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-video-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                          className: \"youtube-video-title\",\n                          children: video.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 953,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-video-meta\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: video.subject\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 955,\n                            columnNumber: 27\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"\\u2022\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 956,\n                            columnNumber: 27\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [\"Class \", video.className]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 957,\n                            columnNumber: 27\n                          }, this), video.level && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"\\u2022\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 960,\n                              columnNumber: 31\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: video.level\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 961,\n                              columnNumber: 31\n                            }, this)]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 954,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-video-actions\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: `youtube-action-btn ${commentsExpanded ? 'active' : ''}`,\n                            onClick: () => setCommentsExpanded(!commentsExpanded),\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"\\uD83D\\uDCAC\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 970,\n                              columnNumber: 29\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Comments\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 971,\n                              columnNumber: 29\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 966,\n                            columnNumber: 27\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"youtube-action-btn\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"\\uD83D\\uDC4D\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 974,\n                              columnNumber: 29\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Like\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 975,\n                              columnNumber: 29\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 973,\n                            columnNumber: 27\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"youtube-action-btn\",\n                            onClick: () => setCurrentVideoIndex(null),\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"\\u2715\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 981,\n                              columnNumber: 29\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Close\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 982,\n                              columnNumber: 29\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 977,\n                            columnNumber: 27\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 965,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 952,\n                        columnNumber: 23\n                      }, this), commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comments-section\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comments-header\",\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [getCurrentVideoComments().length, \" Comments\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 991,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 990,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-input\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-avatar\",\n                            children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || \"A\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 996,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              flex: 1\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                              className: \"youtube-comment-input-field\",\n                              value: newComment,\n                              onChange: e => setNewComment(e.target.value),\n                              placeholder: \"Add a comment...\",\n                              rows: \"1\",\n                              style: {\n                                minHeight: '20px',\n                                resize: 'none',\n                                overflow: 'hidden'\n                              },\n                              onInput: e => {\n                                e.target.style.height = 'auto';\n                                e.target.style.height = e.target.scrollHeight + 'px';\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1000,\n                              columnNumber: 31\n                            }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"youtube-comment-actions\",\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-btn cancel\",\n                                onClick: () => setNewComment(''),\n                                children: \"Cancel\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1018,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-btn submit\",\n                                onClick: handleAddComment,\n                                disabled: !newComment.trim(),\n                                children: \"Comment\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1024,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1017,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 999,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 995,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comments-list\",\n                          children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              textAlign: 'center',\n                              padding: '40px 0',\n                              color: '#606060'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                fontSize: '48px',\n                                marginBottom: '16px'\n                              },\n                              children: \"\\uD83D\\uDCAC\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1040,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              children: \"No comments yet. Be the first to share your thoughts!\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1041,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1039,\n                            columnNumber: 31\n                          }, this) : getCurrentVideoComments().map(comment => {\n                            var _comment$author, _comment$author$charA, _comment$likedBy, _comment$likedBy2;\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"youtube-comment\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"youtube-comment-avatar\",\n                                children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1046,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"youtube-comment-content\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"youtube-comment-header\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"youtube-comment-author\",\n                                    children: comment.author\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1051,\n                                    columnNumber: 39\n                                  }, this), (comment.userRole === 'admin' || comment.isAdmin) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                                    style: {\n                                      color: '#1d9bf0',\n                                      fontSize: '12px',\n                                      marginLeft: '4px'\n                                    },\n                                    title: \"Verified Admin\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1053,\n                                    columnNumber: 41\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"youtube-comment-time\",\n                                    children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1055,\n                                    columnNumber: 39\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1050,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"youtube-comment-text\",\n                                  children: comment.text\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1059,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"youtube-comment-actions\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                    onClick: () => handleLikeComment(comment._id || comment.id),\n                                    className: `youtube-comment-action ${(_comment$likedBy = comment.likedBy) !== null && _comment$likedBy !== void 0 && _comment$likedBy.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                      children: (_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1067,\n                                      columnNumber: 41\n                                    }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                      children: comment.likes\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1068,\n                                      columnNumber: 63\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1063,\n                                    columnNumber: 39\n                                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                    className: \"youtube-comment-action\",\n                                    children: \"Reply\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1070,\n                                    columnNumber: 39\n                                  }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                      className: \"youtube-comment-action\",\n                                      children: \"Edit\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1075,\n                                      columnNumber: 43\n                                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                      className: \"youtube-comment-action\",\n                                      onClick: () => {\n                                        if (window.confirm('Are you sure you want to delete this comment?')) {\n                                          handleDeleteComment(comment._id || comment.id);\n                                        }\n                                      },\n                                      children: \"Delete\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1078,\n                                      columnNumber: 43\n                                    }, this)]\n                                  }, void 0, true)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1062,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1049,\n                                columnNumber: 35\n                              }, this)]\n                            }, comment._id || comment.id, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1045,\n                              columnNumber: 33\n                            }, this);\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1037,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 989,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"empty-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1109,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"suggestion\",\n              children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1106,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 13\n        }, this), totalVideos > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination-info\",\n            children: [\"Showing \", startItem, \"-\", endItem, \" of \", totalVideos, \" videos\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1118,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pagination-btn\",\n              onClick: () => handlePageChange(currentPage - 1),\n              disabled: currentPage === 1,\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1123,\n              columnNumber: 11\n            }, this), Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `pagination-btn ${currentPage === pageNum ? 'active' : ''}`,\n                onClick: () => handlePageChange(pageNum),\n                children: pageNum\n              }, pageNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1144,\n                columnNumber: 15\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pagination-btn\",\n              onClick: () => handlePageChange(currentPage + 1),\n              disabled: currentPage === totalPages,\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1122,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-size-selector\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Videos per page:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1164,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: videosPerPage,\n              onChange: e => handlePageSizeChange(Number(e.target.value)),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: 6,\n                children: \"6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1169,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 12,\n                children: \"12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1170,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 24,\n                children: \"24\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 48,\n                children: \"48\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1172,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1165,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1163,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1117,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 662,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoLessons, \"3CwrNTWe4xtigZx2D1GaiDlCPtI=\", false, function () {\n  return [useSelector, useLanguage, useDispatch];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "useRef", "motion", "AnimatePresence", "getStudyMaterial", "getVideoComments", "addVideoComment", "addCommentReply", "likeComment", "deleteVideoComment", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "message", "primarySubjects", "primaryKiswahiliSubjects", "secondarySubjects", "advanceSubjects", "useLanguage", "MdVerified", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "IconComponents", "FaPlayCircle", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FaGraduationCap", "FaTimes", "FaExpand", "FaCompress", "TbVideo", "TbInfoCircle", "TbAlertTriangle", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbSearch", "TbX", "TbDownload", "VideoLessons", "_s", "user", "state", "t", "isKiswahili", "getClassName", "getSubjectName", "dispatch", "videos", "setVideos", "loading", "setLoading", "error", "setError", "selectedLevel", "setSelectedLevel", "level", "selectedClass", "setSelectedClass", "localStorage", "getItem", "class", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "currentPage", "setCurrentPage", "videosPerPage", "setVideosPerPage", "totalVideos", "setTotalVideos", "videoComments", "setVideoComments", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "replyText", "setReplyText", "showComments", "setShowComments", "commentsExpanded", "setCommentsExpanded", "editingComment", "setEditingComment", "editCommentText", "setEditCommentText", "getCurrentVideoComments", "currentVideo", "paginatedVideos", "videoId", "id", "_id", "setCurrentVideoComments", "comments", "prev", "availableClasses", "availableSubjects", "fetchVideos", "_response$data", "filters", "className", "subject", "content", "response", "data", "success", "videoData", "loadAllVideoComments", "_response$data2", "console", "filteredAndSortedVideos", "filtered", "filter", "video", "videoClass", "trim", "searchLower", "toLowerCase", "_video$title", "_video$subject", "_video$topic", "title", "includes", "topic", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "length", "log", "startIndex", "endIndex", "slice", "totalPages", "Math", "ceil", "startItem", "endItem", "min", "handlePageChange", "page", "handlePageSizeChange", "newSize", "handleShowVideo", "index", "loadVideoComments", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "pause", "toggleVideoExpansion", "fetch", "encodeURIComponent", "method", "headers", "ok", "Error", "status", "json", "getThumbnailUrl", "thumbnail", "videoID", "match", "handleClassChange", "value", "setItem", "handleSubjectChange", "handleSearchChange", "handleSortChange", "handleClearSearch", "handleRefresh", "handleClearAll", "videoList", "commentsMap", "handleAddComment", "Object", "keys", "commentData", "text", "comment", "author", "avatar", "replies", "likes", "<PERSON><PERSON><PERSON>", "currentComments", "handleAddReply", "commentId", "updatedComments", "map", "handleLikeComment", "isReply", "replyId", "handleDeleteComment", "handleEditComment", "handleSaveEditComment", "handleCancelEdit", "formatTimeAgo", "timestamp", "now", "time", "diffInSeconds", "floor", "toLocaleDateString", "renderLoadingState", "renderErrorState", "onClick", "renderEmptyState", "char<PERSON>t", "toUpperCase", "onChange", "e", "target", "cls", "type", "placeholder", "_user$name", "_user$name$charAt", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "position", "width", "height", "ref", "controls", "autoPlay", "playsInline", "preload", "poster", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "name", "flex", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "disabled", "textAlign", "padding", "color", "marginBottom", "_comment$author", "_comment$author$charA", "_comment$likedBy", "_comment$likedBy2", "userRole", "isAdmin", "marginLeft", "window", "confirm", "Array", "from", "_", "i", "pageNum", "Number", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo, useRef } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { getVideoComments, addVideoComment, addCommentReply, likeComment, deleteVideoComment } from \"../../../apicalls/videoComments\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { message } from \"antd\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { MdVerified } from 'react-icons/md';\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nconst IconComponents = {\n  FaPlayCircle: () => <span style={{fontSize: '24px'}}>▶️</span>,\n  FaGraduationCap: () => <span style={{fontSize: '24px'}}>🎓</span>,\n  FaTimes: () => <span style={{fontSize: '18px'}}>✕</span>,\n  FaExpand: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  FaCompress: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  TbVideo: () => <span style={{fontSize: '24px'}}>📹</span>,\n  TbInfoCircle: () => <span style={{fontSize: '16px'}}>ℹ️</span>,\n  TbAlertTriangle: () => <span style={{fontSize: '16px'}}>⚠️</span>,\n  TbFilter: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbSortAscending: () => <span style={{fontSize: '18px'}}>↑</span>,\n  TbSearch: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbX: () => <span style={{fontSize: '16px'}}>✕</span>,\n  TbDownload: () => <span style={{fontSize: '18px'}}>↻</span>\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili, getClassName, getSubjectName } = useLanguage();\n  const dispatch = useDispatch();\n\n  // State management with localStorage persistence\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(() => {\n    // Restore from localStorage or use user's class as default\n    return localStorage.getItem('video-lessons-selected-class') || user?.class || \"all\";\n  });\n  const [selectedSubject, setSelectedSubject] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-selected-subject') || \"all\";\n  });\n  const [searchTerm, setSearchTerm] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-search-term') || \"\";\n  });\n  const [sortBy, setSortBy] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-sort-by') || \"newest\";\n  });\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [videosPerPage, setVideosPerPage] = useState(12);\n  const [totalVideos, setTotalVideos] = useState(0);\n\n  // Comments state - store comments per video\n  const [videoComments, setVideoComments] = useState({});\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [editingComment, setEditingComment] = useState(null);\n  const [editCommentText, setEditCommentText] = useState(\"\");\n\n  // Get comments for current video\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const currentVideo = paginatedVideos[currentVideoIndex];\n    if (!currentVideo) return [];\n\n    // Try both id and _id fields\n    const videoId = currentVideo.id || currentVideo._id;\n    return videoComments[videoId] || [];\n  };\n\n  // Set comments for current video\n  const setCurrentVideoComments = (comments) => {\n    if (currentVideoIndex === null) return;\n    const currentVideo = paginatedVideos[currentVideoIndex];\n    if (!currentVideo) return;\n\n    // Use the same videoId logic as getCurrentVideoComments\n    const videoId = currentVideo.id || currentVideo._id;\n    setVideoComments(prev => ({\n      ...prev,\n      [videoId]: comments\n    }));\n  };\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: \"all\", // Get all classes for the level\n        subject: \"all\", // Get all subjects for the level\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response?.data?.success) {\n        const videoData = response.data.data || [];\n        setVideos(videoData);\n\n        // Load comments for all videos\n        await loadAllVideoComments(videoData);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n\n\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower) ||\n        video.topic?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    // Update total count\n    setTotalVideos(sorted.length);\n\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Paginated videos\n  const paginatedVideos = useMemo(() => {\n    const startIndex = (currentPage - 1) * videosPerPage;\n    const endIndex = startIndex + videosPerPage;\n    return filteredAndSortedVideos.slice(startIndex, endIndex);\n  }, [filteredAndSortedVideos, currentPage, videosPerPage]);\n\n  // Pagination calculations\n  const totalPages = Math.ceil(totalVideos / videosPerPage);\n  const startItem = (currentPage - 1) * videosPerPage + 1;\n  const endItem = Math.min(currentPage * videosPerPage, totalVideos);\n\n  // Pagination handlers\n  const handlePageChange = (page) => {\n    setCurrentPage(page);\n    setCurrentVideoIndex(null); // Close any open video when changing pages\n  };\n\n  const handlePageSizeChange = (newSize) => {\n    setVideosPerPage(newSize);\n    setCurrentPage(1); // Reset to first page when changing page size\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = paginatedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Load comments for this video if not already loaded\n    const videoId = video?.id || video?._id;\n    if (videoId && !videoComments[videoId]) {\n      loadVideoComments(videoId);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Handle filter changes with localStorage persistence\n  const handleClassChange = (value) => {\n    setSelectedClass(value);\n    localStorage.setItem('video-lessons-selected-class', value);\n  };\n\n  const handleSubjectChange = (value) => {\n    setSelectedSubject(value);\n    localStorage.setItem('video-lessons-selected-subject', value);\n  };\n\n  const handleSearchChange = (value) => {\n    setSearchTerm(value);\n    localStorage.setItem('video-lessons-search-term', value);\n  };\n\n  const handleSortChange = (value) => {\n    setSortBy(value);\n    localStorage.setItem('video-lessons-sort-by', value);\n  };\n\n  useEffect(() => {\n    if (user?.level) {\n      setSelectedLevel(user.level);\n    }\n    // Only set user's class as default if no saved preference exists\n    if (user?.class && !localStorage.getItem('video-lessons-selected-class')) {\n      handleClassChange(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    handleSearchChange(\"\");\n  };\n\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n\n  const handleClearAll = () => {\n    handleSearchChange(\"\");\n    handleSubjectChange(\"all\");\n    handleClassChange(\"all\");\n    handleSortChange(\"newest\");\n    fetchVideos();\n  };\n\n  // Load comments for all videos\n  const loadAllVideoComments = async (videoList) => {\n    try {\n      console.log('📹 Loading comments for all videos:', videoList.length);\n      const commentsMap = {};\n\n      // Load comments for each video\n      for (const video of videoList) {\n        const videoId = video.id || video._id;\n        if (videoId) {\n          try {\n            const response = await getVideoComments(videoId);\n            if (response.success) {\n              commentsMap[videoId] = response.data.comments;\n              console.log(`📝 Loaded ${response.data.comments.length} comments for video ${videoId}`);\n            }\n          } catch (error) {\n            console.error(`Error loading comments for video ${videoId}:`, error);\n          }\n        }\n      }\n\n      setVideoComments(commentsMap);\n      console.log('✅ All video comments loaded:', commentsMap);\n    } catch (error) {\n      console.error(\"Error loading all video comments:\", error);\n    }\n  };\n\n  // Load comments for current video\n  const loadVideoComments = async (videoId) => {\n    try {\n      const response = await getVideoComments(videoId);\n      if (response.success) {\n        setVideoComments(prev => ({\n          ...prev,\n          [videoId]: response.data.comments\n        }));\n      }\n    } catch (error) {\n      console.error(\"Error loading comments:\", error);\n    }\n  };\n\n  // Comment functions\n  const handleAddComment = async () => {\n    if (newComment.trim()) {\n      const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n      if (!currentVideo) return;\n\n      try {\n        console.log('📹 Current video object:', currentVideo);\n        console.log('📹 Video keys:', Object.keys(currentVideo || {}));\n        console.log('📹 Video id field:', currentVideo?.id);\n        console.log('📹 Video _id field:', currentVideo?._id);\n\n        // Use _id if id doesn't exist\n        const videoId = currentVideo.id || currentVideo._id;\n\n        const commentData = {\n          videoId: videoId,\n          text: newComment.trim()\n        };\n\n        console.log('📝 Sending video comment:', commentData);\n        console.log('📝 Comment data keys:', Object.keys(commentData));\n        console.log('📝 videoId value:', videoId, '(type:', typeof videoId, ')');\n        console.log('📝 text value:', newComment.trim(), '(type:', typeof newComment.trim(), ')');\n\n        const response = await addVideoComment(commentData);\n\n        if (response.success) {\n          // Add comment to local state immediately for better UX\n          const comment = {\n            _id: response.data._id,\n            text: response.data.text,\n            author: response.data.author,\n            avatar: response.data.avatar,\n            createdAt: response.data.createdAt,\n            replies: [],\n            likes: 0,\n            likedBy: []\n          };\n          const currentComments = getCurrentVideoComments();\n          setCurrentVideoComments([comment, ...currentComments]);\n          setNewComment(\"\");\n          message.success(\"Comment added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add comment\");\n        }\n      } catch (error) {\n        console.error(\"Error adding comment:\", error);\n        message.error(\"Failed to add comment\");\n      }\n    }\n  };\n\n\n\n  const handleAddReply = async (commentId) => {\n    if (replyText.trim()) {\n      try {\n        const response = await addCommentReply(commentId, {\n          text: replyText.trim()\n        });\n\n        if (response.success) {\n          // Update local state with the new reply\n          const currentComments = getCurrentVideoComments();\n          const updatedComments = currentComments.map(comment =>\n            comment._id === commentId || comment.id === commentId\n              ? { ...comment, replies: response.data.replies }\n              : comment\n          );\n          setCurrentVideoComments(updatedComments);\n          setReplyText(\"\");\n          setReplyingTo(null);\n          message.success(\"Reply added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add reply\");\n        }\n      } catch (error) {\n        console.error(\"Error adding reply:\", error);\n        message.error(\"Failed to add reply\");\n      }\n    }\n  };\n\n  const handleLikeComment = async (commentId, isReply = false, replyId = null) => {\n    try {\n      const response = await likeComment(commentId, {\n        isReply,\n        replyId\n      });\n\n      if (response.success) {\n        // Update local state with the updated comment\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.map(comment =>\n          comment._id === commentId || comment.id === commentId\n            ? response.data\n            : comment\n        );\n        setCurrentVideoComments(updatedComments);\n      } else {\n        message.error(response.message || \"Failed to update like\");\n      }\n    } catch (error) {\n      console.error(\"Error updating like:\", error);\n      message.error(\"Failed to update like\");\n    }\n  };\n\n  const handleDeleteComment = async (commentId) => {\n    try {\n      const response = await deleteVideoComment(commentId);\n\n      if (response.success) {\n        // Remove comment from local state\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.filter(comment =>\n          comment._id !== commentId && comment.id !== commentId\n        );\n        setCurrentVideoComments(updatedComments);\n        message.success(\"Comment deleted successfully!\");\n      } else {\n        message.error(response.message || \"Failed to delete comment\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting comment:\", error);\n      message.error(\"Failed to delete comment\");\n    }\n  };\n\n  const handleEditComment = (comment) => {\n    setEditingComment(comment._id || comment.id);\n    setEditCommentText(comment.text);\n  };\n\n  const handleSaveEditComment = async () => {\n    if (!editCommentText.trim()) {\n      message.error(\"Comment cannot be empty\");\n      return;\n    }\n\n    try {\n      // TODO: Add API call to update comment\n      // const response = await updateVideoComment(editingComment, { text: editCommentText.trim() });\n\n      // For now, update local state\n      const currentComments = getCurrentVideoComments();\n      const updatedComments = currentComments.map(comment => {\n        if ((comment._id || comment.id) === editingComment) {\n          return { ...comment, text: editCommentText.trim() };\n        }\n        return comment;\n      });\n      setCurrentVideoComments(updatedComments);\n\n      setEditingComment(null);\n      setEditCommentText(\"\");\n      message.success(\"Comment updated successfully!\");\n    } catch (error) {\n      console.error(\"Error updating comment:\", error);\n      message.error(\"Failed to update comment\");\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setEditingComment(null);\n    setEditCommentText(\"\");\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n\n  // Render loading state\n  const renderLoadingState = () => (\n    <div className=\"loading-state\">\n      <div className=\"loading-spinner\"></div>\n      <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n    </div>\n  );\n\n  // Render error state\n  const renderErrorState = () => (\n    <div className=\"error-state\">\n      <TbAlertTriangle className=\"error-icon\" />\n      <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n      <p>{error}</p>\n      <button onClick={fetchVideos} className=\"retry-btn\">\n        {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n      </button>\n    </div>\n  );\n\n  // Render empty state\n  const renderEmptyState = () => (\n    <div className=\"empty-state\">\n      <FaGraduationCap className=\"empty-icon\" />\n      <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n      <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n      <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n    </div>\n  );\n\n  return (\n    <div className=\"video-lessons-container\">\n      {/* Enhanced Header with Level Display */}\n      <div className=\"video-lessons-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <div className=\"header-icon\">\n              <TbVideo />\n            </div>\n            <div className=\"header-text\">\n              <h1>Video Lessons</h1>\n              <p>Watch educational videos to enhance your learning</p>\n            </div>\n          </div>\n\n          {/* Level and Class Display */}\n          <div className=\"level-display\">\n            <div className=\"current-level\">\n              <span className=\"level-label\">Level:</span>\n              <span className=\"level-value\">{selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</span>\n            </div>\n            <div className=\"current-class\">\n              <span className=\"class-label\">Your Class:</span>\n              <span className=\"class-value\">\n                {user?.level === 'primary' ? `Class ${user?.class || 'N/A'}` :\n                 user?.level === 'secondary' ? `Form ${user?.class || 'N/A'}` :\n                 user?.level === 'advance' ? `Form ${user?.class || 'N/A'}` :\n                 'Not Set'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"video-lessons-content\">\n        {/* Enhanced Filters and Controls */}\n        <div className=\"video-controls\">\n          <div className=\"controls-row\">\n            {/* Class Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                {isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class'}\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => handleClassChange(e.target.value)}\n                className=\"control-select class-select\"\n              >\n                <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                      (isKiswahili ? `Darasa la ${cls}` : `Class ${cls}`) :\n                      `Form ${cls}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => handleSubjectChange(e.target.value)}\n                className=\"control-select subject-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSortAscending />\n                Sort\n              </label>\n              <select\n                value={sortBy}\n                onChange={(e) => handleSortChange(e.target.value)}\n                className=\"control-select sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Search Row */}\n          <div className=\"search-row\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title, subject, or topic...\"\n                value={searchTerm}\n                onChange={(e) => handleSearchChange(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                  Clear Search\n                </button>\n              )}\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh All\n            </button>\n          </div>\n        </div>\n\n        {/* Main Content Area */}\n        {loading && renderLoadingState()}\n        {!loading && error && renderErrorState()}\n        {!loading && !error && (\n          <>\n            <div className=\"videos-grid\">\n              {paginatedVideos.length > 0 ? (\n                <>\n                  {paginatedVideos.map((video, index) => (\n                  <React.Fragment key={index}>\n                    <div className=\"video-item\">\n                {/* Video Card */}\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                      // Fallback logic for failed thumbnails\n                      if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                        // For YouTube videos, try different quality thumbnails\n                        let videoId = video.videoID;\n                        if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                        }\n\n                        const fallbacks = [\n                          `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                          '/api/placeholder/320/180'\n                        ];\n\n                        const currentSrc = e.target.src;\n                        const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                        if (currentIndex < fallbacks.length - 1) {\n                          e.target.src = fallbacks[currentIndex + 1];\n                        }\n                      } else {\n                        e.target.src = '/api/placeholder/320/180';\n                      }\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlayCircle className=\"play-icon\" />\n                  </div>\n                  <div className=\"video-duration\">\n                    {video.duration || \"Video\"}\n                  </div>\n                  {video.subtitles && video.subtitles.length > 0 && (\n                    <div className=\"subtitle-badge\">\n                      <TbInfoCircle />\n                      CC\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"video-card-content\">\n                  <h3 className=\"video-title\">{video.title}</h3>\n                  <div className=\"video-meta\">\n                    <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                    <span className=\"video-class\">\n                      {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                        (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`) :\n                        `Form ${video.className || video.class}`}\n                    </span>\n                  </div>\n                  <div className=\"video-tags\">\n                    {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                    {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                      <span className=\"shared-tag\">\n                        {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}{selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                          (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`) :\n                          `Form ${video.sharedFromClass}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n                </div>\n\n                {/* Inline Video Player */}\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      {/* Video Player */}\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <div style={{ position: 'relative', width: '100%', height: '100%' }}>\n                            <video\n                              ref={(ref) => setVideoRef(ref)}\n                              controls\n                              autoPlay\n                              playsInline\n                              preload=\"metadata\"\n                              width=\"100%\"\n                              height=\"100%\"\n                              poster={getThumbnailUrl(video)}\n                              style={{\n                                width: '100%',\n                                height: '100%',\n                                backgroundColor: '#000',\n                                objectFit: 'contain'\n                              }}\n                              onError={(e) => {\n                                setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                              }}\n                              onCanPlay={() => {\n                                setVideoError(null);\n                              }}\n                              crossOrigin=\"anonymous\"\n                            >\n                              <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                              {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                                <track\n                                  key={`${subtitle.language}-${index}`}\n                                  kind=\"subtitles\"\n                                  src={subtitle.url}\n                                  srcLang={subtitle.language}\n                                  label={subtitle.languageName}\n                                  default={subtitle.isDefault || index === 0}\n                                />\n                              ))}\n                              Your browser does not support the video tag.\n                            </video>\n\n                            {video.subtitles && video.subtitles.length > 0 && (\n                              <div className=\"subtitle-indicator\">\n                                <TbInfoCircle className=\"subtitle-icon\" />\n                                <span>Subtitles available in {video.subtitles.length} language(s)</span>\n                              </div>\n                            )}\n\n                            {videoError && (\n                              <div className=\"video-error-overlay\">\n                                <div className=\"error-content\">\n                                  <TbAlertTriangle className=\"error-icon\" />\n                                  <p>{videoError}</p>\n                                  <button onClick={() => setVideoError(null)} className=\"dismiss-error-btn\">\n                                    Dismiss\n                                  </button>\n                                </div>\n                              </div>\n                            )}\n                          </div>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Video Info */}\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{video.subject}</span>\n                          <span>•</span>\n                          <span>Class {video.className}</span>\n                          {video.level && (\n                            <>\n                              <span>•</span>\n                              <span>{video.level}</span>\n                            </>\n                          )}\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className={`youtube-action-btn ${commentsExpanded ? 'active' : ''}`}\n                            onClick={() => setCommentsExpanded(!commentsExpanded)}\n                          >\n                            <span>💬</span>\n                            <span>Comments</span>\n                          </button>\n                          <button className=\"youtube-action-btn\">\n                            <span>👍</span>\n                            <span>Like</span>\n                          </button>\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {commentsExpanded && (\n                        <div className=\"youtube-comments-section\">\n                          <div className=\"youtube-comments-header\">\n                            <span>{getCurrentVideoComments().length} Comments</span>\n                          </div>\n\n                          {/* Add Comment */}\n                          <div className=\"youtube-comment-input\">\n                            <div className=\"youtube-comment-avatar\">\n                              {user?.name?.charAt(0)?.toUpperCase() || \"A\"}\n                            </div>\n                            <div style={{ flex: 1 }}>\n                              <textarea\n                                className=\"youtube-comment-input-field\"\n                                value={newComment}\n                                onChange={(e) => setNewComment(e.target.value)}\n                                placeholder=\"Add a comment...\"\n                                rows=\"1\"\n                                style={{\n                                  minHeight: '20px',\n                                  resize: 'none',\n                                  overflow: 'hidden'\n                                }}\n                                onInput={(e) => {\n                                  e.target.style.height = 'auto';\n                                  e.target.style.height = e.target.scrollHeight + 'px';\n                                }}\n                              />\n                              {newComment.trim() && (\n                                <div className=\"youtube-comment-actions\">\n                                  <button\n                                    className=\"youtube-comment-btn cancel\"\n                                    onClick={() => setNewComment('')}\n                                  >\n                                    Cancel\n                                  </button>\n                                  <button\n                                    className=\"youtube-comment-btn submit\"\n                                    onClick={handleAddComment}\n                                    disabled={!newComment.trim()}\n                                  >\n                                    Comment\n                                  </button>\n                                </div>\n                              )}\n                            </div>\n                          </div>\n\n                          {/* Comments List */}\n                          <div className=\"youtube-comments-list\">\n                            {getCurrentVideoComments().length === 0 ? (\n                              <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                                <p>No comments yet. Be the first to share your thoughts!</p>\n                              </div>\n                            ) : (\n                              getCurrentVideoComments().map((comment) => (\n                                <div key={comment._id || comment.id} className=\"youtube-comment\">\n                                  <div className=\"youtube-comment-avatar\">\n                                    {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                  </div>\n                                  <div className=\"youtube-comment-content\">\n                                    <div className=\"youtube-comment-header\">\n                                      <span className=\"youtube-comment-author\">{comment.author}</span>\n                                      {(comment.userRole === 'admin' || comment.isAdmin) && (\n                                        <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title=\"Verified Admin\" />\n                                      )}\n                                      <span className=\"youtube-comment-time\">\n                                        {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                      </span>\n                                    </div>\n                                    <div className=\"youtube-comment-text\">\n                                      {comment.text}\n                                    </div>\n                                    <div className=\"youtube-comment-actions\">\n                                      <button\n                                        onClick={() => handleLikeComment(comment._id || comment.id)}\n                                        className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                      >\n                                        <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                        {comment.likes > 0 && <span>{comment.likes}</span>}\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        Reply\n                                      </button>\n                                      {comment.user === user?._id && (\n                                        <>\n                                          <button className=\"youtube-comment-action\">\n                                            Edit\n                                          </button>\n                                          <button\n                                            className=\"youtube-comment-action\"\n                                            onClick={() => {\n                                              if (window.confirm('Are you sure you want to delete this comment?')) {\n                                                handleDeleteComment(comment._id || comment.id);\n                                              }\n                                            }}\n                                          >\n                                            Delete\n                                          </button>\n                                        </>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              ))\n                            )}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </React.Fragment>\n          ))}\n        </>\n      ) : (\n        <div className=\"empty-state\">\n          <FaGraduationCap className=\"empty-icon\" />\n          <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n          <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n          <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n        </div>\n      )}\n    </div>\n\n    {/* Pagination Controls */}\n    {totalVideos > 0 && (\n      <div className=\"pagination-container\">\n        <div className=\"pagination-info\">\n          Showing {startItem}-{endItem} of {totalVideos} videos\n        </div>\n\n        <div className=\"pagination-controls\">\n          <button\n            className=\"pagination-btn\"\n            onClick={() => handlePageChange(currentPage - 1)}\n            disabled={currentPage === 1}\n          >\n            Previous\n          </button>\n\n          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n            let pageNum;\n            if (totalPages <= 5) {\n              pageNum = i + 1;\n            } else if (currentPage <= 3) {\n              pageNum = i + 1;\n            } else if (currentPage >= totalPages - 2) {\n              pageNum = totalPages - 4 + i;\n            } else {\n              pageNum = currentPage - 2 + i;\n            }\n\n            return (\n              <button\n                key={pageNum}\n                className={`pagination-btn ${currentPage === pageNum ? 'active' : ''}`}\n                onClick={() => handlePageChange(pageNum)}\n              >\n                {pageNum}\n              </button>\n            );\n          })}\n\n          <button\n            className=\"pagination-btn\"\n            onClick={() => handlePageChange(currentPage + 1)}\n            disabled={currentPage === totalPages}\n          >\n            Next\n          </button>\n        </div>\n\n        <div className=\"page-size-selector\">\n          <span>Videos per page:</span>\n          <select\n            value={videosPerPage}\n            onChange={(e) => handlePageSizeChange(Number(e.target.value))}\n          >\n            <option value={6}>6</option>\n            <option value={12}>12</option>\n            <option value={24}>24</option>\n            <option value={48}>48</option>\n          </select>\n        </div>\n      </div>\n    )}\n  </>\n)}\n</div>\n</div>\n);\n}\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAChF,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,WAAW,EAAEC,kBAAkB,QAAQ,iCAAiC;AACrI,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAC1H,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,UAAU,QAAQ,gBAAgB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,YAAY,EAAEA,CAAA,kBAAMJ,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9DC,eAAe,EAAEA,CAAA,kBAAMZ,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACjEE,OAAO,EAAEA,CAAA,kBAAMb,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACxDG,QAAQ,EAAEA,CAAA,kBAAMd,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDI,UAAU,EAAEA,CAAA,kBAAMf,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC3DK,OAAO,EAAEA,CAAA,kBAAMhB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDM,YAAY,EAAEA,CAAA,kBAAMjB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9DO,eAAe,EAAEA,CAAA,kBAAMlB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACjEQ,QAAQ,EAAEA,CAAA,kBAAMnB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DS,eAAe,EAAEA,CAAA,kBAAMpB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAChEU,QAAQ,EAAEA,CAAA,kBAAMrB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DW,GAAG,EAAEA,CAAA,kBAAMtB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACpDY,UAAU,EAAEA,CAAA,kBAAMvB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAC5D,CAAC;;AAED;AACA,MAAM;EACJP,YAAY;EACZQ,eAAe;EACfC,OAAO;EACPC,QAAQ;EACRC,UAAU;EACVC,OAAO;EACPG,QAAQ;EACRC,eAAe;EACfC,QAAQ;EACRC,GAAG;EACHC,UAAU;EACVL,eAAe;EACfD;AACF,CAAC,GAAGd,cAAc;AAElB,SAASqB,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAe,CAAC,GAAGlC,WAAW,CAAC,CAAC;EACtE,MAAMmC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC4D,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8D,KAAK,EAAEC,QAAQ,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,CAAAmD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,KAAI,SAAS,CAAC;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpE,QAAQ,CAAC,MAAM;IACvD;IACA,OAAOqE,YAAY,CAACC,OAAO,CAAC,8BAA8B,CAAC,KAAInB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,KAAI,KAAK;EACrF,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC,MAAM;IAC3D;IACA,OAAOqE,YAAY,CAACC,OAAO,CAAC,gCAAgC,CAAC,IAAI,KAAK;EACxE,CAAC,CAAC;EACF,MAAM,CAACI,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,MAAM;IACjD;IACA,OAAOqE,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,IAAI,EAAE;EAChE,CAAC,CAAC;EACF,MAAM,CAACM,MAAM,EAAEC,SAAS,CAAC,GAAG7E,QAAQ,CAAC,MAAM;IACzC;IACA,OAAOqE,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,IAAI,QAAQ;EAClE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACQ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsF,QAAQ,EAAEC,WAAW,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAACwF,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC0F,aAAa,EAAEC,gBAAgB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4F,WAAW,EAAEC,cAAc,CAAC,GAAG7F,QAAQ,CAAC,CAAC,CAAC;;EAEjD;EACA,MAAM,CAAC8F,aAAa,EAAEC,gBAAgB,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACgG,UAAU,EAAEC,aAAa,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkG,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoG,SAAS,EAAEC,YAAY,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsG,YAAY,EAAEC,eAAe,CAAC,GAAGvG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0G,cAAc,EAAEC,iBAAiB,CAAC,GAAG3G,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4G,eAAe,EAAEC,kBAAkB,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM8G,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIhC,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAMiC,YAAY,GAAGC,eAAe,CAAClC,iBAAiB,CAAC;IACvD,IAAI,CAACiC,YAAY,EAAE,OAAO,EAAE;;IAE5B;IACA,MAAME,OAAO,GAAGF,YAAY,CAACG,EAAE,IAAIH,YAAY,CAACI,GAAG;IACnD,OAAOrB,aAAa,CAACmB,OAAO,CAAC,IAAI,EAAE;EACrC,CAAC;;EAED;EACA,MAAMG,uBAAuB,GAAIC,QAAQ,IAAK;IAC5C,IAAIvC,iBAAiB,KAAK,IAAI,EAAE;IAChC,MAAMiC,YAAY,GAAGC,eAAe,CAAClC,iBAAiB,CAAC;IACvD,IAAI,CAACiC,YAAY,EAAE;;IAEnB;IACA,MAAME,OAAO,GAAGF,YAAY,CAACG,EAAE,IAAIH,YAAY,CAACI,GAAG;IACnDpB,gBAAgB,CAACuB,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACL,OAAO,GAAGI;IACb,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGpH,OAAO,CAAC,MAAM;IACrC,IAAI6D,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACpH,IAAIA,aAAa,KAAK,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9D,IAAIA,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMwD,iBAAiB,GAAGrH,OAAO,CAAC,MAAM;IACtC,IAAI6D,aAAa,KAAK,SAAS,EAAE,OAAO9C,eAAe;IACvD,IAAI8C,aAAa,KAAK,mBAAmB,EAAE,OAAO7C,wBAAwB;IAC1E,IAAI6C,aAAa,KAAK,WAAW,EAAE,OAAO5C,iBAAiB;IAC3D,IAAI4C,aAAa,KAAK,SAAS,EAAE,OAAO3C,eAAe;IACvD,OAAO,EAAE;EACX,CAAC,EAAE,CAAC2C,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMyD,WAAW,GAAGvH,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAwH,cAAA;MACF7D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdN,QAAQ,CAACzC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM2G,OAAO,GAAG;QACdzD,KAAK,EAAEF,aAAa;QACpB4D,SAAS,EAAE,KAAK;QAAE;QAClBC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMxH,gBAAgB,CAACoH,OAAO,CAAC;MAEhD,IAAII,QAAQ,aAARA,QAAQ,gBAAAL,cAAA,GAARK,QAAQ,CAAEC,IAAI,cAAAN,cAAA,eAAdA,cAAA,CAAgBO,OAAO,EAAE;QAC3B,MAAMC,SAAS,GAAGH,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE;QAC1CrE,SAAS,CAACuE,SAAS,CAAC;;QAEpB;QACA,MAAMC,oBAAoB,CAACD,SAAS,CAAC;MACvC,CAAC,MAAM;QAAA,IAAAE,eAAA;QACLrE,QAAQ,CAAC,CAAAgE,QAAQ,aAARA,QAAQ,wBAAAK,eAAA,GAARL,QAAQ,CAAEC,IAAI,cAAAI,eAAA,uBAAdA,eAAA,CAAgBnH,OAAO,KAAI,wBAAwB,CAAC;QAC7D0C,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjBJ,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACiD,aAAa,EAAEP,QAAQ,CAAC,CAAC;;EAE7B;EACA,MAAM6E,uBAAuB,GAAGnI,OAAO,CAAC,MAAM;IAG5C,IAAIoI,QAAQ,GAAG7E,MAAM;;IAErB;IACA6E,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACvE,KAAK,KAAKF,aAAa,CAAC;;IAElE;IACA,IAAIG,aAAa,KAAK,KAAK,EAAE;MAC3BoE,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAI;QAClC;QACA,MAAMC,UAAU,GAAGD,KAAK,CAACb,SAAS,IAAIa,KAAK,CAAClE,KAAK;QACjD,OAAOmE,UAAU,KAAKvE,aAAa;MACrC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIK,eAAe,KAAK,KAAK,EAAE;MAC7B+D,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACZ,OAAO,KAAKrD,eAAe,CAAC;IACxE;;IAEA;IACA,IAAIE,UAAU,CAACiE,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAGlE,UAAU,CAACmE,WAAW,CAAC,CAAC;MAC5CN,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK;QAAA,IAAAK,YAAA,EAAAC,cAAA,EAAAC,YAAA;QAAA,OAC9B,EAAAF,YAAA,GAAAL,KAAK,CAACQ,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaD,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAG,cAAA,GAChDN,KAAK,CAACZ,OAAO,cAAAkB,cAAA,uBAAbA,cAAA,CAAeF,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAI,YAAA,GAClDP,KAAK,CAACU,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaH,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC;MAAA,CAClD,CAAC;IACH;;IAEA;IACA,MAAMQ,MAAM,GAAG,CAAC,GAAGb,QAAQ,CAAC,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,QAAQ3E,MAAM;QACZ,KAAK,QAAQ;UACX,OAAO,IAAI4E,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,QAAQ;UACX,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,OAAO;UACV,OAAO,CAACH,CAAC,CAACL,KAAK,IAAI,EAAE,EAAES,aAAa,CAACH,CAAC,CAACN,KAAK,IAAI,EAAE,CAAC;QACrD,KAAK,SAAS;UACZ,OAAO,CAACK,CAAC,CAACzB,OAAO,IAAI,EAAE,EAAE6B,aAAa,CAACH,CAAC,CAAC1B,OAAO,IAAI,EAAE,CAAC;QACzD;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;;IAEF;IACAhC,cAAc,CAACuD,MAAM,CAACO,MAAM,CAAC;IAE7BtB,OAAO,CAACuB,GAAG,CAAC,0BAA0B,EAAER,MAAM,CAACO,MAAM,CAAC;IACtD,IAAIP,MAAM,CAACO,MAAM,GAAG,CAAC,EAAE;MACrBtB,OAAO,CAACuB,GAAG,CAAC,2BAA2B,EAAER,MAAM,CAAC,CAAC,CAAC,CAAC;IACrD;IAEA,OAAOA,MAAM;EACf,CAAC,EAAE,CAAC1F,MAAM,EAAEgB,UAAU,EAAEE,MAAM,EAAEZ,aAAa,EAAEG,aAAa,EAAEK,eAAe,CAAC,CAAC;;EAE/E;EACA,MAAMwC,eAAe,GAAG7G,OAAO,CAAC,MAAM;IACpC,MAAM0J,UAAU,GAAG,CAACrE,WAAW,GAAG,CAAC,IAAIE,aAAa;IACpD,MAAMoE,QAAQ,GAAGD,UAAU,GAAGnE,aAAa;IAC3C,OAAO4C,uBAAuB,CAACyB,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC5D,CAAC,EAAE,CAACxB,uBAAuB,EAAE9C,WAAW,EAAEE,aAAa,CAAC,CAAC;;EAEzD;EACA,MAAMsE,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACtE,WAAW,GAAGF,aAAa,CAAC;EACzD,MAAMyE,SAAS,GAAG,CAAC3E,WAAW,GAAG,CAAC,IAAIE,aAAa,GAAG,CAAC;EACvD,MAAM0E,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC7E,WAAW,GAAGE,aAAa,EAAEE,WAAW,CAAC;;EAElE;EACA,MAAM0E,gBAAgB,GAAIC,IAAI,IAAK;IACjC9E,cAAc,CAAC8E,IAAI,CAAC;IACpBxF,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED,MAAMyF,oBAAoB,GAAIC,OAAO,IAAK;IACxC9E,gBAAgB,CAAC8E,OAAO,CAAC;IACzBhF,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMiF,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMlC,KAAK,GAAGzB,eAAe,CAAC2D,KAAK,CAAC;IAEpC5F,oBAAoB,CAAC4F,KAAK,CAAC;IAC3B1F,mBAAmB,CAAC,CAAC0F,KAAK,CAAC,CAAC;IAC5BxF,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAM4B,OAAO,GAAG,CAAAwB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEvB,EAAE,MAAIuB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEtB,GAAG;IACvC,IAAIF,OAAO,IAAI,CAACnB,aAAa,CAACmB,OAAO,CAAC,EAAE;MACtC2D,iBAAiB,CAAC3D,OAAO,CAAC;IAC5B;;IAEA;IACA,IAAIwB,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEoC,QAAQ,KAAKpC,KAAK,CAACoC,QAAQ,CAAC3B,QAAQ,CAAC,eAAe,CAAC,IAAIT,KAAK,CAACoC,QAAQ,CAAC3B,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAM4B,SAAS,GAAG,MAAMC,iBAAiB,CAACtC,KAAK,CAACoC,QAAQ,CAAC;QACzDpC,KAAK,CAACuC,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAOhH,KAAK,EAAE;QACduE,OAAO,CAAC4C,IAAI,CAAC,8CAA8C,CAAC;QAC5DxC,KAAK,CAACuC,cAAc,GAAGvC,KAAK,CAACoC,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BjG,mBAAmB,CAAC,EAAE,CAAC;IACvBF,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC6F,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCjG,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAM6F,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAAC3B,QAAQ,CAAC,eAAe,CAAC,IAAI2B,QAAQ,CAAC3B,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAMnB,QAAQ,GAAG,MAAMsD,KAAK,CAAE,6DAA4DC,kBAAkB,CAACT,QAAQ,CAAE,EAAC,EAAE;UACxHU,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACzD,QAAQ,CAAC0D,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsB3D,QAAQ,CAAC4D,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAM3D,IAAI,GAAG,MAAMD,QAAQ,CAAC6D,IAAI,CAAC,CAAC;QAElC,IAAI5D,IAAI,CAACC,OAAO,IAAID,IAAI,CAAC8C,SAAS,EAAE;UAClCzC,OAAO,CAACuB,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAO5B,IAAI,CAAC8C,SAAS;QACvB,CAAC,MAAM;UACLzC,OAAO,CAAC4C,IAAI,CAAC,+CAA+C,EAAEjD,IAAI,CAAC;UACnE,OAAO6C,QAAQ;QACjB;MACF,CAAC,CAAC,OAAO/G,KAAK,EAAE;QACduE,OAAO,CAACvE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAO+G,QAAQ;MACjB;IACF;IAEA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAIpD,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACqD,SAAS,EAAE;MACnB,OAAOrD,KAAK,CAACqD,SAAS;IACxB;IAEA,IAAIrD,KAAK,CAACsD,OAAO,IAAI,CAACtD,KAAK,CAACsD,OAAO,CAAC7C,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAIjC,OAAO,GAAGwB,KAAK,CAACsD,OAAO;MAC3B,IAAI9E,OAAO,CAACiC,QAAQ,CAAC,aAAa,CAAC,IAAIjC,OAAO,CAACiC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAM8C,KAAK,GAAG/E,OAAO,CAAC+E,KAAK,CAAC,oDAAoD,CAAC;QACjF/E,OAAO,GAAG+E,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG/E,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IAEA,OAAO,0BAA0B;EACnC,CAAC;;EAED;EACAhH,SAAS,CAAC,MAAM;IACdwH,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMwE,iBAAiB,GAAIC,KAAK,IAAK;IACnC9H,gBAAgB,CAAC8H,KAAK,CAAC;IACvB7H,YAAY,CAAC8H,OAAO,CAAC,8BAA8B,EAAED,KAAK,CAAC;EAC7D,CAAC;EAED,MAAME,mBAAmB,GAAIF,KAAK,IAAK;IACrCzH,kBAAkB,CAACyH,KAAK,CAAC;IACzB7H,YAAY,CAAC8H,OAAO,CAAC,gCAAgC,EAAED,KAAK,CAAC;EAC/D,CAAC;EAED,MAAMG,kBAAkB,GAAIH,KAAK,IAAK;IACpCvH,aAAa,CAACuH,KAAK,CAAC;IACpB7H,YAAY,CAAC8H,OAAO,CAAC,2BAA2B,EAAED,KAAK,CAAC;EAC1D,CAAC;EAED,MAAMI,gBAAgB,GAAIJ,KAAK,IAAK;IAClCrH,SAAS,CAACqH,KAAK,CAAC;IAChB7H,YAAY,CAAC8H,OAAO,CAAC,uBAAuB,EAAED,KAAK,CAAC;EACtD,CAAC;EAEDjM,SAAS,CAAC,MAAM;IACd,IAAIkD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEe,KAAK,EAAE;MACfD,gBAAgB,CAACd,IAAI,CAACe,KAAK,CAAC;IAC9B;IACA;IACA,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoB,KAAK,IAAI,CAACF,YAAY,CAACC,OAAO,CAAC,8BAA8B,CAAC,EAAE;MACxE2H,iBAAiB,CAAC9I,IAAI,CAACoB,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE,CAACpB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMoJ,iBAAiB,GAAGA,CAAA,KAAM;IAC9BF,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA/E,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMgF,cAAc,GAAGA,CAAA,KAAM;IAC3BJ,kBAAkB,CAAC,EAAE,CAAC;IACtBD,mBAAmB,CAAC,KAAK,CAAC;IAC1BH,iBAAiB,CAAC,KAAK,CAAC;IACxBK,gBAAgB,CAAC,QAAQ,CAAC;IAC1B7E,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMU,oBAAoB,GAAG,MAAOuE,SAAS,IAAK;IAChD,IAAI;MACFrE,OAAO,CAACuB,GAAG,CAAC,qCAAqC,EAAE8C,SAAS,CAAC/C,MAAM,CAAC;MACpE,MAAMgD,WAAW,GAAG,CAAC,CAAC;;MAEtB;MACA,KAAK,MAAMlE,KAAK,IAAIiE,SAAS,EAAE;QAC7B,MAAMzF,OAAO,GAAGwB,KAAK,CAACvB,EAAE,IAAIuB,KAAK,CAACtB,GAAG;QACrC,IAAIF,OAAO,EAAE;UACX,IAAI;YACF,MAAMc,QAAQ,GAAG,MAAMvH,gBAAgB,CAACyG,OAAO,CAAC;YAChD,IAAIc,QAAQ,CAACE,OAAO,EAAE;cACpB0E,WAAW,CAAC1F,OAAO,CAAC,GAAGc,QAAQ,CAACC,IAAI,CAACX,QAAQ;cAC7CgB,OAAO,CAACuB,GAAG,CAAE,aAAY7B,QAAQ,CAACC,IAAI,CAACX,QAAQ,CAACsC,MAAO,uBAAsB1C,OAAQ,EAAC,CAAC;YACzF;UACF,CAAC,CAAC,OAAOnD,KAAK,EAAE;YACduE,OAAO,CAACvE,KAAK,CAAE,oCAAmCmD,OAAQ,GAAE,EAAEnD,KAAK,CAAC;UACtE;QACF;MACF;MAEAiC,gBAAgB,CAAC4G,WAAW,CAAC;MAC7BtE,OAAO,CAACuB,GAAG,CAAC,8BAA8B,EAAE+C,WAAW,CAAC;IAC1D,CAAC,CAAC,OAAO7I,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;;EAED;EACA,MAAM8G,iBAAiB,GAAG,MAAO3D,OAAO,IAAK;IAC3C,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMvH,gBAAgB,CAACyG,OAAO,CAAC;MAChD,IAAIc,QAAQ,CAACE,OAAO,EAAE;QACpBlC,gBAAgB,CAACuB,IAAI,KAAK;UACxB,GAAGA,IAAI;UACP,CAACL,OAAO,GAAGc,QAAQ,CAACC,IAAI,CAACX;QAC3B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAM8I,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI5G,UAAU,CAAC2C,IAAI,CAAC,CAAC,EAAE;MACrB,MAAM5B,YAAY,GAAGuB,uBAAuB,CAACxD,iBAAiB,CAAC;MAC/D,IAAI,CAACiC,YAAY,EAAE;MAEnB,IAAI;QACFsB,OAAO,CAACuB,GAAG,CAAC,0BAA0B,EAAE7C,YAAY,CAAC;QACrDsB,OAAO,CAACuB,GAAG,CAAC,gBAAgB,EAAEiD,MAAM,CAACC,IAAI,CAAC/F,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9DsB,OAAO,CAACuB,GAAG,CAAC,oBAAoB,EAAE7C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,EAAE,CAAC;QACnDmB,OAAO,CAACuB,GAAG,CAAC,qBAAqB,EAAE7C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEI,GAAG,CAAC;;QAErD;QACA,MAAMF,OAAO,GAAGF,YAAY,CAACG,EAAE,IAAIH,YAAY,CAACI,GAAG;QAEnD,MAAM4F,WAAW,GAAG;UAClB9F,OAAO,EAAEA,OAAO;UAChB+F,IAAI,EAAEhH,UAAU,CAAC2C,IAAI,CAAC;QACxB,CAAC;QAEDN,OAAO,CAACuB,GAAG,CAAC,2BAA2B,EAAEmD,WAAW,CAAC;QACrD1E,OAAO,CAACuB,GAAG,CAAC,uBAAuB,EAAEiD,MAAM,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC;QAC9D1E,OAAO,CAACuB,GAAG,CAAC,mBAAmB,EAAE3C,OAAO,EAAE,QAAQ,EAAE,OAAOA,OAAO,EAAE,GAAG,CAAC;QACxEoB,OAAO,CAACuB,GAAG,CAAC,gBAAgB,EAAE5D,UAAU,CAAC2C,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO3C,UAAU,CAAC2C,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;QAEzF,MAAMZ,QAAQ,GAAG,MAAMtH,eAAe,CAACsM,WAAW,CAAC;QAEnD,IAAIhF,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMgF,OAAO,GAAG;YACd9F,GAAG,EAAEY,QAAQ,CAACC,IAAI,CAACb,GAAG;YACtB6F,IAAI,EAAEjF,QAAQ,CAACC,IAAI,CAACgF,IAAI;YACxBE,MAAM,EAAEnF,QAAQ,CAACC,IAAI,CAACkF,MAAM;YAC5BC,MAAM,EAAEpF,QAAQ,CAACC,IAAI,CAACmF,MAAM;YAC5B1D,SAAS,EAAE1B,QAAQ,CAACC,IAAI,CAACyB,SAAS;YAClC2D,OAAO,EAAE,EAAE;YACXC,KAAK,EAAE,CAAC;YACRC,OAAO,EAAE;UACX,CAAC;UACD,MAAMC,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;UACjDM,uBAAuB,CAAC,CAAC6F,OAAO,EAAE,GAAGM,eAAe,CAAC,CAAC;UACtDtH,aAAa,CAAC,EAAE,CAAC;UACjBhF,OAAO,CAACgH,OAAO,CAAC,6BAA6B,CAAC;QAChD,CAAC,MAAM;UACLhH,OAAO,CAAC6C,KAAK,CAACiE,QAAQ,CAAC9G,OAAO,IAAI,uBAAuB,CAAC;QAC5D;MACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;QACduE,OAAO,CAACvE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C7C,OAAO,CAAC6C,KAAK,CAAC,uBAAuB,CAAC;MACxC;IACF;EACF,CAAC;EAID,MAAM0J,cAAc,GAAG,MAAOC,SAAS,IAAK;IAC1C,IAAIrH,SAAS,CAACuC,IAAI,CAAC,CAAC,EAAE;MACpB,IAAI;QACF,MAAMZ,QAAQ,GAAG,MAAMrH,eAAe,CAAC+M,SAAS,EAAE;UAChDT,IAAI,EAAE5G,SAAS,CAACuC,IAAI,CAAC;QACvB,CAAC,CAAC;QAEF,IAAIZ,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMsF,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;UACjD,MAAM4G,eAAe,GAAGH,eAAe,CAACI,GAAG,CAACV,OAAO,IACjDA,OAAO,CAAC9F,GAAG,KAAKsG,SAAS,IAAIR,OAAO,CAAC/F,EAAE,KAAKuG,SAAS,GACjD;YAAE,GAAGR,OAAO;YAAEG,OAAO,EAAErF,QAAQ,CAACC,IAAI,CAACoF;UAAQ,CAAC,GAC9CH,OACN,CAAC;UACD7F,uBAAuB,CAACsG,eAAe,CAAC;UACxCrH,YAAY,CAAC,EAAE,CAAC;UAChBF,aAAa,CAAC,IAAI,CAAC;UACnBlF,OAAO,CAACgH,OAAO,CAAC,2BAA2B,CAAC;QAC9C,CAAC,MAAM;UACLhH,OAAO,CAAC6C,KAAK,CAACiE,QAAQ,CAAC9G,OAAO,IAAI,qBAAqB,CAAC;QAC1D;MACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;QACduE,OAAO,CAACvE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C7C,OAAO,CAAC6C,KAAK,CAAC,qBAAqB,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAM8J,iBAAiB,GAAG,MAAAA,CAAOH,SAAS,EAAEI,OAAO,GAAG,KAAK,EAAEC,OAAO,GAAG,IAAI,KAAK;IAC9E,IAAI;MACF,MAAM/F,QAAQ,GAAG,MAAMpH,WAAW,CAAC8M,SAAS,EAAE;QAC5CI,OAAO;QACPC;MACF,CAAC,CAAC;MAEF,IAAI/F,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMsF,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;QACjD,MAAM4G,eAAe,GAAGH,eAAe,CAACI,GAAG,CAACV,OAAO,IACjDA,OAAO,CAAC9F,GAAG,KAAKsG,SAAS,IAAIR,OAAO,CAAC/F,EAAE,KAAKuG,SAAS,GACjD1F,QAAQ,CAACC,IAAI,GACbiF,OACN,CAAC;QACD7F,uBAAuB,CAACsG,eAAe,CAAC;MAC1C,CAAC,MAAM;QACLzM,OAAO,CAAC6C,KAAK,CAACiE,QAAQ,CAAC9G,OAAO,IAAI,uBAAuB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C7C,OAAO,CAAC6C,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;EAED,MAAMiK,mBAAmB,GAAG,MAAON,SAAS,IAAK;IAC/C,IAAI;MACF,MAAM1F,QAAQ,GAAG,MAAMnH,kBAAkB,CAAC6M,SAAS,CAAC;MAEpD,IAAI1F,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMsF,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;QACjD,MAAM4G,eAAe,GAAGH,eAAe,CAAC/E,MAAM,CAACyE,OAAO,IACpDA,OAAO,CAAC9F,GAAG,KAAKsG,SAAS,IAAIR,OAAO,CAAC/F,EAAE,KAAKuG,SAC9C,CAAC;QACDrG,uBAAuB,CAACsG,eAAe,CAAC;QACxCzM,OAAO,CAACgH,OAAO,CAAC,+BAA+B,CAAC;MAClD,CAAC,MAAM;QACLhH,OAAO,CAAC6C,KAAK,CAACiE,QAAQ,CAAC9G,OAAO,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7C,OAAO,CAAC6C,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMkK,iBAAiB,GAAIf,OAAO,IAAK;IACrCtG,iBAAiB,CAACsG,OAAO,CAAC9F,GAAG,IAAI8F,OAAO,CAAC/F,EAAE,CAAC;IAC5CL,kBAAkB,CAACoG,OAAO,CAACD,IAAI,CAAC;EAClC,CAAC;EAED,MAAMiB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACrH,eAAe,CAAC+B,IAAI,CAAC,CAAC,EAAE;MAC3B1H,OAAO,CAAC6C,KAAK,CAAC,yBAAyB,CAAC;MACxC;IACF;IAEA,IAAI;MACF;MACA;;MAEA;MACA,MAAMyJ,eAAe,GAAGzG,uBAAuB,CAAC,CAAC;MACjD,MAAM4G,eAAe,GAAGH,eAAe,CAACI,GAAG,CAACV,OAAO,IAAI;QACrD,IAAI,CAACA,OAAO,CAAC9F,GAAG,IAAI8F,OAAO,CAAC/F,EAAE,MAAMR,cAAc,EAAE;UAClD,OAAO;YAAE,GAAGuG,OAAO;YAAED,IAAI,EAAEpG,eAAe,CAAC+B,IAAI,CAAC;UAAE,CAAC;QACrD;QACA,OAAOsE,OAAO;MAChB,CAAC,CAAC;MACF7F,uBAAuB,CAACsG,eAAe,CAAC;MAExC/G,iBAAiB,CAAC,IAAI,CAAC;MACvBE,kBAAkB,CAAC,EAAE,CAAC;MACtB5F,OAAO,CAACgH,OAAO,CAAC,+BAA+B,CAAC;IAClD,CAAC,CAAC,OAAOnE,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7C,OAAO,CAAC6C,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMoK,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvH,iBAAiB,CAAC,IAAI,CAAC;IACvBE,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMsH,aAAa,GAAIC,SAAS,IAAK;IACnC,MAAMC,GAAG,GAAG,IAAI7E,IAAI,CAAC,CAAC;IACtB,MAAM8E,IAAI,GAAG,IAAI9E,IAAI,CAAC4E,SAAS,CAAC;IAChC,MAAMG,aAAa,GAAGtE,IAAI,CAACuE,KAAK,CAAC,CAACH,GAAG,GAAGC,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEtE,IAAI,CAACuE,KAAK,CAACD,aAAa,GAAG,EAAE,CAAE,OAAM;IACzE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEtE,IAAI,CAACuE,KAAK,CAACD,aAAa,GAAG,IAAI,CAAE,OAAM;IAC5E,IAAIA,aAAa,GAAG,MAAM,EAAE,OAAQ,GAAEtE,IAAI,CAACuE,KAAK,CAACD,aAAa,GAAG,KAAK,CAAE,OAAM;IAC9E,OAAOD,IAAI,CAACG,kBAAkB,CAAC,CAAC;EAClC,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,kBACzBjN,OAAA;IAAKmG,SAAS,EAAC,eAAe;IAAA5F,QAAA,gBAC5BP,OAAA;MAAKmG,SAAS,EAAC;IAAiB;MAAA3F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvCX,OAAA;MAAAO,QAAA,EAAIsB,WAAW,GAAG,mBAAmB,GAAG;IAAmB;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7D,CACN;;EAED;EACA,MAAMuM,gBAAgB,GAAGA,CAAA,kBACvBlN,OAAA;IAAKmG,SAAS,EAAC,aAAa;IAAA5F,QAAA,gBAC1BP,OAAA,CAACkB,eAAe;MAACiF,SAAS,EAAC;IAAY;MAAA3F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1CX,OAAA;MAAAO,QAAA,EAAKsB,WAAW,GAAG,2BAA2B,GAAG;IAAsB;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC7EX,OAAA;MAAAO,QAAA,EAAI8B;IAAK;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACdX,OAAA;MAAQmN,OAAO,EAAEnH,WAAY;MAACG,SAAS,EAAC,WAAW;MAAA5F,QAAA,EAChDsB,WAAW,GAAG,aAAa,GAAG;IAAW;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;;EAED;EACA,MAAMyM,gBAAgB,GAAGA,CAAA,kBACvBpN,OAAA;IAAKmG,SAAS,EAAC,aAAa;IAAA5F,QAAA,gBAC1BP,OAAA,CAACY,eAAe;MAACuF,SAAS,EAAC;IAAY;MAAA3F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1CX,OAAA;MAAAO,QAAA,EAAKsB,WAAW,GAAG,6BAA6B,GAAG;IAAiB;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC1EX,OAAA;MAAAO,QAAA,EAAIsB,WAAW,GAAG,kEAAkE,GAAG;IAA4D;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxJX,OAAA;MAAGmG,SAAS,EAAC,YAAY;MAAA5F,QAAA,EAAEsB,WAAW,GAAG,yCAAyC,GAAG;IAA6C;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpI,CACN;EAED,oBACEX,OAAA;IAAKmG,SAAS,EAAC,yBAAyB;IAAA5F,QAAA,gBAEtCP,OAAA;MAAKmG,SAAS,EAAC,sBAAsB;MAAA5F,QAAA,eACnCP,OAAA;QAAKmG,SAAS,EAAC,gBAAgB;QAAA5F,QAAA,gBAC7BP,OAAA;UAAKmG,SAAS,EAAC,aAAa;UAAA5F,QAAA,gBAC1BP,OAAA;YAAKmG,SAAS,EAAC,aAAa;YAAA5F,QAAA,eAC1BP,OAAA,CAACgB,OAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNX,OAAA;YAAKmG,SAAS,EAAC,aAAa;YAAA5F,QAAA,gBAC1BP,OAAA;cAAAO,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBX,OAAA;cAAAO,QAAA,EAAG;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNX,OAAA;UAAKmG,SAAS,EAAC,eAAe;UAAA5F,QAAA,gBAC5BP,OAAA;YAAKmG,SAAS,EAAC,eAAe;YAAA5F,QAAA,gBAC5BP,OAAA;cAAMmG,SAAS,EAAC,aAAa;cAAA5F,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CX,OAAA;cAAMmG,SAAS,EAAC,aAAa;cAAA5F,QAAA,EAAEgC,aAAa,CAAC8K,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG/K,aAAa,CAAC+F,KAAK,CAAC,CAAC;YAAC;cAAA9H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACNX,OAAA;YAAKmG,SAAS,EAAC,eAAe;YAAA5F,QAAA,gBAC5BP,OAAA;cAAMmG,SAAS,EAAC,aAAa;cAAA5F,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDX,OAAA;cAAMmG,SAAS,EAAC,aAAa;cAAA5F,QAAA,EAC1B,CAAAmB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,SAAS,GAAI,SAAQ,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,KAAI,KAAM,EAAC,GAC3D,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,WAAW,GAAI,QAAO,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,KAAI,KAAM,EAAC,GAC5D,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,SAAS,GAAI,QAAO,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,KAAI,KAAM,EAAC,GAC1D;YAAS;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAKmG,SAAS,EAAC,uBAAuB;MAAA5F,QAAA,gBAEpCP,OAAA;QAAKmG,SAAS,EAAC,gBAAgB;QAAA5F,QAAA,gBAC7BP,OAAA;UAAKmG,SAAS,EAAC,cAAc;UAAA5F,QAAA,gBAE3BP,OAAA;YAAKmG,SAAS,EAAC,eAAe;YAAA5F,QAAA,gBAC5BP,OAAA;cAAOmG,SAAS,EAAC,eAAe;cAAA5F,QAAA,gBAC9BP,OAAA,CAACmB,QAAQ;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACXkB,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACRX,OAAA;cACEyK,KAAK,EAAE/H,aAAc;cACrB6K,QAAQ,EAAGC,CAAC,IAAKhD,iBAAiB,CAACgD,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;cACnDtE,SAAS,EAAC,6BAA6B;cAAA5F,QAAA,gBAEvCP,OAAA;gBAAQyK,KAAK,EAAC,KAAK;gBAAAlK,QAAA,EAAEsB,WAAW,GAAG,eAAe,GAAG;cAAa;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,EAC3EmF,gBAAgB,CAACoG,GAAG,CAAEwB,GAAG,iBACxB1N,OAAA;gBAAkByK,KAAK,EAAEiD,GAAI;gBAAAnN,QAAA,EAC1BgC,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClEV,WAAW,GAAI,aAAY6L,GAAI,EAAC,GAAI,SAAQA,GAAI,EAAC,GACjD,QAAOA,GAAI;cAAC,GAHJA,GAAG;gBAAAlN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIR,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNX,OAAA;YAAKmG,SAAS,EAAC,eAAe;YAAA5F,QAAA,gBAC5BP,OAAA;cAAOmG,SAAS,EAAC,eAAe;cAAA5F,QAAA,gBAC9BP,OAAA,CAACmB,QAAQ;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEyK,KAAK,EAAE1H,eAAgB;cACvBwK,QAAQ,EAAGC,CAAC,IAAK7C,mBAAmB,CAAC6C,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;cACrDtE,SAAS,EAAC,+BAA+B;cAAA5F,QAAA,gBAEzCP,OAAA;gBAAQyK,KAAK,EAAC,KAAK;gBAAAlK,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCoF,iBAAiB,CAACmG,GAAG,CAAE9F,OAAO,iBAC7BpG,OAAA;gBAAsByK,KAAK,EAAErE,OAAQ;gBAAA7F,QAAA,EAClC6F;cAAO,GADGA,OAAO;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNX,OAAA;YAAKmG,SAAS,EAAC,eAAe;YAAA5F,QAAA,gBAC5BP,OAAA;cAAOmG,SAAS,EAAC,eAAe;cAAA5F,QAAA,gBAC9BP,OAAA,CAACoB,eAAe;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEyK,KAAK,EAAEtH,MAAO;cACdoK,QAAQ,EAAGC,CAAC,IAAK3C,gBAAgB,CAAC2C,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;cAClDtE,SAAS,EAAC,4BAA4B;cAAA5F,QAAA,gBAEtCP,OAAA;gBAAQyK,KAAK,EAAC,QAAQ;gBAAAlK,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CX,OAAA;gBAAQyK,KAAK,EAAC,QAAQ;gBAAAlK,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CX,OAAA;gBAAQyK,KAAK,EAAC,OAAO;gBAAAlK,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCX,OAAA;gBAAQyK,KAAK,EAAC,SAAS;gBAAAlK,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNX,OAAA;UAAKmG,SAAS,EAAC,YAAY;UAAA5F,QAAA,gBACzBP,OAAA;YAAKmG,SAAS,EAAC,kBAAkB;YAAA5F,QAAA,gBAC/BP,OAAA,CAACqB,QAAQ;cAAC8E,SAAS,EAAC;YAAa;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCX,OAAA;cACE2N,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DnD,KAAK,EAAExH,UAAW;cAClBsK,QAAQ,EAAGC,CAAC,IAAK5C,kBAAkB,CAAC4C,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;cACpDtE,SAAS,EAAC;YAAc;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EACDsC,UAAU,iBACTjD,OAAA;cAAQmN,OAAO,EAAErC,iBAAkB;cAAC3E,SAAS,EAAC,kBAAkB;cAAA5F,QAAA,gBAC9DP,OAAA,CAACsB,GAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENX,OAAA;YAAQmN,OAAO,EAAEpC,aAAc;YAAC5E,SAAS,EAAC,aAAa;YAAA5F,QAAA,gBACrDP,OAAA,CAACuB,UAAU;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLwB,OAAO,IAAI8K,kBAAkB,CAAC,CAAC,EAC/B,CAAC9K,OAAO,IAAIE,KAAK,IAAI6K,gBAAgB,CAAC,CAAC,EACvC,CAAC/K,OAAO,IAAI,CAACE,KAAK,iBACjBrC,OAAA,CAAAE,SAAA;QAAAK,QAAA,gBACEP,OAAA;UAAKmG,SAAS,EAAC,aAAa;UAAA5F,QAAA,EACzBgF,eAAe,CAAC2C,MAAM,GAAG,CAAC,gBACzBlI,OAAA,CAAAE,SAAA;YAAAK,QAAA,EACGgF,eAAe,CAAC2G,GAAG,CAAC,CAAClF,KAAK,EAAEkC,KAAK;cAAA,IAAA2E,UAAA,EAAAC,iBAAA;cAAA,oBAClC9N,OAAA,CAAC1B,KAAK,CAAC2B,QAAQ;gBAAAM,QAAA,eACbP,OAAA;kBAAKmG,SAAS,EAAC,YAAY;kBAAA5F,QAAA,gBAE/BP,OAAA;oBAAKmG,SAAS,EAAC,YAAY;oBAACgH,OAAO,EAAEA,CAAA,KAAMlE,eAAe,CAACC,KAAK,CAAE;oBAAA3I,QAAA,gBAChEP,OAAA;sBAAKmG,SAAS,EAAC,sBAAsB;sBAAA5F,QAAA,gBACnCP,OAAA;wBACE+N,GAAG,EAAE3D,eAAe,CAACpD,KAAK,CAAE;wBAC5BgH,GAAG,EAAEhH,KAAK,CAACQ,KAAM;wBACjBrB,SAAS,EAAC,iBAAiB;wBAC3BhE,OAAO,EAAC,MAAM;wBACd8L,OAAO,EAAGT,CAAC,IAAK;0BAChB;0BACA,IAAIxG,KAAK,CAACsD,OAAO,IAAI,CAACtD,KAAK,CAACsD,OAAO,CAAC7C,QAAQ,CAAC,eAAe,CAAC,EAAE;4BAC7D;4BACA,IAAIjC,OAAO,GAAGwB,KAAK,CAACsD,OAAO;4BAC3B,IAAI9E,OAAO,CAACiC,QAAQ,CAAC,aAAa,CAAC,IAAIjC,OAAO,CAACiC,QAAQ,CAAC,UAAU,CAAC,EAAE;8BACnE,MAAM8C,KAAK,GAAG/E,OAAO,CAAC+E,KAAK,CAAC,oDAAoD,CAAC;8BACjF/E,OAAO,GAAG+E,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG/E,OAAO;4BACtC;4BAEA,MAAM0I,SAAS,GAAG,CACf,8BAA6B1I,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;4BAED,MAAM2I,UAAU,GAAGX,CAAC,CAACC,MAAM,CAACM,GAAG;4BAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAAC1G,QAAQ,CAAC6G,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;4BAE1F,IAAIJ,YAAY,GAAGF,SAAS,CAAChG,MAAM,GAAG,CAAC,EAAE;8BACvCsF,CAAC,CAACC,MAAM,CAACM,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;4BAC5C;0BACF,CAAC,MAAM;4BACLZ,CAAC,CAACC,MAAM,CAACM,GAAG,GAAG,0BAA0B;0BAC3C;wBACF;sBAAE;wBAAAvN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFX,OAAA;wBAAKmG,SAAS,EAAC,cAAc;wBAAA5F,QAAA,eAC3BP,OAAA,CAACI,YAAY;0BAAC+F,SAAS,EAAC;wBAAW;0BAAA3F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC,eACNX,OAAA;wBAAKmG,SAAS,EAAC,gBAAgB;wBAAA5F,QAAA,EAC5ByG,KAAK,CAACyH,QAAQ,IAAI;sBAAO;wBAAAjO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,EACLqG,KAAK,CAAC0H,SAAS,IAAI1H,KAAK,CAAC0H,SAAS,CAACxG,MAAM,GAAG,CAAC,iBAC5ClI,OAAA;wBAAKmG,SAAS,EAAC,gBAAgB;wBAAA5F,QAAA,gBAC7BP,OAAA,CAACiB,YAAY;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,MAElB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAENX,OAAA;sBAAKmG,SAAS,EAAC,oBAAoB;sBAAA5F,QAAA,gBACjCP,OAAA;wBAAImG,SAAS,EAAC,aAAa;wBAAA5F,QAAA,EAAEyG,KAAK,CAACQ;sBAAK;wBAAAhH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC9CX,OAAA;wBAAKmG,SAAS,EAAC,YAAY;wBAAA5F,QAAA,gBACzBP,OAAA;0BAAMmG,SAAS,EAAC,eAAe;0BAAA5F,QAAA,EAAEwB,cAAc,CAACiF,KAAK,CAACZ,OAAO;wBAAC;0BAAA5F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACtEX,OAAA;0BAAMmG,SAAS,EAAC,aAAa;0BAAA5F,QAAA,EAC1BgC,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClEV,WAAW,GAAI,aAAYmF,KAAK,CAACb,SAAS,IAAIa,KAAK,CAAClE,KAAM,EAAC,GAAI,SAAQkE,KAAK,CAACb,SAAS,IAAIa,KAAK,CAAClE,KAAM,EAAC,GACvG,QAAOkE,KAAK,CAACb,SAAS,IAAIa,KAAK,CAAClE,KAAM;wBAAC;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNX,OAAA;wBAAKmG,SAAS,EAAC,YAAY;wBAAA5F,QAAA,GACxByG,KAAK,CAACU,KAAK,iBAAI1H,OAAA;0BAAMmG,SAAS,EAAC,WAAW;0BAAA5F,QAAA,EAAEyG,KAAK,CAACU;wBAAK;0BAAAlH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EAC/DqG,KAAK,CAAC2H,eAAe,IAAI3H,KAAK,CAAC2H,eAAe,MAAM3H,KAAK,CAACb,SAAS,IAAIa,KAAK,CAAClE,KAAK,CAAC,iBAClF9C,OAAA;0BAAMmG,SAAS,EAAC,YAAY;0BAAA5F,QAAA,GACzBsB,WAAW,GAAG,qBAAqB,GAAG,cAAc,EAAEU,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GACxHV,WAAW,GAAI,aAAYmF,KAAK,CAAC2H,eAAgB,EAAC,GAAI,SAAQ3H,KAAK,CAAC2H,eAAgB,EAAC,GACrF,QAAO3H,KAAK,CAAC2H,eAAgB,EAAC;wBAAA;0BAAAnO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7B,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,EAGL0C,iBAAiB,KAAK6F,KAAK,iBAC1BlJ,OAAA;oBAAKmG,SAAS,EAAC,qBAAqB;oBAAA5F,QAAA,eAClCP,OAAA;sBAAKmG,SAAS,EAAC,sBAAsB;sBAAA5F,QAAA,gBAEnCP,OAAA;wBAAKmG,SAAS,EAAC,sBAAsB;wBAAA5F,QAAA,EAClCyG,KAAK,CAACoC,QAAQ,gBACbpJ,OAAA;0BAAKK,KAAK,EAAE;4BAAEuO,QAAQ,EAAE,UAAU;4BAAEC,KAAK,EAAE,MAAM;4BAAEC,MAAM,EAAE;0BAAO,CAAE;0BAAAvO,QAAA,gBAClEP,OAAA;4BACE+O,GAAG,EAAGA,GAAG,IAAKjL,WAAW,CAACiL,GAAG,CAAE;4BAC/BC,QAAQ;4BACRC,QAAQ;4BACRC,WAAW;4BACXC,OAAO,EAAC,UAAU;4BAClBN,KAAK,EAAC,MAAM;4BACZC,MAAM,EAAC,MAAM;4BACbM,MAAM,EAAEhF,eAAe,CAACpD,KAAK,CAAE;4BAC/B3G,KAAK,EAAE;8BACLwO,KAAK,EAAE,MAAM;8BACbC,MAAM,EAAE,MAAM;8BACdO,eAAe,EAAE,MAAM;8BACvBC,SAAS,EAAE;4BACb,CAAE;4BACFrB,OAAO,EAAGT,CAAC,IAAK;8BACd5J,aAAa,CAAE,yBAAwBoD,KAAK,CAACQ,KAAM,mCAAkC,CAAC;4BACxF,CAAE;4BACF+H,SAAS,EAAEA,CAAA,KAAM;8BACf3L,aAAa,CAAC,IAAI,CAAC;4BACrB,CAAE;4BACF4L,WAAW,EAAC,WAAW;4BAAAjP,QAAA,gBAEvBP,OAAA;8BAAQ+N,GAAG,EAAE/G,KAAK,CAACuC,cAAc,IAAIvC,KAAK,CAACoC,QAAS;8BAACuE,IAAI,EAAC;4BAAW;8BAAAnN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACvEqG,KAAK,CAAC0H,SAAS,IAAI1H,KAAK,CAAC0H,SAAS,CAACxG,MAAM,GAAG,CAAC,IAAIlB,KAAK,CAAC0H,SAAS,CAACxC,GAAG,CAAC,CAACuD,QAAQ,EAAEvG,KAAK,kBACpFlJ,OAAA;8BAEE0P,IAAI,EAAC,WAAW;8BAChB3B,GAAG,EAAE0B,QAAQ,CAACnB,GAAI;8BAClBqB,OAAO,EAAEF,QAAQ,CAACG,QAAS;8BAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;8BAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAI9G,KAAK,KAAK;4BAAE,GALrC,GAAEuG,QAAQ,CAACG,QAAS,IAAG1G,KAAM,EAAC;8BAAA1I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAEPqG,KAAK,CAAC0H,SAAS,IAAI1H,KAAK,CAAC0H,SAAS,CAACxG,MAAM,GAAG,CAAC,iBAC5ClI,OAAA;4BAAKmG,SAAS,EAAC,oBAAoB;4BAAA5F,QAAA,gBACjCP,OAAA,CAACiB,YAAY;8BAACkF,SAAS,EAAC;4BAAe;8BAAA3F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eAC1CX,OAAA;8BAAAO,QAAA,GAAM,yBAAuB,EAACyG,KAAK,CAAC0H,SAAS,CAACxG,MAAM,EAAC,cAAY;4BAAA;8BAAA1H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrE,CACN,EAEAgD,UAAU,iBACT3D,OAAA;4BAAKmG,SAAS,EAAC,qBAAqB;4BAAA5F,QAAA,eAClCP,OAAA;8BAAKmG,SAAS,EAAC,eAAe;8BAAA5F,QAAA,gBAC5BP,OAAA,CAACkB,eAAe;gCAACiF,SAAS,EAAC;8BAAY;gCAAA3F,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC1CX,OAAA;gCAAAO,QAAA,EAAIoD;8BAAU;gCAAAnD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC,eACnBX,OAAA;gCAAQmN,OAAO,EAAEA,CAAA,KAAMvJ,aAAa,CAAC,IAAI,CAAE;gCAACuC,SAAS,EAAC,mBAAmB;gCAAA5F,QAAA,EAAC;8BAE1E;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,GACJqG,KAAK,CAACsD,OAAO,gBACftK,OAAA;0BACE+N,GAAG,EAAG,iCAAgC/G,KAAK,CAACsD,OAAQ,mBAAmB;0BACvE9C,KAAK,EAAER,KAAK,CAACQ,KAAM;0BACnByI,WAAW,EAAC,GAAG;0BACfC,eAAe;0BACf7P,KAAK,EAAE;4BAAEwO,KAAK,EAAE,MAAM;4BAAEC,MAAM,EAAE,MAAM;4BAAEqB,MAAM,EAAE;0BAAO;wBAAE;0BAAA3P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClD,CAAC,gBAEVX,OAAA;0BAAKmG,SAAS,EAAC,aAAa;0BAAA5F,QAAA,gBAC1BP,OAAA;4BAAKmG,SAAS,EAAC,YAAY;4BAAA5F,QAAA,EAAC;0BAAE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpCX,OAAA;4BAAAO,QAAA,EAAI;0BAAiB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC1BX,OAAA;4BAAAO,QAAA,EAAIoD,UAAU,IAAI;0BAA4C;4BAAAnD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChE;sBACN;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGNX,OAAA;wBAAKmG,SAAS,EAAC,oBAAoB;wBAAA5F,QAAA,gBACjCP,OAAA;0BAAImG,SAAS,EAAC,qBAAqB;0BAAA5F,QAAA,EAAEyG,KAAK,CAACQ;wBAAK;0BAAAhH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtDX,OAAA;0BAAKmG,SAAS,EAAC,oBAAoB;0BAAA5F,QAAA,gBACjCP,OAAA;4BAAAO,QAAA,EAAOyG,KAAK,CAACZ;0BAAO;4BAAA5F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAC5BX,OAAA;4BAAAO,QAAA,EAAM;0BAAC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACdX,OAAA;4BAAAO,QAAA,GAAM,QAAM,EAACyG,KAAK,CAACb,SAAS;0BAAA;4BAAA3F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EACnCqG,KAAK,CAACvE,KAAK,iBACVzC,OAAA,CAAAE,SAAA;4BAAAK,QAAA,gBACEP,OAAA;8BAAAO,QAAA,EAAM;4BAAC;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACdX,OAAA;8BAAAO,QAAA,EAAOyG,KAAK,CAACvE;4BAAK;8BAAAjC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA,eAC1B,CACH;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNX,OAAA;0BAAKmG,SAAS,EAAC,uBAAuB;0BAAA5F,QAAA,gBACpCP,OAAA;4BACEmG,SAAS,EAAG,sBAAqBpB,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;4BACpEoI,OAAO,EAAEA,CAAA,KAAMnI,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;4BAAAxE,QAAA,gBAEtDP,OAAA;8BAAAO,QAAA,EAAM;4BAAE;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACfX,OAAA;8BAAAO,QAAA,EAAM;4BAAQ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf,CAAC,eACTX,OAAA;4BAAQmG,SAAS,EAAC,oBAAoB;4BAAA5F,QAAA,gBACpCP,OAAA;8BAAAO,QAAA,EAAM;4BAAE;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACfX,OAAA;8BAAAO,QAAA,EAAM;4BAAI;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACX,CAAC,eACTX,OAAA;4BACEmG,SAAS,EAAC,oBAAoB;4BAC9BgH,OAAO,EAAEA,CAAA,KAAM7J,oBAAoB,CAAC,IAAI,CAAE;4BAAA/C,QAAA,gBAE1CP,OAAA;8BAAAO,QAAA,EAAM;4BAAC;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACdX,OAAA;8BAAAO,QAAA,EAAM;4BAAK;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACZ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EAGLoE,gBAAgB,iBACf/E,OAAA;wBAAKmG,SAAS,EAAC,0BAA0B;wBAAA5F,QAAA,gBACvCP,OAAA;0BAAKmG,SAAS,EAAC,yBAAyB;0BAAA5F,QAAA,eACtCP,OAAA;4BAAAO,QAAA,GAAO8E,uBAAuB,CAAC,CAAC,CAAC6C,MAAM,EAAC,WAAS;0BAAA;4BAAA1H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrD,CAAC,eAGNX,OAAA;0BAAKmG,SAAS,EAAC,uBAAuB;0BAAA5F,QAAA,gBACpCP,OAAA;4BAAKmG,SAAS,EAAC,wBAAwB;4BAAA5F,QAAA,EACpC,CAAAmB,IAAI,aAAJA,IAAI,wBAAAmM,UAAA,GAAJnM,IAAI,CAAE0O,IAAI,cAAAvC,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYR,MAAM,CAAC,CAAC,CAAC,cAAAS,iBAAA,uBAArBA,iBAAA,CAAuBR,WAAW,CAAC,CAAC,KAAI;0BAAG;4BAAA9M,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzC,CAAC,eACNX,OAAA;4BAAKK,KAAK,EAAE;8BAAEgQ,IAAI,EAAE;4BAAE,CAAE;4BAAA9P,QAAA,gBACtBP,OAAA;8BACEmG,SAAS,EAAC,6BAA6B;8BACvCsE,KAAK,EAAElG,UAAW;8BAClBgJ,QAAQ,EAAGC,CAAC,IAAKhJ,aAAa,CAACgJ,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;8BAC/CmD,WAAW,EAAC,kBAAkB;8BAC9B0C,IAAI,EAAC,GAAG;8BACRjQ,KAAK,EAAE;gCACLkQ,SAAS,EAAE,MAAM;gCACjBC,MAAM,EAAE,MAAM;gCACdC,QAAQ,EAAE;8BACZ,CAAE;8BACFC,OAAO,EAAGlD,CAAC,IAAK;gCACdA,CAAC,CAACC,MAAM,CAACpN,KAAK,CAACyO,MAAM,GAAG,MAAM;gCAC9BtB,CAAC,CAACC,MAAM,CAACpN,KAAK,CAACyO,MAAM,GAAGtB,CAAC,CAACC,MAAM,CAACkD,YAAY,GAAG,IAAI;8BACtD;4BAAE;8BAAAnQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,EACD4D,UAAU,CAAC2C,IAAI,CAAC,CAAC,iBAChBlH,OAAA;8BAAKmG,SAAS,EAAC,yBAAyB;8BAAA5F,QAAA,gBACtCP,OAAA;gCACEmG,SAAS,EAAC,4BAA4B;gCACtCgH,OAAO,EAAEA,CAAA,KAAM3I,aAAa,CAAC,EAAE,CAAE;gCAAAjE,QAAA,EAClC;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACTX,OAAA;gCACEmG,SAAS,EAAC,4BAA4B;gCACtCgH,OAAO,EAAEhC,gBAAiB;gCAC1ByF,QAAQ,EAAE,CAACrM,UAAU,CAAC2C,IAAI,CAAC,CAAE;gCAAA3G,QAAA,EAC9B;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAGNX,OAAA;0BAAKmG,SAAS,EAAC,uBAAuB;0BAAA5F,QAAA,EACnC8E,uBAAuB,CAAC,CAAC,CAAC6C,MAAM,KAAK,CAAC,gBACrClI,OAAA;4BAAKK,KAAK,EAAE;8BAAEwQ,SAAS,EAAE,QAAQ;8BAAEC,OAAO,EAAE,QAAQ;8BAAEC,KAAK,EAAE;4BAAU,CAAE;4BAAAxQ,QAAA,gBACvEP,OAAA;8BAAKK,KAAK,EAAE;gCAAEC,QAAQ,EAAE,MAAM;gCAAE0Q,YAAY,EAAE;8BAAO,CAAE;8BAAAzQ,QAAA,EAAC;4BAAE;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAChEX,OAAA;8BAAAO,QAAA,EAAG;4BAAqD;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzD,CAAC,GAEN0E,uBAAuB,CAAC,CAAC,CAAC6G,GAAG,CAAEV,OAAO;4BAAA,IAAAyF,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA;4BAAA,oBACpCpR,OAAA;8BAAqCmG,SAAS,EAAC,iBAAiB;8BAAA5F,QAAA,gBAC9DP,OAAA;gCAAKmG,SAAS,EAAC,wBAAwB;gCAAA5F,QAAA,EACpCiL,OAAO,CAACE,MAAM,MAAAuF,eAAA,GAAIzF,OAAO,CAACC,MAAM,cAAAwF,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgB5D,MAAM,CAAC,CAAC,CAAC,cAAA6D,qBAAA,uBAAzBA,qBAAA,CAA2B5D,WAAW,CAAC,CAAC,KAAI;8BAAG;gCAAA9M,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/D,CAAC,eACNX,OAAA;gCAAKmG,SAAS,EAAC,yBAAyB;gCAAA5F,QAAA,gBACtCP,OAAA;kCAAKmG,SAAS,EAAC,wBAAwB;kCAAA5F,QAAA,gBACrCP,OAAA;oCAAMmG,SAAS,EAAC,wBAAwB;oCAAA5F,QAAA,EAAEiL,OAAO,CAACC;kCAAM;oCAAAjL,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAO,CAAC,EAC/D,CAAC6K,OAAO,CAAC6F,QAAQ,KAAK,OAAO,IAAI7F,OAAO,CAAC8F,OAAO,kBAC/CtR,OAAA,CAACF,UAAU;oCAACO,KAAK,EAAE;sCAAE0Q,KAAK,EAAE,SAAS;sCAAEzQ,QAAQ,EAAE,MAAM;sCAAEiR,UAAU,EAAE;oCAAM,CAAE;oCAAC/J,KAAK,EAAC;kCAAgB;oCAAAhH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CACvG,eACDX,OAAA;oCAAMmG,SAAS,EAAC,sBAAsB;oCAAA5F,QAAA,EACnCmM,aAAa,CAAClB,OAAO,CAACxD,SAAS,IAAIwD,OAAO,CAACmB,SAAS;kCAAC;oCAAAnM,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAClD,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACJ,CAAC,eACNX,OAAA;kCAAKmG,SAAS,EAAC,sBAAsB;kCAAA5F,QAAA,EAClCiL,OAAO,CAACD;gCAAI;kCAAA/K,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACV,CAAC,eACNX,OAAA;kCAAKmG,SAAS,EAAC,yBAAyB;kCAAA5F,QAAA,gBACtCP,OAAA;oCACEmN,OAAO,EAAEA,CAAA,KAAMhB,iBAAiB,CAACX,OAAO,CAAC9F,GAAG,IAAI8F,OAAO,CAAC/F,EAAE,CAAE;oCAC5DU,SAAS,EAAG,0BAAyB,CAAAgL,gBAAA,GAAA3F,OAAO,CAACK,OAAO,cAAAsF,gBAAA,eAAfA,gBAAA,CAAiB1J,QAAQ,CAAC/F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;oCAAAnF,QAAA,gBAE3FP,OAAA;sCAAAO,QAAA,EAAO,CAAA6Q,iBAAA,GAAA5F,OAAO,CAACK,OAAO,cAAAuF,iBAAA,eAAfA,iBAAA,CAAiB3J,QAAQ,CAAC/F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,GAAG,CAAC,GAAG,IAAI,GAAG;oCAAI;sCAAAlF,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC,EAChE6K,OAAO,CAACI,KAAK,GAAG,CAAC,iBAAI5L,OAAA;sCAAAO,QAAA,EAAOiL,OAAO,CAACI;oCAAK;sCAAApL,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC5C,CAAC,eACTX,OAAA;oCAAQmG,SAAS,EAAC,wBAAwB;oCAAA5F,QAAA,EAAC;kCAE3C;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAQ,CAAC,EACR6K,OAAO,CAAC9J,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,GAAG,kBACzB1F,OAAA,CAAAE,SAAA;oCAAAK,QAAA,gBACEP,OAAA;sCAAQmG,SAAS,EAAC,wBAAwB;sCAAA5F,QAAA,EAAC;oCAE3C;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAQ,CAAC,eACTX,OAAA;sCACEmG,SAAS,EAAC,wBAAwB;sCAClCgH,OAAO,EAAEA,CAAA,KAAM;wCACb,IAAIqE,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;0CACnEnF,mBAAmB,CAACd,OAAO,CAAC9F,GAAG,IAAI8F,OAAO,CAAC/F,EAAE,CAAC;wCAChD;sCACF,CAAE;sCAAAlF,QAAA,EACH;oCAED;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAQ,CAAC;kCAAA,eACT,CACH;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACE,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC;4BAAA,GA9CE6K,OAAO,CAAC9F,GAAG,IAAI8F,OAAO,CAAC/F,EAAE;8BAAAjF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OA+C9B,CAAC;0BAAA,CACP;wBACF;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC,GAlTmBuI,KAAK;gBAAA1I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmThB,CAAC;YAAA,CAClB;UAAC,gBACF,CAAC,gBAEHX,OAAA;YAAKmG,SAAS,EAAC,aAAa;YAAA5F,QAAA,gBAC1BP,OAAA,CAACY,eAAe;cAACuF,SAAS,EAAC;YAAY;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1CX,OAAA;cAAAO,QAAA,EAAKsB,WAAW,GAAG,6BAA6B,GAAG;YAAiB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1EX,OAAA;cAAAO,QAAA,EAAIsB,WAAW,GAAG,kEAAkE,GAAG;YAA4D;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxJX,OAAA;cAAGmG,SAAS,EAAC,YAAY;cAAA5F,QAAA,EAAEsB,WAAW,GAAG,yCAAyC,GAAG;YAA6C;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpI;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLwD,WAAW,GAAG,CAAC,iBACdnE,OAAA;UAAKmG,SAAS,EAAC,sBAAsB;UAAA5F,QAAA,gBACnCP,OAAA;YAAKmG,SAAS,EAAC,iBAAiB;YAAA5F,QAAA,GAAC,UACvB,EAACmI,SAAS,EAAC,GAAC,EAACC,OAAO,EAAC,MAAI,EAACxE,WAAW,EAAC,SAChD;UAAA;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENX,OAAA;YAAKmG,SAAS,EAAC,qBAAqB;YAAA5F,QAAA,gBAClCP,OAAA;cACEmG,SAAS,EAAC,gBAAgB;cAC1BgH,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAAC9E,WAAW,GAAG,CAAC,CAAE;cACjD6M,QAAQ,EAAE7M,WAAW,KAAK,CAAE;cAAAxD,QAAA,EAC7B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAER+Q,KAAK,CAACC,IAAI,CAAC;cAAEzJ,MAAM,EAAEM,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEL,UAAU;YAAE,CAAC,EAAE,CAACqJ,CAAC,EAAEC,CAAC,KAAK;cACzD,IAAIC,OAAO;cACX,IAAIvJ,UAAU,IAAI,CAAC,EAAE;gBACnBuJ,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI9N,WAAW,IAAI,CAAC,EAAE;gBAC3B+N,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI9N,WAAW,IAAIwE,UAAU,GAAG,CAAC,EAAE;gBACxCuJ,OAAO,GAAGvJ,UAAU,GAAG,CAAC,GAAGsJ,CAAC;cAC9B,CAAC,MAAM;gBACLC,OAAO,GAAG/N,WAAW,GAAG,CAAC,GAAG8N,CAAC;cAC/B;cAEA,oBACE7R,OAAA;gBAEEmG,SAAS,EAAG,kBAAiBpC,WAAW,KAAK+N,OAAO,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACvE3E,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAACiJ,OAAO,CAAE;gBAAAvR,QAAA,EAExCuR;cAAO,GAJHA,OAAO;gBAAAtR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CAAC;YAEb,CAAC,CAAC,eAEFX,OAAA;cACEmG,SAAS,EAAC,gBAAgB;cAC1BgH,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAAC9E,WAAW,GAAG,CAAC,CAAE;cACjD6M,QAAQ,EAAE7M,WAAW,KAAKwE,UAAW;cAAAhI,QAAA,EACtC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENX,OAAA;YAAKmG,SAAS,EAAC,oBAAoB;YAAA5F,QAAA,gBACjCP,OAAA;cAAAO,QAAA,EAAM;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7BX,OAAA;cACEyK,KAAK,EAAExG,aAAc;cACrBsJ,QAAQ,EAAGC,CAAC,IAAKzE,oBAAoB,CAACgJ,MAAM,CAACvE,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAC,CAAE;cAAAlK,QAAA,gBAE9DP,OAAA;gBAAQyK,KAAK,EAAE,CAAE;gBAAAlK,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5BX,OAAA;gBAAQyK,KAAK,EAAE,EAAG;gBAAAlK,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BX,OAAA;gBAAQyK,KAAK,EAAE,EAAG;gBAAAlK,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BX,OAAA;gBAAQyK,KAAK,EAAE,EAAG;gBAAAlK,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,eACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEN;AAACc,EAAA,CA/mCQD,YAAY;EAAA,QACFnC,WAAW,EAC6BQ,WAAW,EACnDT,WAAW;AAAA;AAAA4S,EAAA,GAHrBxQ,YAAY;AAinCrB,eAAeA,YAAY;AAAC,IAAAwQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}