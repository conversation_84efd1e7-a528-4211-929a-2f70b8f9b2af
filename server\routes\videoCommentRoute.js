const router = require("express").Router();
const VideoComment = require("../models/videoCommentModel");
const authMiddleware = require("../middlewares/authMiddleware");
const User = require("../models/userModel");

// Get comments for a specific video
router.get("/:videoId", authMiddleware, async (req, res) => {
  try {
    const { videoId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const comments = await VideoComment.find({ videoId })
      .populate('user', 'name')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const totalComments = await VideoComment.countDocuments({ videoId });

    res.send({
      success: true,
      data: {
        comments,
        totalComments,
        currentPage: page,
        totalPages: Math.ceil(totalComments / limit)
      }
    });
  } catch (error) {
    res.status(500).send({
      success: false,
      message: "Error fetching comments",
      error: error.message
    });
  }
});

// Add a new comment (main route)
router.post("/", authMiddleware, async (req, res) => {
  try {
    const { videoId, text } = req.body;
    const userId = req.body.userId;

    console.log('📝 Video comment request received:');
    console.log('  - videoId:', videoId, '(type:', typeof videoId, ')');
    console.log('  - text:', text, '(length:', text?.length, ')');
    console.log('  - userId:', userId, '(type:', typeof userId, ')');
    console.log('  - Full body:', req.body);

    if (!videoId || !text) {
      console.log('❌ Missing required fields:', { videoId: !!videoId, text: !!text });
      return res.status(400).send({
        success: false,
        message: "Video ID and comment text are required"
      });
    }

    if (!userId) {
      console.log('❌ Missing userId');
      return res.status(400).send({
        success: false,
        message: "User ID is required"
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        success: false,
        message: "User not found"
      });
    }

    // Create proper author name from user data
    let authorName = "Anonymous";
    if (user.firstName && user.lastName) {
      authorName = `${user.firstName} ${user.lastName}`;
    } else if (user.name) {
      authorName = user.name;
    } else if (user.username) {
      authorName = user.username;
    }

    // Create proper avatar - use profile image or first letter of name
    let avatar = authorName.charAt(0).toUpperCase();
    if (user.profileImage) {
      avatar = user.profileImage;
    }

    const newComment = new VideoComment({
      videoId,
      text: text.trim(),
      user: userId,
      author: authorName,
      avatar: avatar,
      userLevel: user.level,
      userClass: user.class
    });

    await newComment.save();

    // Populate user data for response
    await newComment.populate('user', 'firstName lastName name username profileImage');

    res.send({
      success: true,
      message: "Comment added successfully",
      data: newComment
    });
  } catch (error) {
    res.status(500).send({
      success: false,
      message: "Error adding comment",
      error: error.message
    });
  }
});

// Add a new comment (legacy route for backward compatibility)
router.post("/add", authMiddleware, async (req, res) => {
  try {
    const { videoId, text } = req.body;
    const userId = req.body.userId;

    console.log('📝 Video comment request received (legacy route):');
    console.log('  - videoId:', videoId, '(type:', typeof videoId, ')');
    console.log('  - text:', text, '(length:', text?.length, ')');
    console.log('  - userId:', userId, '(type:', typeof userId, ')');

    if (!videoId || !text) {
      return res.status(400).send({
        success: false,
        message: "Video ID and comment text are required"
      });
    }

    if (!userId) {
      return res.status(400).send({
        success: false,
        message: "User ID is required"
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        success: false,
        message: "User not found"
      });
    }

    // Create proper author name from user data
    let authorName = "Anonymous";
    if (user.firstName && user.lastName) {
      authorName = `${user.firstName} ${user.lastName}`;
    } else if (user.name) {
      authorName = user.name;
    } else if (user.username) {
      authorName = user.username;
    }

    // Create proper avatar - use profile image or first letter of name
    let avatar = authorName.charAt(0).toUpperCase();
    if (user.profileImage) {
      avatar = user.profileImage;
    }

    const newComment = new VideoComment({
      videoId,
      text: text.trim(),
      user: userId,
      author: authorName,
      avatar: avatar,
      userLevel: user.level,
      userClass: user.class
    });

    await newComment.save();

    // Populate user data for response
    await newComment.populate('user', 'firstName lastName name username profileImage');

    res.send({
      success: true,
      message: "Comment added successfully",
      data: newComment
    });
  } catch (error) {
    res.status(500).send({
      success: false,
      message: "Error adding comment",
      error: error.message
    });
  }
});

// Add a reply to a comment
router.post("/reply/:commentId", authMiddleware, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { text } = req.body;
    const userId = req.body.userId;

    if (!text) {
      return res.status(400).send({
        success: false,
        message: "Reply text is required"
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        success: false,
        message: "User not found"
      });
    }

    const comment = await VideoComment.findById(commentId);
    if (!comment) {
      return res.status(404).send({
        success: false,
        message: "Comment not found"
      });
    }

    const reply = {
      text: text.trim(),
      user: userId,
      author: user.name || "Anonymous",
      avatar: user.name?.charAt(0)?.toUpperCase() || "A",
      timestamp: new Date()
    };

    comment.replies.push(reply);
    await comment.save();

    res.send({
      success: true,
      message: "Reply added successfully",
      data: comment
    });
  } catch (error) {
    res.status(500).send({
      success: false,
      message: "Error adding reply",
      error: error.message
    });
  }
});

// Like/unlike a comment
router.post("/like/:commentId", authMiddleware, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { isReply = false, replyId } = req.body;
    const userId = req.body.userId;

    const comment = await VideoComment.findById(commentId);
    if (!comment) {
      return res.status(404).send({
        success: false,
        message: "Comment not found"
      });
    }

    if (isReply && replyId) {
      // Handle reply like/unlike
      const reply = comment.replies.id(replyId);
      if (!reply) {
        return res.status(404).send({
          success: false,
          message: "Reply not found"
        });
      }

      const hasLiked = reply.likedBy.includes(userId);
      if (hasLiked) {
        reply.likedBy.pull(userId);
        reply.likes = Math.max(0, reply.likes - 1);
      } else {
        reply.likedBy.push(userId);
        reply.likes += 1;
      }
    } else {
      // Handle comment like/unlike
      const hasLiked = comment.likedBy.includes(userId);
      if (hasLiked) {
        comment.likedBy.pull(userId);
        comment.likes = Math.max(0, comment.likes - 1);
      } else {
        comment.likedBy.push(userId);
        comment.likes += 1;
      }
    }

    await comment.save();

    res.send({
      success: true,
      message: "Like updated successfully",
      data: comment
    });
  } catch (error) {
    res.status(500).send({
      success: false,
      message: "Error updating like",
      error: error.message
    });
  }
});

// Delete a comment (only by author or admin)
router.delete("/:commentId", authMiddleware, async (req, res) => {
  try {
    const { commentId } = req.params;
    const userId = req.body.userId;

    const user = await User.findById(userId);
    const comment = await VideoComment.findById(commentId);

    if (!comment) {
      return res.status(404).send({
        success: false,
        message: "Comment not found"
      });
    }

    // Check if user is the author or admin
    if (comment.user.toString() !== userId && !user.isAdmin) {
      return res.status(403).send({
        success: false,
        message: "You can only delete your own comments"
      });
    }

    await VideoComment.findByIdAndDelete(commentId);

    res.send({
      success: true,
      message: "Comment deleted successfully"
    });
  } catch (error) {
    res.status(500).send({
      success: false,
      message: "Error deleting comment",
      error: error.message
    });
  }
});

// Delete a comment
router.delete("/delete/:commentId", authMiddleware, async (req, res) => {
  try {
    const { commentId } = req.params;
    const userId = req.body.userId;

    console.log('🗑️ Delete comment request:', { commentId, userId });

    if (!commentId) {
      return res.status(400).send({
        success: false,
        message: "Comment ID is required"
      });
    }

    // Find the comment
    const comment = await VideoComment.findById(commentId);
    if (!comment) {
      return res.status(404).send({
        success: false,
        message: "Comment not found"
      });
    }

    // Check if user owns the comment or is admin
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        success: false,
        message: "User not found"
      });
    }

    if (comment.user.toString() !== userId && !user.isAdmin) {
      return res.status(403).send({
        success: false,
        message: "You can only delete your own comments"
      });
    }

    // Delete the comment
    await VideoComment.findByIdAndDelete(commentId);

    console.log('✅ Comment deleted successfully:', commentId);

    res.send({
      success: true,
      message: "Comment deleted successfully"
    });

  } catch (error) {
    console.error('❌ Error deleting comment:', error);
    res.status(500).send({
      success: false,
      message: "Failed to delete comment"
    });
  }
});

module.exports = router;
