{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\StudyMaterial\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport { FaBook, FaFileAlt, FaGraduationCap, FaDownload, FaEye, FaChevronDown, FaSearch, FaTimes } from \"react-icons/fa\";\nimport { TbFileText, TbBook as TbBookIcon, TbSchool, TbSearch, TbFilter, TbSortAscending, TbDownload, TbEye, TbCalendar, TbUser, TbChevronDown as TbChevronDownIcon, TbChevronUp, TbX, TbAlertTriangle, TbInfoCircle, TbCheck, TbBooks, TbCertificate } from \"react-icons/tb\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction StudyMaterial() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili,\n    getSubjectName\n  } = useLanguage();\n  const dispatch = useDispatch();\n\n  // Simplified CSS - no header conflicts\n  const inlineStyles = `\n    /* Mobile Layout Fixes for Study Materials */\n    @media (max-width: 768px) {\n      /* Reduce Bell Icon Size */\n      .notification-bell-button .w-5,\n      .notification-bell-button .h-5 {\n        width: 14px !important;\n        height: 14px !important;\n      }\n\n      /* Study Materials content positioning - no header conflicts */\n      .study-material-modern,\n      .container-modern {\n        padding-top: 16px !important;\n      }\n\n      /* Tabs positioning */\n      .ant-tabs-nav {\n        margin-bottom: 8px !important;\n      }\n    }\n\n      /* Force ALL sidebar text elements to be white */\n      .sidebar *,\n      .mobile-sidebar *,\n      .modern-sidebar *,\n      [class*=\"sidebar\"] *,\n      .ant-menu *,\n      .ant-menu-item *,\n      .sidebar .menu-item,\n      .mobile-sidebar .menu-item,\n      .modern-sidebar .menu-item,\n      .sidebar a,\n      .mobile-sidebar a,\n      .modern-sidebar a,\n      .sidebar span,\n      .mobile-sidebar span,\n      .modern-sidebar span,\n      .sidebar div,\n      .mobile-sidebar div,\n      .modern-sidebar div,\n      .sidebar p,\n      .mobile-sidebar p,\n      .modern-sidebar p,\n      .sidebar h1, .sidebar h2, .sidebar h3, .sidebar h4, .sidebar h5, .sidebar h6,\n      .mobile-sidebar h1, .mobile-sidebar h2, .mobile-sidebar h3, .mobile-sidebar h4, .mobile-sidebar h5, .mobile-sidebar h6,\n      .modern-sidebar h1, .modern-sidebar h2, .modern-sidebar h3, .modern-sidebar h4, .modern-sidebar h5, .modern-sidebar h6,\n      .sidebar .text-gray-100,\n      .sidebar .text-gray-200,\n      .sidebar .text-gray-300,\n      .sidebar .text-gray-400,\n      .sidebar .text-gray-500,\n      .sidebar .text-gray-600,\n      .sidebar .text-gray-700,\n      .sidebar .text-gray-800,\n      .sidebar .text-gray-900,\n      .mobile-sidebar .text-gray-100,\n      .mobile-sidebar .text-gray-200,\n      .mobile-sidebar .text-gray-300,\n      .mobile-sidebar .text-gray-400,\n      .mobile-sidebar .text-gray-500,\n      .mobile-sidebar .text-gray-600,\n      .mobile-sidebar .text-gray-700,\n      .mobile-sidebar .text-gray-800,\n      .mobile-sidebar .text-gray-900,\n      .modern-sidebar .text-gray-100,\n      .modern-sidebar .text-gray-200,\n      .modern-sidebar .text-gray-300,\n      .modern-sidebar .text-gray-400,\n      .modern-sidebar .text-gray-500,\n      .modern-sidebar .text-gray-600,\n      .modern-sidebar .text-gray-700,\n      .modern-sidebar .text-gray-800,\n      .modern-sidebar .text-gray-900 {\n        color: #ffffff !important;\n        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;\n        font-weight: 500 !important;\n      }\n\n      /* Sidebar hover effects */\n      .sidebar .menu-item:hover,\n      .mobile-sidebar .menu-item:hover,\n      .modern-sidebar .menu-item:hover,\n      .sidebar a:hover,\n      .mobile-sidebar a:hover,\n      .modern-sidebar a:hover {\n        color: #ffffff !important;\n        background: rgba(255, 255, 255, 0.2) !important;\n        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;\n      }\n\n      /* Remove Gap - Adjust Study Materials content to fit perfectly under header */\n      .study-material-modern,\n      .container-modern,\n      .layout,\n      .layout-modern,\n      main,\n      .body {\n        margin-top: 48px !important;\n        padding-top: 0px !important;\n      }\n\n      /* Study Materials specific mobile fixes - No gap */\n      .modern-header {\n        margin-top: 48px !important;\n        padding-top: 8px !important;\n        position: relative !important;\n        z-index: 1 !important;\n      }\n\n      .tabs-container {\n        position: relative !important;\n        z-index: 1 !important;\n        margin-top: 0px !important;\n        padding-top: 0px !important;\n      }\n\n      /* Study Material Content - No gaps */\n      .study-material-content,\n      .material-grid,\n      .pdf-grid,\n      .content-container {\n        margin-top: 0px !important;\n        padding-top: 8px !important;\n      }\n\n      /* Page Title and Search - Compact spacing */\n      .page-title,\n      .search-container,\n      .filter-container {\n        margin-top: 0px !important;\n        margin-bottom: 8px !important;\n        padding-top: 0px !important;\n      }\n\n      /* Tabs - Compact spacing */\n      .ant-tabs,\n      .tabs-wrapper {\n        margin-top: 0px !important;\n        padding-top: 0px !important;\n      }\n\n      .ant-tabs-nav {\n        margin-bottom: 8px !important;\n      }\n\n      .ant-tabs-content {\n        padding-top: 0px !important;\n      }\n\n      .lg\\\\:hidden button {\n        background: rgba(255, 255, 255, 0.95) !important;\n        border-radius: 8px !important;\n        padding: 8px !important;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;\n        border: 1px solid rgba(0, 0, 0, 0.1) !important;\n      }\n    }\n\n    /* Center All Modals */\n    .ant-modal,\n    .modal,\n    .pdf-modal,\n    .study-modal,\n    .material-modal {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      top: 0 !important;\n      padding-top: 0 !important;\n    }\n\n    .ant-modal-content,\n    .modal-content,\n    .pdf-modal-content,\n    .study-modal-content,\n    .material-modal-content {\n      margin: 0 auto !important;\n      position: relative !important;\n      top: auto !important;\n      transform: none !important;\n    }\n\n    .ant-modal-wrap {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      min-height: 100vh !important;\n    }\n\n    /* PDF Modal specific centering */\n    .pdf-modal-overlay {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      position: fixed !important;\n      top: 0 !important;\n      left: 0 !important;\n      width: 100vw !important;\n      height: 100vh !important;\n      z-index: 10000 !important;\n      padding: 20px !important;\n      box-sizing: border-box !important;\n    }\n  `;\n\n  // Add styles to document head\n  React.useEffect(() => {\n    const styleElement = document.createElement('style');\n    styleElement.textContent = inlineStyles;\n    document.head.appendChild(styleElement);\n\n    // Add additional mobile header fixes\n    const mobileHeaderFix = document.createElement('style');\n    mobileHeaderFix.textContent = `\n      @media (max-width: 768px) {\n        /* Force hamburger menu to top left */\n        .lg\\\\:hidden {\n          position: fixed !important;\n          top: 8px !important;\n          left: 8px !important;\n          z-index: 10001 !important;\n        }\n\n        /* Desktop-like header layout for mobile */\n        .nav-modern, header {\n          position: fixed !important;\n          top: 0 !important;\n          left: 0 !important;\n          right: 0 !important;\n          height: 48px !important;\n          display: flex !important;\n          align-items: center !important;\n          justify-content: space-between !important;\n          padding: 0 48px 0 16px !important;\n          z-index: 10000 !important;\n          background: rgba(255, 255, 255, 0.98) !important;\n          backdrop-filter: blur(15px) !important;\n        }\n\n        /* Header content layout */\n        .nav-modern .flex, header .flex {\n          width: 100% !important;\n          display: flex !important;\n          align-items: center !important;\n          justify-content: space-between !important;\n        }\n\n        /* Logo section */\n        .nav-modern .flex > div:first-child, header .flex > div:first-child {\n          flex: 1 !important;\n          display: flex !important;\n          align-items: center !important;\n          margin-left: 32px !important;\n        }\n\n        /* Right section (bell + profile) */\n        .nav-modern .flex > div:last-child, header .flex > div:last-child {\n          display: flex !important;\n          align-items: center !important;\n          gap: 8px !important;\n        }\n\n        /* Bell icon size */\n        .notification-bell-button .w-5, .notification-bell-button .h-5 {\n          width: 14px !important;\n          height: 14px !important;\n        }\n      }\n    `;\n    document.head.appendChild(mobileHeaderFix);\n    return () => {\n      document.head.removeChild(styleElement);\n      document.head.removeChild(mobileHeaderFix);\n    };\n  }, []);\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = (user === null || user === void 0 ? void 0 : user.level) || 'Primary';\n  const userLevelLower = useMemo(() => userLevel.toLowerCase(), [userLevel]);\n  const subjectsList = useMemo(() => {\n    return userLevelLower === 'primary' ? primarySubjects : userLevelLower === 'primary_kiswahili' ? primaryKiswahiliSubjects : userLevelLower === 'secondary' ? secondarySubjects : advanceSubjects;\n  }, [userLevelLower]);\n\n  // Debug: Log current level and subjects (removed subjectsList from dependencies to prevent infinite loop)\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, user]);\n\n  // Define all possible classes for each level (memoized to prevent re-renders)\n  const allPossibleClasses = useMemo(() => {\n    return userLevelLower === 'primary' ? ['1', '2', '3', '4', '5', '6', '7'] : userLevelLower === 'secondary' ? ['Form-1', 'Form-2', 'Form-3', 'Form-4'] : ['Form-5', 'Form-6'];\n  }, [userLevelLower]);\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"study-notes\");\n  const [selectedClass, setSelectedClass] = useState((user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className) || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className);\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className);\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes (removed subjectsList from dependencies)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user === null || user === void 0 ? void 0 : user.level, selectedSubject]); // Removed subjectsList to prevent infinite loop\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" : selectedClass.toString().replace(\"Form-\", \"\");\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject // This can be \"all\" or a specific subject\n      };\n\n      if (userLevel) {\n        data.level = userLevel;\n      }\n      const res = await getStudyMaterial(data);\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = tab => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n  const handleSubjectChange = subject => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n  const handleClassChange = className => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material => material.title.toLowerCase().includes(searchLower) || material.subject.toLowerCase().includes(searchLower) || material.year && material.year.toLowerCase().includes(searchLower));\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;else if (!a.year && b.year) return 1;else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;else if (!a.year && b.year) return 1;else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = documentUrl => {\n    // Use proxy endpoint to handle CORS issues\n    const proxyUrl = `${process.env.REACT_APP_SERVER_DOMAIN}/api/study/document-proxy?url=${encodeURIComponent(documentUrl)}`;\n    fetch(proxyUrl, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('token')}`\n      }\n    }).then(response => {\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      return response.blob();\n    }).then(blob => {\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = documentUrl.split(\"/\").pop();\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      window.URL.revokeObjectURL(url);\n    }).catch(error => {\n      console.error(\"Error downloading the file:\", error);\n      // Fallback to direct download if proxy fails\n      window.open(documentUrl, '_blank');\n    });\n  };\n  const handleDocumentPreview = documentUrl => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n    style: {\n      marginTop: window.innerWidth <= 768 ? '48px' : '0px',\n      paddingTop: window.innerWidth <= 768 ? '0px' : '0px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-modern py-8\",\n      style: {\n        paddingTop: window.innerWidth <= 768 ? '8px' : '32px',\n        marginTop: window.innerWidth <= 768 ? '0px' : '0px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"study-tabs\",\n          children: [{\n            key: 'study-notes',\n            label: isKiswahili ? 'Maelezo' : 'Notes',\n            icon: TbFileText\n          }, {\n            key: 'past-papers',\n            label: isKiswahili ? 'Karatasi za Zamani' : 'Past Papers',\n            icon: TbCertificate\n          }, {\n            key: 'books',\n            label: isKiswahili ? 'Vitabu' : 'Books',\n            icon: TbBookIcon\n          }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `study-tab ${activeTab === tab.key ? 'active' : ''}`,\n            onClick: () => handleTabChange(tab.key),\n            children: [/*#__PURE__*/_jsxDEV(tab.icon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: tab.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 17\n            }, this)]\n          }, tab.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col lg:flex-row gap-6 items-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: isKiswahili ? 'Tafuta Vifaa' : 'Search Materials'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                placeholder: isKiswahili ? `Tafuta ${activeTab === 'study-notes' ? 'maelezo' : activeTab === 'past-papers' ? 'karatasi za zamani' : 'vitabu'}...` : `Search ${activeTab.replace('-', ' ')}...`,\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-64\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class', userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-xs text-primary-600 font-medium\",\n                  children: [\"(\", isKiswahili ? 'Darasa lako: ' : 'Your class: ', userLevelLower === 'primary' || userLevelLower === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${userCurrentClass}` : `Class ${userCurrentClass}` : `Form ${userCurrentClass}`, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: toggleClassSelector,\n                  className: \"w-full input-modern flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                      className: \"w-4 h-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: selectedClass === 'all' ? 'All Classes' : userLevelLower === 'primary' ? `Class ${selectedClass}` : `Form ${selectedClass}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 23\n                    }, this), selectedClass === userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge-primary text-xs\",\n                      children: \"Current\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TbChevronDownIcon, {\n                    className: `w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                  children: showClassSelector && /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      y: -10\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    exit: {\n                      opacity: 0,\n                      y: -10\n                    },\n                    className: \"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'}`,\n                      onClick: () => handleClassChange('all'),\n                      children: \"All Classes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 696,\n                      columnNumber: 25\n                    }, this), availableClasses.map((className, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'}`,\n                      onClick: () => handleClassChange(className),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 712,\n                        columnNumber: 29\n                      }, this), className === userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"badge-success text-xs\",\n                        children: \"Your Class\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 716,\n                        columnNumber: 31\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 690,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-64\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: isKiswahili ? 'Chuja kwa Somo' : 'Filter by Subject'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedSubject,\n                onChange: e => handleSubjectChange(e.target.value),\n                className: \"input-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 19\n                }, this), subjectsList.map((subject, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subject,\n                  children: getSubjectName(subject)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-48\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: isKiswahili ? 'Panga kwa' : 'Sort by'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: sortBy,\n                onChange: e => setSortBy(e.target.value),\n                className: \"input-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"newest\",\n                  children: \"Newest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"oldest\",\n                  children: \"Oldest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"title\",\n                  children: \"By Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => {\n                setSearchTerm(\"\");\n                setSelectedClass(\"all\");\n                setSelectedSubject(\"all\");\n                setSortBy(\"newest\");\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this), (searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 pt-4 border-t border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Showing \", filteredAndSortedMaterials.length, \" of \", materials.length, \" \", activeTab.replace('-', ' ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 777,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"materials-section\",\n        children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading materials...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 11\n        }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-state\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n            className: \"error-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Error Loading Materials\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 797,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"retry-btn\",\n            onClick: () => {\n              setError(null);\n              fetchMaterials();\n            },\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 11\n        }, this) : filteredAndSortedMaterials.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: activeTab === 'past-papers' ? \"flex flex-wrap gap-3 justify-start\" : \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n          children: filteredAndSortedMaterials.map((material, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: activeTab === 'past-papers' ? \"past-paper-square-card\" : \"study-card\",\n            children: activeTab === 'past-papers' ?\n            /*#__PURE__*/\n            // Past Papers Layout - Buttons below tags\n            _jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"study-card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"study-card-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(FaFileAlt, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isKiswahili ? 'Karatasi ya Zamani' : 'Past Paper'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 824,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"study-card-title\",\n                  children: material.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-content\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"material-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-subject\",\n                    children: material.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 25\n                  }, this), material.className && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-class\",\n                    children: userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 27\n                  }, this), material.year && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge badge-secondary\",\n                    children: material.year\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 840,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-actions\",\n                children: material.documentUrl ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn-primary\",\n                    onClick: () => handleDocumentPreview(material.documentUrl),\n                    children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 852,\n                      columnNumber: 29\n                    }, this), \" \", isKiswahili ? 'Ona' : 'View']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn-primary\",\n                    onClick: () => handleDocumentDownload(material.documentUrl),\n                    children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 29\n                    }, this), \" \", isKiswahili ? 'Pakua' : 'Download']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 854,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"unavailable\",\n                  children: isKiswahili ? 'Haipatikani' : 'Not available'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true) :\n            /*#__PURE__*/\n            // Regular Layout for other tabs\n            _jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"study-card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"study-card-meta\",\n                  children: [activeTab === 'study-notes' && /*#__PURE__*/_jsxDEV(FaFileAlt, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 57\n                  }, this), activeTab === 'books' && /*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 51\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: activeTab === 'study-notes' ? isKiswahili ? 'Maelezo' : 'Note' : isKiswahili ? 'Kitabu' : 'Book'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 870,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"study-card-title\",\n                  children: material.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 23\n                }, this), material.year && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge badge-secondary mt-2\",\n                  children: material.year\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 882,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"material-title\",\n                  children: material.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 887,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"material-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-subject\",\n                    children: material.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 889,\n                    columnNumber: 25\n                  }, this), material.className && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-class\",\n                    children: userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 891,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-actions\",\n                children: material.documentUrl ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn secondary\",\n                    onClick: () => handleDocumentPreview(material.documentUrl),\n                    children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 905,\n                      columnNumber: 29\n                    }, this), \" \", isKiswahili ? 'Ona' : 'View']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 901,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn primary\",\n                    onClick: () => handleDocumentDownload(material.documentUrl),\n                    children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 911,\n                      columnNumber: 29\n                    }, this), \" \", isKiswahili ? 'Pakua' : 'Download']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 907,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"unavailable\",\n                  children: isKiswahili ? 'Haipatikani' : 'Not available'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n            className: \"empty-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Materials Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No study materials are available for your current selection.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"suggestion\",\n            children: \"Try selecting a different class or subject.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 787,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(PDFModal, {\n        modalIsOpen: modalIsOpen,\n        closeModal: () => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        },\n        documentUrl: documentUrl\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 937,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 599,\n    columnNumber: 5\n  }, this);\n}\n_s(StudyMaterial, \"lHfLHOyN57Im6RcUENwOdGZM0g0=\", false, function () {\n  return [useSelector, useLanguage, useDispatch];\n});\n_c = StudyMaterial;\nexport default StudyMaterial;\nvar _c;\n$RefreshReg$(_c, \"StudyMaterial\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "useLanguage", "PDFModal", "FaBook", "FaFileAlt", "FaGraduationCap", "FaDownload", "FaEye", "FaChevronDown", "FaSearch", "FaTimes", "TbFileText", "TbBook", "TbBookIcon", "TbSchool", "TbSearch", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbDownload", "TbEye", "TbCalendar", "TbUser", "TbChevronDown", "TbChevronDownIcon", "TbChevronUp", "TbX", "TbAlertTriangle", "TbInfoCircle", "TbCheck", "TbBooks", "TbCertificate", "primarySubjects", "primaryKiswahiliSubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StudyMaterial", "_s", "user", "state", "t", "isKiswahili", "getSubjectName", "dispatch", "inlineStyles", "styleElement", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "mobileHeaderFix", "<PERSON><PERSON><PERSON><PERSON>", "userLevel", "level", "userLevelLower", "toLowerCase", "subjectsList", "console", "log", "allPossibleClasses", "activeTab", "setActiveTab", "selectedClass", "setSelectedClass", "class", "className", "selectedSubject", "setSelectedSubject", "userCurrentClass", "materials", "setMaterials", "isLoading", "setIsLoading", "error", "setError", "modalIsOpen", "setModalIsOpen", "documentUrl", "setDocumentUrl", "availableClasses", "setAvailableClasses", "showClassSelector", "setShowClassSelector", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "userClass", "length", "isValidSubject", "includes", "setAvailableClassesForLevel", "fetchMaterials", "normalizedClassName", "toString", "replace", "data", "content", "subject", "res", "status", "success", "handleTabChange", "tab", "handleSubjectChange", "handleClassChange", "toggleClassSelector", "filteredAndSortedMaterials", "filtered", "trim", "searchLower", "filter", "material", "title", "year", "sort", "a", "b", "parseInt", "localeCompare", "handleDocumentDownload", "proxyUrl", "process", "env", "REACT_APP_SERVER_DOMAIN", "encodeURIComponent", "fetch", "method", "headers", "localStorage", "getItem", "then", "response", "ok", "Error", "blob", "url", "window", "URL", "createObjectURL", "href", "download", "split", "pop", "body", "click", "revokeObjectURL", "catch", "open", "handleDocumentPreview", "style", "marginTop", "innerWidth", "paddingTop", "children", "key", "label", "icon", "map", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "delay", "placeholder", "value", "onChange", "e", "target", "exit", "index", "closeModal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/StudyMaterial/index.js"], "sourcesContent": ["import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport {\n  FaBook,\n  FaFileAlt,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaChevronDown,\n  FaSearch,\n  FaTimes,\n} from \"react-icons/fa\";\nimport {\n  TbFileText,\n  TbBook as TbBookIcon,\n  TbSchool,\n  <PERSON>b<PERSON><PERSON><PERSON>,\n  <PERSON>b<PERSON><PERSON><PERSON>,\n  Tb<PERSON>ortAscending,\n  Tb<PERSON><PERSON>load,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>b<PERSON><PERSON>,\n  Tb<PERSON>hevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  TbAlertTriangle,\n  TbInfoCircle,\n  TbCheck,\n  TbBooks,\n  TbCertificate\n} from \"react-icons/tb\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction StudyMaterial() {\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili, getSubjectName } = useLanguage();\n  const dispatch = useDispatch();\n\n  // Simplified CSS - no header conflicts\n  const inlineStyles = `\n    /* Mobile Layout Fixes for Study Materials */\n    @media (max-width: 768px) {\n      /* Reduce Bell Icon Size */\n      .notification-bell-button .w-5,\n      .notification-bell-button .h-5 {\n        width: 14px !important;\n        height: 14px !important;\n      }\n\n      /* Study Materials content positioning - no header conflicts */\n      .study-material-modern,\n      .container-modern {\n        padding-top: 16px !important;\n      }\n\n      /* Tabs positioning */\n      .ant-tabs-nav {\n        margin-bottom: 8px !important;\n      }\n    }\n\n      /* Force ALL sidebar text elements to be white */\n      .sidebar *,\n      .mobile-sidebar *,\n      .modern-sidebar *,\n      [class*=\"sidebar\"] *,\n      .ant-menu *,\n      .ant-menu-item *,\n      .sidebar .menu-item,\n      .mobile-sidebar .menu-item,\n      .modern-sidebar .menu-item,\n      .sidebar a,\n      .mobile-sidebar a,\n      .modern-sidebar a,\n      .sidebar span,\n      .mobile-sidebar span,\n      .modern-sidebar span,\n      .sidebar div,\n      .mobile-sidebar div,\n      .modern-sidebar div,\n      .sidebar p,\n      .mobile-sidebar p,\n      .modern-sidebar p,\n      .sidebar h1, .sidebar h2, .sidebar h3, .sidebar h4, .sidebar h5, .sidebar h6,\n      .mobile-sidebar h1, .mobile-sidebar h2, .mobile-sidebar h3, .mobile-sidebar h4, .mobile-sidebar h5, .mobile-sidebar h6,\n      .modern-sidebar h1, .modern-sidebar h2, .modern-sidebar h3, .modern-sidebar h4, .modern-sidebar h5, .modern-sidebar h6,\n      .sidebar .text-gray-100,\n      .sidebar .text-gray-200,\n      .sidebar .text-gray-300,\n      .sidebar .text-gray-400,\n      .sidebar .text-gray-500,\n      .sidebar .text-gray-600,\n      .sidebar .text-gray-700,\n      .sidebar .text-gray-800,\n      .sidebar .text-gray-900,\n      .mobile-sidebar .text-gray-100,\n      .mobile-sidebar .text-gray-200,\n      .mobile-sidebar .text-gray-300,\n      .mobile-sidebar .text-gray-400,\n      .mobile-sidebar .text-gray-500,\n      .mobile-sidebar .text-gray-600,\n      .mobile-sidebar .text-gray-700,\n      .mobile-sidebar .text-gray-800,\n      .mobile-sidebar .text-gray-900,\n      .modern-sidebar .text-gray-100,\n      .modern-sidebar .text-gray-200,\n      .modern-sidebar .text-gray-300,\n      .modern-sidebar .text-gray-400,\n      .modern-sidebar .text-gray-500,\n      .modern-sidebar .text-gray-600,\n      .modern-sidebar .text-gray-700,\n      .modern-sidebar .text-gray-800,\n      .modern-sidebar .text-gray-900 {\n        color: #ffffff !important;\n        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;\n        font-weight: 500 !important;\n      }\n\n      /* Sidebar hover effects */\n      .sidebar .menu-item:hover,\n      .mobile-sidebar .menu-item:hover,\n      .modern-sidebar .menu-item:hover,\n      .sidebar a:hover,\n      .mobile-sidebar a:hover,\n      .modern-sidebar a:hover {\n        color: #ffffff !important;\n        background: rgba(255, 255, 255, 0.2) !important;\n        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;\n      }\n\n      /* Remove Gap - Adjust Study Materials content to fit perfectly under header */\n      .study-material-modern,\n      .container-modern,\n      .layout,\n      .layout-modern,\n      main,\n      .body {\n        margin-top: 48px !important;\n        padding-top: 0px !important;\n      }\n\n      /* Study Materials specific mobile fixes - No gap */\n      .modern-header {\n        margin-top: 48px !important;\n        padding-top: 8px !important;\n        position: relative !important;\n        z-index: 1 !important;\n      }\n\n      .tabs-container {\n        position: relative !important;\n        z-index: 1 !important;\n        margin-top: 0px !important;\n        padding-top: 0px !important;\n      }\n\n      /* Study Material Content - No gaps */\n      .study-material-content,\n      .material-grid,\n      .pdf-grid,\n      .content-container {\n        margin-top: 0px !important;\n        padding-top: 8px !important;\n      }\n\n      /* Page Title and Search - Compact spacing */\n      .page-title,\n      .search-container,\n      .filter-container {\n        margin-top: 0px !important;\n        margin-bottom: 8px !important;\n        padding-top: 0px !important;\n      }\n\n      /* Tabs - Compact spacing */\n      .ant-tabs,\n      .tabs-wrapper {\n        margin-top: 0px !important;\n        padding-top: 0px !important;\n      }\n\n      .ant-tabs-nav {\n        margin-bottom: 8px !important;\n      }\n\n      .ant-tabs-content {\n        padding-top: 0px !important;\n      }\n\n      .lg\\\\:hidden button {\n        background: rgba(255, 255, 255, 0.95) !important;\n        border-radius: 8px !important;\n        padding: 8px !important;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;\n        border: 1px solid rgba(0, 0, 0, 0.1) !important;\n      }\n    }\n\n    /* Center All Modals */\n    .ant-modal,\n    .modal,\n    .pdf-modal,\n    .study-modal,\n    .material-modal {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      top: 0 !important;\n      padding-top: 0 !important;\n    }\n\n    .ant-modal-content,\n    .modal-content,\n    .pdf-modal-content,\n    .study-modal-content,\n    .material-modal-content {\n      margin: 0 auto !important;\n      position: relative !important;\n      top: auto !important;\n      transform: none !important;\n    }\n\n    .ant-modal-wrap {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      min-height: 100vh !important;\n    }\n\n    /* PDF Modal specific centering */\n    .pdf-modal-overlay {\n      display: flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      position: fixed !important;\n      top: 0 !important;\n      left: 0 !important;\n      width: 100vw !important;\n      height: 100vh !important;\n      z-index: 10000 !important;\n      padding: 20px !important;\n      box-sizing: border-box !important;\n    }\n  `;\n\n  // Add styles to document head\n  React.useEffect(() => {\n    const styleElement = document.createElement('style');\n    styleElement.textContent = inlineStyles;\n    document.head.appendChild(styleElement);\n\n    // Add additional mobile header fixes\n    const mobileHeaderFix = document.createElement('style');\n    mobileHeaderFix.textContent = `\n      @media (max-width: 768px) {\n        /* Force hamburger menu to top left */\n        .lg\\\\:hidden {\n          position: fixed !important;\n          top: 8px !important;\n          left: 8px !important;\n          z-index: 10001 !important;\n        }\n\n        /* Desktop-like header layout for mobile */\n        .nav-modern, header {\n          position: fixed !important;\n          top: 0 !important;\n          left: 0 !important;\n          right: 0 !important;\n          height: 48px !important;\n          display: flex !important;\n          align-items: center !important;\n          justify-content: space-between !important;\n          padding: 0 48px 0 16px !important;\n          z-index: 10000 !important;\n          background: rgba(255, 255, 255, 0.98) !important;\n          backdrop-filter: blur(15px) !important;\n        }\n\n        /* Header content layout */\n        .nav-modern .flex, header .flex {\n          width: 100% !important;\n          display: flex !important;\n          align-items: center !important;\n          justify-content: space-between !important;\n        }\n\n        /* Logo section */\n        .nav-modern .flex > div:first-child, header .flex > div:first-child {\n          flex: 1 !important;\n          display: flex !important;\n          align-items: center !important;\n          margin-left: 32px !important;\n        }\n\n        /* Right section (bell + profile) */\n        .nav-modern .flex > div:last-child, header .flex > div:last-child {\n          display: flex !important;\n          align-items: center !important;\n          gap: 8px !important;\n        }\n\n        /* Bell icon size */\n        .notification-bell-button .w-5, .notification-bell-button .h-5 {\n          width: 14px !important;\n          height: 14px !important;\n        }\n      }\n    `;\n    document.head.appendChild(mobileHeaderFix);\n\n    return () => {\n      document.head.removeChild(styleElement);\n      document.head.removeChild(mobileHeaderFix);\n    };\n  }, []);\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = user?.level || 'Primary';\n  const userLevelLower = useMemo(() => userLevel.toLowerCase(), [userLevel]);\n\n  const subjectsList = useMemo(() => {\n    return userLevelLower === 'primary'\n      ? primarySubjects\n      : userLevelLower === 'primary_kiswahili'\n        ? primaryKiswahiliSubjects\n        : userLevelLower === 'secondary'\n          ? secondarySubjects\n          : advanceSubjects;\n  }, [userLevelLower]);\n\n  // Debug: Log current level and subjects (removed subjectsList from dependencies to prevent infinite loop)\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, user]);\n\n  // Define all possible classes for each level (memoized to prevent re-renders)\n  const allPossibleClasses = useMemo(() => {\n    return userLevelLower === 'primary'\n      ? ['1', '2', '3', '4', '5', '6', '7']\n      : userLevelLower === 'secondary'\n        ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']\n        : ['Form-5', 'Form-6'];\n  }, [userLevelLower]);\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"study-notes\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = user?.class || user?.className;\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = user?.class || user?.className;\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes (removed subjectsList from dependencies)\n  useEffect(() => {\n    if (user?.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user?.level, selectedSubject]); // Removed subjectsList to prevent infinite loop\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" :\n        selectedClass.toString().replace(\"Form-\", \"\");\n\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject, // This can be \"all\" or a specific subject\n      };\n      if (userLevel) {\n        data.level = userLevel;\n      }\n\n      const res = await getStudyMaterial(data);\n\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = (tab) => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n\n  const handleSubjectChange = (subject) => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n\n  const handleClassChange = (className) => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material =>\n        material.title.toLowerCase().includes(searchLower) ||\n        material.subject.toLowerCase().includes(searchLower) ||\n        (material.year && material.year.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n\n\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = (documentUrl) => {\n    // Use proxy endpoint to handle CORS issues\n    const proxyUrl = `${process.env.REACT_APP_SERVER_DOMAIN}/api/study/document-proxy?url=${encodeURIComponent(documentUrl)}`;\n\n    fetch(proxyUrl, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('token')}`,\n      }\n    })\n      .then((response) => {\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        return response.blob();\n      })\n      .then((blob) => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = documentUrl.split(\"/\").pop();\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      })\n      .catch((error) => {\n        console.error(\"Error downloading the file:\", error);\n        // Fallback to direct download if proxy fails\n        window.open(documentUrl, '_blank');\n      });\n  };\n\n  const handleDocumentPreview = (documentUrl) => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  return (\n    <div\n      className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\"\n      style={{\n        marginTop: window.innerWidth <= 768 ? '48px' : '0px',\n        paddingTop: window.innerWidth <= 768 ? '0px' : '0px'\n      }}\n    >\n      {/* Header removed - using ProtectedRoute header only */}\n\n      <div\n        className=\"container-modern py-8\"\n        style={{\n          paddingTop: window.innerWidth <= 768 ? '8px' : '32px',\n          marginTop: window.innerWidth <= 768 ? '0px' : '0px'\n        }}\n      >\n        {/* Study Material Tabs */}\n        <div className=\"mb-6\">\n          <div className=\"study-tabs\">\n            {[\n              { key: 'study-notes', label: isKiswahili ? 'Maelezo' : 'Notes', icon: TbFileText },\n              { key: 'past-papers', label: isKiswahili ? 'Karatasi za Zamani' : 'Past Papers', icon: TbCertificate },\n              { key: 'books', label: isKiswahili ? 'Vitabu' : 'Books', icon: TbBookIcon }\n            ].map((tab) => (\n              <button\n                key={tab.key}\n                className={`study-tab ${activeTab === tab.key ? 'active' : ''}`}\n                onClick={() => handleTabChange(tab.key)}\n              >\n                <tab.icon />\n                <span>{tab.label}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Modern Filters Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <div className=\"card p-6\">\n            <div className=\"flex flex-col lg:flex-row gap-6 items-end\">\n              {/* Search */}\n              <div className=\"flex-1\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {isKiswahili ? 'Tafuta Vifaa' : 'Search Materials'}\n                </label>\n                <input\n                  placeholder={isKiswahili ? `Tafuta ${activeTab === 'study-notes' ? 'maelezo' : activeTab === 'past-papers' ? 'karatasi za zamani' : 'vitabu'}...` : `Search ${activeTab.replace('-', ' ')}...`}\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"form-input\"\n                />\n              </div>\n\n              {/* Class Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class'}\n                  {userCurrentClass && (\n                    <span className=\"ml-2 text-xs text-primary-600 font-medium\">\n                      ({isKiswahili ? 'Darasa lako: ' : 'Your class: '}{userLevelLower === 'primary' || userLevelLower === 'primary_kiswahili' ? (isKiswahili ? `Darasa la ${userCurrentClass}` : `Class ${userCurrentClass}`) : `Form ${userCurrentClass}`})\n                    </span>\n                  )}\n                </label>\n                <div className=\"relative\">\n                  <button\n                    onClick={toggleClassSelector}\n                    className=\"w-full input-modern flex items-center justify-between\"\n                  >\n                    <span className=\"flex items-center space-x-2\">\n                      <TbSchool className=\"w-4 h-4 text-gray-400\" />\n                      <span>\n                        {selectedClass === 'all' ? 'All Classes' :\n                          userLevelLower === 'primary'\n                            ? `Class ${selectedClass}`\n                            : `Form ${selectedClass}`\n                        }\n                      </span>\n                      {selectedClass === userCurrentClass && (\n                        <span className=\"badge-primary text-xs\">Current</span>\n                      )}\n                    </span>\n                    <TbChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  <AnimatePresence>\n                    {showClassSelector && (\n                      <motion.div\n                        initial={{ opacity: 0, y: -10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        exit={{ opacity: 0, y: -10 }}\n                        className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\"\n                      >\n                        <button\n                          className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${\n                            selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                          }`}\n                          onClick={() => handleClassChange('all')}\n                        >\n                          All Classes\n                        </button>\n                        {availableClasses.map((className, index) => (\n                          <button\n                            key={index}\n                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${\n                              selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                            }`}\n                            onClick={() => handleClassChange(className)}\n                          >\n                            <span>\n                              {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}\n                            </span>\n                            {className === userCurrentClass && (\n                              <span className=\"badge-success text-xs\">Your Class</span>\n                            )}\n                          </button>\n                        ))}\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              </div>\n\n              {/* Subject Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {isKiswahili ? 'Chuja kwa Somo' : 'Filter by Subject'}\n                </label>\n                <select\n                  value={selectedSubject}\n                  onChange={(e) => handleSubjectChange(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n                  {subjectsList.map((subject, index) => (\n                    <option key={index} value={subject}>\n                      {getSubjectName(subject)}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Sort */}\n              <div className=\"w-full lg:w-48\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {isKiswahili ? 'Panga kwa' : 'Sort by'}\n                </label>\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                  <option value=\"title\">By Title</option>\n                </select>\n              </div>\n\n              {/* Clear Filters */}\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => {\n                  setSearchTerm(\"\");\n                  setSelectedClass(\"all\");\n                  setSelectedSubject(\"all\");\n                  setSortBy(\"newest\");\n                }}\n              >\n                Clear Filters\n              </button>\n            </div>\n\n            {/* Results Count */}\n            {(searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && (\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                <span className=\"text-sm text-gray-600\">\n                  Showing {filteredAndSortedMaterials.length} of {materials.length} {activeTab.replace('-', ' ')}\n                </span>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n      {/* Materials Display */}\n      <div className=\"materials-section\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading materials...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <FaTimes className=\"error-icon\" />\n            <h3>Error Loading Materials</h3>\n            <p>{error}</p>\n            <button\n              className=\"retry-btn\"\n              onClick={() => {\n                setError(null);\n                fetchMaterials();\n              }}\n            >\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedMaterials.length > 0 ? (\n          <div className={activeTab === 'past-papers'\n            ? \"flex flex-wrap gap-3 justify-start\"\n            : \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\"\n          }>\n            {filteredAndSortedMaterials.map((material, index) => (\n              <div key={index} className={activeTab === 'past-papers'\n                ? \"past-paper-square-card\"\n                : \"study-card\"\n              }>\n                {activeTab === 'past-papers' ? (\n                  // Past Papers Layout - Buttons below tags\n                  <>\n                    <div className=\"study-card-header\">\n                      <div className=\"study-card-meta\">\n                        <FaFileAlt />\n                        <span>{isKiswahili ? 'Karatasi ya Zamani' : 'Past Paper'}</span>\n                      </div>\n                      <div className=\"study-card-title\">\n                        {material.title}\n                      </div>\n                    </div>\n\n                    <div className=\"card-content\">\n                      <div className=\"material-meta\">\n                        <span className=\"material-subject\">{material.subject}</span>\n                        {material.className && (\n                          <span className=\"material-class\">\n                            {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                          </span>\n                        )}\n                        {material.year && (\n                          <span className=\"badge badge-secondary\">{material.year}</span>\n                        )}\n                      </div>\n                    </div>\n\n                    <div className=\"card-actions\">\n                      {material.documentUrl ? (\n                        <>\n                          <button\n                            className=\"btn-primary\"\n                            onClick={() => handleDocumentPreview(material.documentUrl)}\n                          >\n                            <FaEye /> {isKiswahili ? 'Ona' : 'View'}\n                          </button>\n                          <button\n                            className=\"btn-primary\"\n                            onClick={() => handleDocumentDownload(material.documentUrl)}\n                          >\n                            <FaDownload /> {isKiswahili ? 'Pakua' : 'Download'}\n                          </button>\n                        </>\n                      ) : (\n                        <span className=\"unavailable\">{isKiswahili ? 'Haipatikani' : 'Not available'}</span>\n                      )}\n                    </div>\n                  </>\n                ) : (\n                  // Regular Layout for other tabs\n                  <>\n                    <div className=\"study-card-header\">\n                      <div className=\"study-card-meta\">\n                        {activeTab === 'study-notes' && <FaFileAlt />}\n                        {activeTab === 'books' && <FaBook />}\n                        <span>\n                          {activeTab === 'study-notes' ? (isKiswahili ? 'Maelezo' : 'Note') :\n                           (isKiswahili ? 'Kitabu' : 'Book')}\n                        </span>\n                      </div>\n                      <div className=\"study-card-title\">\n                        {material.title}\n                      </div>\n                      {material.year && (\n                        <span className=\"badge badge-secondary mt-2\">{material.year}</span>\n                      )}\n                    </div>\n\n                    <div className=\"card-content\">\n                      <h3 className=\"material-title\">{material.title}</h3>\n                      <div className=\"material-meta\">\n                        <span className=\"material-subject\">{material.subject}</span>\n                        {material.className && (\n                          <span className=\"material-class\">\n                            {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n\n                    <div className=\"card-actions\">\n                      {material.documentUrl ? (\n                        <>\n                          <button\n                            className=\"action-btn secondary\"\n                            onClick={() => handleDocumentPreview(material.documentUrl)}\n                          >\n                            <FaEye /> {isKiswahili ? 'Ona' : 'View'}\n                          </button>\n                          <button\n                            className=\"action-btn primary\"\n                            onClick={() => handleDocumentDownload(material.documentUrl)}\n                          >\n                            <FaDownload /> {isKiswahili ? 'Pakua' : 'Download'}\n                          </button>\n                        </>\n                      ) : (\n                        <span className=\"unavailable\">{isKiswahili ? 'Haipatikani' : 'Not available'}</span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Materials Found</h3>\n            <p>No study materials are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n\n\n\n      {/* PDF Modal */}\n      <PDFModal\n        modalIsOpen={modalIsOpen}\n        closeModal={() => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        }}\n        documentUrl={documentUrl}\n      />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterial;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,QAAQ,mCAAmC;AAC/D;;AAEA,OAAOC,QAAQ,MAAM,YAAY;AACjC,SACEC,MAAM,EACNC,SAAS,EACTC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,aAAa,EACbC,QAAQ,EACRC,OAAO,QACF,gBAAgB;AACvB,SACEC,UAAU,EACVC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,aAAa,IAAIC,iBAAiB,EAClCC,WAAW,EACXC,GAAG,EACHC,eAAe,EACfC,YAAY,EACZC,OAAO,EACPC,OAAO,EACPC,aAAa,QACR,gBAAgB;AACvB,SAASC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE3H,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAG3C,WAAW,CAAE4C,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAG5C,WAAW,CAAC,CAAC;EACxD,MAAM6C,QAAQ,GAAGjD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMkD,YAAY,GAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;EAED;EACA1D,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMyD,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACpDF,YAAY,CAACG,WAAW,GAAGJ,YAAY;IACvCE,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,YAAY,CAAC;;IAEvC;IACA,MAAMM,eAAe,GAAGL,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACvDI,eAAe,CAACH,WAAW,GAAI;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACC,eAAe,CAAC;IAE1C,OAAO,MAAM;MACXL,QAAQ,CAACG,IAAI,CAACG,WAAW,CAACP,YAAY,CAAC;MACvCC,QAAQ,CAACG,IAAI,CAACG,WAAW,CAACD,eAAe,CAAC;IAC5C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,SAAS,GAAG,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,KAAK,KAAI,SAAS;EAC1C,MAAMC,cAAc,GAAGjE,OAAO,CAAC,MAAM+D,SAAS,CAACG,WAAW,CAAC,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;EAE1E,MAAMI,YAAY,GAAGnE,OAAO,CAAC,MAAM;IACjC,OAAOiE,cAAc,KAAK,SAAS,GAC/B3B,eAAe,GACf2B,cAAc,KAAK,mBAAmB,GACpC1B,wBAAwB,GACxB0B,cAAc,KAAK,WAAW,GAC5BzB,iBAAiB,GACjBC,eAAe;EACzB,CAAC,EAAE,CAACwB,cAAc,CAAC,CAAC;;EAEpB;EACAnE,SAAS,CAAC,MAAM;IACdsE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEN,SAAS,CAAC;IAC1DK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,cAAc,CAAC;IAC3EG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEF,YAAY,CAAC;IAChEC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAErB,IAAI,CAAC;EACtD,CAAC,EAAE,CAACe,SAAS,EAAEE,cAAc,EAAEjB,IAAI,CAAC,CAAC;;EAErC;EACA,MAAMsB,kBAAkB,GAAGtE,OAAO,CAAC,MAAM;IACvC,OAAOiE,cAAc,KAAK,SAAS,GAC/B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GACnCA,cAAc,KAAK,WAAW,GAC5B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,GACxC,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,CAAAmD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK,MAAI3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,SAAS,KAAI,KAAK,CAAC;EAC3F,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAMkF,gBAAgB,GAAG,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK,MAAI3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,SAAS;EACvD,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqF,SAAS,EAAEC,YAAY,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuF,KAAK,EAAEC,QAAQ,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACyF,WAAW,EAAEC,cAAc,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2F,WAAW,EAAEC,cAAc,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC+F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;;EAGjE;EACA,MAAM,CAACiG,UAAU,EAAEC,aAAa,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmG,MAAM,EAAEC,SAAS,CAAC,GAAGpG,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoG,SAAS,GAAG,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK,MAAI3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,SAAS;IAChD,IAAIsB,SAAS,IAAIzB,aAAa,KAAK,KAAK,IAAI,CAACiB,gBAAgB,CAACS,MAAM,EAAE;MACpEzB,gBAAgB,CAACwB,SAAS,CAAC;IAC7B;EACF,CAAC,EAAE,CAAClD,IAAI,EAAEyB,aAAa,EAAEiB,gBAAgB,CAACS,MAAM,CAAC,CAAC;;EAElD;EACArG,SAAS,CAAC,MAAM;IACd,IAAIkD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgB,KAAK,EAAE;MACf;MACA,MAAMoC,cAAc,GAAGjC,YAAY,CAACkC,QAAQ,CAACxB,eAAe,CAAC;MAC7D,IAAI,CAACuB,cAAc,IAAIvB,eAAe,KAAK,KAAK,EAAE;QAChDT,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjES,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAAC9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,KAAK,EAAEa,eAAe,CAAC,CAAC,CAAC,CAAC;;EAEpC;EACA,MAAMyB,2BAA2B,GAAGvG,WAAW,CAAC,MAAM;IACpD4F,mBAAmB,CAACrB,kBAAkB,CAAC;EACzC,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAMiC,cAAc,GAAGxG,WAAW,CAAC,YAAY;IAC7C,IAAI,CAACwE,SAAS,IAAIE,aAAa,KAAK,SAAS,EAAE;MAC7C;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IACdhC,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;IAEvB,IAAI;MACF;MACA,MAAMiG,mBAAmB,GAAG/B,aAAa,KAAK,KAAK,GAAG,KAAK,GACzDA,aAAa,CAACgC,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAE/C,MAAMC,IAAI,GAAG;QACXC,OAAO,EAAErC,SAAS;QAClBK,SAAS,EAAE4B,mBAAmB;QAC9BK,OAAO,EAAEhC,eAAe,CAAE;MAC5B,CAAC;;MACD,IAAId,SAAS,EAAE;QACb4C,IAAI,CAAC3C,KAAK,GAAGD,SAAS;MACxB;MAEA,MAAM+C,GAAG,GAAG,MAAM3G,gBAAgB,CAACwG,IAAI,CAAC;MAExC,IAAIG,GAAG,CAACC,MAAM,KAAK,GAAG,IAAID,GAAG,CAACH,IAAI,CAACK,OAAO,EAAE;QAC1C,MAAMhC,SAAS,GAAG8B,GAAG,CAACH,IAAI,CAACA,IAAI,KAAK,OAAO,GAAG,EAAE,GAAGG,GAAG,CAACH,IAAI,CAACA,IAAI;QAChE1B,YAAY,CAACD,SAAS,CAAC;MACzB,CAAC,MAAM;QACLC,YAAY,CAAC,EAAE,CAAC;QAChBI,QAAQ,CAAE,mBAAkBd,SAAU,qBAAoB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDH,YAAY,CAAC,EAAE,CAAC;MAChBI,QAAQ,CAAE,kBAAiBd,SAAU,+CAA8C,CAAC;IACtF,CAAC,SAAS;MACRY,YAAY,CAAC,KAAK,CAAC;MACnB9B,QAAQ,CAAC/C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACiE,SAAS,EAAEE,aAAa,EAAEI,eAAe,EAAEd,SAAS,EAAEV,QAAQ,CAAC,CAAC;;EAEpE;EACAvD,SAAS,CAAC,MAAM;IACd,IAAIkD,IAAI,IAAIe,SAAS,EAAE;MACrBuC,2BAA2B,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACtD,IAAI,EAAEe,SAAS,EAAEuC,2BAA2B,CAAC,CAAC;;EAElD;EACAxG,SAAS,CAAC,MAAM;IACd;IACA,IAAIkD,IAAI,IAAIe,SAAS,IAAIQ,SAAS,IAAIE,aAAa,IAAIA,aAAa,KAAK,SAAS,EAAE;MAClF8B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACvD,IAAI,EAAEe,SAAS,EAAEQ,SAAS,EAAEE,aAAa,EAAEI,eAAe,EAAE0B,cAAc,CAAC,CAAC;;EAEhF;EACA,MAAMU,eAAe,GAAIC,GAAG,IAAK;IAC/BjC,YAAY,CAAC,EAAE,CAAC;IAChBT,YAAY,CAAC0C,GAAG,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBE,SAAS,CAAC,QAAQ,CAAC;EACrB,CAAC;EAED,MAAMkB,mBAAmB,GAAIN,OAAO,IAAK;IACvC5B,YAAY,CAAC,EAAE,CAAC;IAChBH,kBAAkB,CAAC+B,OAAO,CAAC;IAC3Bd,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMqB,iBAAiB,GAAIxC,SAAS,IAAK;IACvCK,YAAY,CAAC,EAAE,CAAC;IAChBP,gBAAgB,CAACE,SAAS,CAAC;IAC3BiB,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;IAChCxB,oBAAoB,CAAC,CAACD,iBAAiB,CAAC;EAC1C,CAAC;;EAED;EACA,MAAM0B,0BAA0B,GAAGtH,OAAO,CAAC,MAAM;IAC/C,IAAI,CAACgF,SAAS,IAAIA,SAAS,CAACmB,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO,EAAE;IACX;IAEA,IAAIoB,QAAQ,GAAGvC,SAAS;;IAExB;IACA,IAAIc,UAAU,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAG3B,UAAU,CAAC5B,WAAW,CAAC,CAAC;MAC5CqD,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IACjCA,QAAQ,CAACC,KAAK,CAAC1D,WAAW,CAAC,CAAC,CAACmC,QAAQ,CAACoB,WAAW,CAAC,IAClDE,QAAQ,CAACd,OAAO,CAAC3C,WAAW,CAAC,CAAC,CAACmC,QAAQ,CAACoB,WAAW,CAAC,IACnDE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC3D,WAAW,CAAC,CAAC,CAACmC,QAAQ,CAACoB,WAAW,CACpE,CAAC;IACH;;IAEA;IACAF,QAAQ,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIhC,MAAM,KAAK,QAAQ,EAAE;QACvB;QACA,IAAI+B,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE;UACpB,OAAOI,QAAQ,CAACD,CAAC,CAACH,IAAI,CAAC,GAAGI,QAAQ,CAACF,CAAC,CAACF,IAAI,CAAC;QAC5C;;QAEA;QAAA,KACK,IAAIE,CAAC,CAACF,IAAI,IAAI,CAACG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KACjC,IAAI,CAACE,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,KAChC,OAAO,CAAC;MACf,CAAC,MAAM,IAAI7B,MAAM,KAAK,QAAQ,EAAE;QAC9B;QACA,IAAI+B,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE;UACpB,OAAOI,QAAQ,CAACF,CAAC,CAACF,IAAI,CAAC,GAAGI,QAAQ,CAACD,CAAC,CAACH,IAAI,CAAC;QAC5C;;QAEA;QAAA,KACK,IAAIE,CAAC,CAACF,IAAI,IAAI,CAACG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KACjC,IAAI,CAACE,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,KAChC,OAAO,CAAC;MACf,CAAC,MAAM;QACL;QACA,OAAOE,CAAC,CAACH,KAAK,CAACM,aAAa,CAACF,CAAC,CAACJ,KAAK,CAAC;MACvC;IACF,CAAC,CAAC;IAIF,OAAOL,QAAQ;EACjB,CAAC,EAAE,CAACvC,SAAS,EAAEc,UAAU,EAAEE,MAAM,EAAEzB,SAAS,CAAC,CAAC;;EAE9C;EACA,MAAM4D,sBAAsB,GAAI3C,WAAW,IAAK;IAC9C;IACA,MAAM4C,QAAQ,GAAI,GAAEC,OAAO,CAACC,GAAG,CAACC,uBAAwB,iCAAgCC,kBAAkB,CAAChD,WAAW,CAAE,EAAC;IAEzHiD,KAAK,CAACL,QAAQ,EAAE;MACdM,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;MAC3D;IACF,CAAC,CAAC,CACCC,IAAI,CAAEC,QAAQ,IAAK;MAClB,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsBF,QAAQ,CAAChC,MAAO,EAAC,CAAC;MAC3D;MACA,OAAOgC,QAAQ,CAACG,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC,CACDJ,IAAI,CAAEI,IAAI,IAAK;MACd,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMnB,CAAC,GAAGvE,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCsE,CAAC,CAACwB,IAAI,GAAGJ,GAAG;MACZpB,CAAC,CAACyB,QAAQ,GAAGhE,WAAW,CAACiE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MACzClG,QAAQ,CAACmG,IAAI,CAAC/F,WAAW,CAACmE,CAAC,CAAC;MAC5BA,CAAC,CAAC6B,KAAK,CAAC,CAAC;MACTpG,QAAQ,CAACmG,IAAI,CAAC7F,WAAW,CAACiE,CAAC,CAAC;MAC5BqB,MAAM,CAACC,GAAG,CAACQ,eAAe,CAACV,GAAG,CAAC;IACjC,CAAC,CAAC,CACDW,KAAK,CAAE1E,KAAK,IAAK;MAChBhB,OAAO,CAACgB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD;MACAgE,MAAM,CAACW,IAAI,CAACvE,WAAW,EAAE,QAAQ,CAAC;IACpC,CAAC,CAAC;EACN,CAAC;EAED,MAAMwE,qBAAqB,GAAIxE,WAAW,IAAK;IAC7CC,cAAc,CAACD,WAAW,CAAC;IAC3BD,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAwBD,oBACE5C,OAAA;IACEiC,SAAS,EAAC,wDAAwD;IAClEqF,KAAK,EAAE;MACLC,SAAS,EAAEd,MAAM,CAACe,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,KAAK;MACpDC,UAAU,EAAEhB,MAAM,CAACe,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;IACjD,CAAE;IAAAE,QAAA,eAIF1H,OAAA;MACEiC,SAAS,EAAC,uBAAuB;MACjCqF,KAAK,EAAE;QACLG,UAAU,EAAEhB,MAAM,CAACe,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;QACrDD,SAAS,EAAEd,MAAM,CAACe,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;MAChD,CAAE;MAAAE,QAAA,gBAGF1H,OAAA;QAAKiC,SAAS,EAAC,MAAM;QAAAyF,QAAA,eACnB1H,OAAA;UAAKiC,SAAS,EAAC,YAAY;UAAAyF,QAAA,EACxB,CACC;YAAEC,GAAG,EAAE,aAAa;YAAEC,KAAK,EAAEpH,WAAW,GAAG,SAAS,GAAG,OAAO;YAAEqH,IAAI,EAAEtJ;UAAW,CAAC,EAClF;YAAEoJ,GAAG,EAAE,aAAa;YAAEC,KAAK,EAAEpH,WAAW,GAAG,oBAAoB,GAAG,aAAa;YAAEqH,IAAI,EAAEnI;UAAc,CAAC,EACtG;YAAEiI,GAAG,EAAE,OAAO;YAAEC,KAAK,EAAEpH,WAAW,GAAG,QAAQ,GAAG,OAAO;YAAEqH,IAAI,EAAEpJ;UAAW,CAAC,CAC5E,CAACqJ,GAAG,CAAEvD,GAAG,iBACRvE,OAAA;YAEEiC,SAAS,EAAG,aAAYL,SAAS,KAAK2C,GAAG,CAACoD,GAAG,GAAG,QAAQ,GAAG,EAAG,EAAE;YAChEI,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAACC,GAAG,CAACoD,GAAG,CAAE;YAAAD,QAAA,gBAExC1H,OAAA,CAACuE,GAAG,CAACsD,IAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACZnI,OAAA;cAAA0H,QAAA,EAAOnD,GAAG,CAACqD;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GALnB5D,GAAG,CAACoD,GAAG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMN,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnI,OAAA,CAAC1C,MAAM,CAAC8K,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BzG,SAAS,EAAC,MAAM;QAAAyF,QAAA,eAEhB1H,OAAA;UAAKiC,SAAS,EAAC,UAAU;UAAAyF,QAAA,gBACvB1H,OAAA;YAAKiC,SAAS,EAAC,2CAA2C;YAAAyF,QAAA,gBAExD1H,OAAA;cAAKiC,SAAS,EAAC,QAAQ;cAAAyF,QAAA,gBACrB1H,OAAA;gBAAOiC,SAAS,EAAC,8CAA8C;gBAAAyF,QAAA,EAC5DlH,WAAW,GAAG,cAAc,GAAG;cAAkB;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACRnI,OAAA;gBACE2I,WAAW,EAAEnI,WAAW,GAAI,UAASoB,SAAS,KAAK,aAAa,GAAG,SAAS,GAAGA,SAAS,KAAK,aAAa,GAAG,oBAAoB,GAAG,QAAS,KAAI,GAAI,UAASA,SAAS,CAACmC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAE,KAAK;gBAC/L6E,KAAK,EAAEzF,UAAW;gBAClB0F,QAAQ,EAAGC,CAAC,IAAK1F,aAAa,CAAC0F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/C3G,SAAS,EAAC;cAAY;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNnI,OAAA;cAAKiC,SAAS,EAAC,gBAAgB;cAAAyF,QAAA,gBAC7B1H,OAAA;gBAAOiC,SAAS,EAAC,8CAA8C;gBAAAyF,QAAA,GAC5DlH,WAAW,GAAG,kBAAkB,GAAG,iBAAiB,EACpD4B,gBAAgB,iBACfpC,OAAA;kBAAMiC,SAAS,EAAC,2CAA2C;kBAAAyF,QAAA,GAAC,GACzD,EAAClH,WAAW,GAAG,eAAe,GAAG,cAAc,EAAEc,cAAc,KAAK,SAAS,IAAIA,cAAc,KAAK,mBAAmB,GAAId,WAAW,GAAI,aAAY4B,gBAAiB,EAAC,GAAI,SAAQA,gBAAiB,EAAC,GAAK,QAAOA,gBAAiB,EAAC,EAAC,GACxO;gBAAA;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACRnI,OAAA;gBAAKiC,SAAS,EAAC,UAAU;gBAAAyF,QAAA,gBACvB1H,OAAA;kBACE+H,OAAO,EAAErD,mBAAoB;kBAC7BzC,SAAS,EAAC,uDAAuD;kBAAAyF,QAAA,gBAEjE1H,OAAA;oBAAMiC,SAAS,EAAC,6BAA6B;oBAAAyF,QAAA,gBAC3C1H,OAAA,CAACtB,QAAQ;sBAACuD,SAAS,EAAC;oBAAuB;sBAAA+F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9CnI,OAAA;sBAAA0H,QAAA,EACG5F,aAAa,KAAK,KAAK,GAAG,aAAa,GACtCR,cAAc,KAAK,SAAS,GACvB,SAAQQ,aAAc,EAAC,GACvB,QAAOA,aAAc;oBAAC;sBAAAkG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEzB,CAAC,EACNrG,aAAa,KAAKM,gBAAgB,iBACjCpC,OAAA;sBAAMiC,SAAS,EAAC,uBAAuB;sBAAAyF,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACtD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACPnI,OAAA,CAACb,iBAAiB;oBAAC8C,SAAS,EAAG,8CAA6CgB,iBAAiB,GAAG,YAAY,GAAG,EAAG;kBAAE;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjH,CAAC,eAETnI,OAAA,CAACzC,eAAe;kBAAAmK,QAAA,EACbzE,iBAAiB,iBAChBjD,OAAA,CAAC1C,MAAM,CAAC8K,GAAG;oBACTC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChCC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BS,IAAI,EAAE;sBAAEV,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAC7BtG,SAAS,EAAC,0HAA0H;oBAAAyF,QAAA,gBAEpI1H,OAAA;sBACEiC,SAAS,EAAG,iEACVH,aAAa,KAAK,KAAK,GAAG,4CAA4C,GAAG,eAC1E,EAAE;sBACHiG,OAAO,EAAEA,CAAA,KAAMtD,iBAAiB,CAAC,KAAK,CAAE;sBAAAiD,QAAA,EACzC;oBAED;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRpF,gBAAgB,CAAC+E,GAAG,CAAC,CAAC7F,SAAS,EAAEgH,KAAK,kBACrCjJ,OAAA;sBAEEiC,SAAS,EAAG,mGACVH,aAAa,KAAKG,SAAS,GAAG,4CAA4C,GAAG,eAC9E,EAAE;sBACH8F,OAAO,EAAEA,CAAA,KAAMtD,iBAAiB,CAACxC,SAAS,CAAE;sBAAAyF,QAAA,gBAE5C1H,OAAA;wBAAA0H,QAAA,EACGpG,cAAc,KAAK,SAAS,GAAI,SAAQW,SAAU,EAAC,GAAI,QAAOA,SAAU;sBAAC;wBAAA+F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtE,CAAC,EACNlG,SAAS,KAAKG,gBAAgB,iBAC7BpC,OAAA;wBAAMiC,SAAS,EAAC,uBAAuB;wBAAAyF,QAAA,EAAC;sBAAU;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACzD;oBAAA,GAXIc,KAAK;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAYJ,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACc,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnI,OAAA;cAAKiC,SAAS,EAAC,gBAAgB;cAAAyF,QAAA,gBAC7B1H,OAAA;gBAAOiC,SAAS,EAAC,8CAA8C;gBAAAyF,QAAA,EAC5DlH,WAAW,GAAG,gBAAgB,GAAG;cAAmB;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACRnI,OAAA;gBACE4I,KAAK,EAAE1G,eAAgB;gBACvB2G,QAAQ,EAAGC,CAAC,IAAKtE,mBAAmB,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACrD3G,SAAS,EAAC,cAAc;gBAAAyF,QAAA,gBAExB1H,OAAA;kBAAQ4I,KAAK,EAAC,KAAK;kBAAAlB,QAAA,EAAElH,WAAW,GAAG,aAAa,GAAG;gBAAc;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,EAC1E3G,YAAY,CAACsG,GAAG,CAAC,CAAC5D,OAAO,EAAE+E,KAAK,kBAC/BjJ,OAAA;kBAAoB4I,KAAK,EAAE1E,OAAQ;kBAAAwD,QAAA,EAChCjH,cAAc,CAACyD,OAAO;gBAAC,GADb+E,KAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNnI,OAAA;cAAKiC,SAAS,EAAC,gBAAgB;cAAAyF,QAAA,gBAC7B1H,OAAA;gBAAOiC,SAAS,EAAC,8CAA8C;gBAAAyF,QAAA,EAC5DlH,WAAW,GAAG,WAAW,GAAG;cAAS;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACRnI,OAAA;gBACE4I,KAAK,EAAEvF,MAAO;gBACdwF,QAAQ,EAAGC,CAAC,IAAKxF,SAAS,CAACwF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC3C3G,SAAS,EAAC,cAAc;gBAAAyF,QAAA,gBAExB1H,OAAA;kBAAQ4I,KAAK,EAAC,QAAQ;kBAAAlB,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CnI,OAAA;kBAAQ4I,KAAK,EAAC,QAAQ;kBAAAlB,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CnI,OAAA;kBAAQ4I,KAAK,EAAC,OAAO;kBAAAlB,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNnI,OAAA;cACEiC,SAAS,EAAC,mBAAmB;cAC7B8F,OAAO,EAAEA,CAAA,KAAM;gBACb3E,aAAa,CAAC,EAAE,CAAC;gBACjBrB,gBAAgB,CAAC,KAAK,CAAC;gBACvBI,kBAAkB,CAAC,KAAK,CAAC;gBACzBmB,SAAS,CAAC,QAAQ,CAAC;cACrB,CAAE;cAAAoE,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGL,CAAChF,UAAU,IAAIrB,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,kBAClElC,OAAA;YAAKiC,SAAS,EAAC,oCAAoC;YAAAyF,QAAA,eACjD1H,OAAA;cAAMiC,SAAS,EAAC,uBAAuB;cAAAyF,QAAA,GAAC,UAC9B,EAAC/C,0BAA0B,CAACnB,MAAM,EAAC,MAAI,EAACnB,SAAS,CAACmB,MAAM,EAAC,GAAC,EAAC5B,SAAS,CAACmC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGfnI,OAAA;QAAKiC,SAAS,EAAC,mBAAmB;QAAAyF,QAAA,EAC/BnF,SAAS,gBACRvC,OAAA;UAAKiC,SAAS,EAAC,eAAe;UAAAyF,QAAA,gBAC5B1H,OAAA;YAAKiC,SAAS,EAAC;UAAiB;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvCnI,OAAA;YAAA0H,QAAA,EAAG;UAAoB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,GACJ1F,KAAK,gBACPzC,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAyF,QAAA,gBAC1B1H,OAAA,CAAC1B,OAAO;YAAC2D,SAAS,EAAC;UAAY;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClCnI,OAAA;YAAA0H,QAAA,EAAI;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCnI,OAAA;YAAA0H,QAAA,EAAIjF;UAAK;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdnI,OAAA;YACEiC,SAAS,EAAC,WAAW;YACrB8F,OAAO,EAAEA,CAAA,KAAM;cACbrF,QAAQ,CAAC,IAAI,CAAC;cACdkB,cAAc,CAAC,CAAC;YAClB,CAAE;YAAA8D,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,GACJxD,0BAA0B,CAACnB,MAAM,GAAG,CAAC,gBACvCxD,OAAA;UAAKiC,SAAS,EAAEL,SAAS,KAAK,aAAa,GACvC,oCAAoC,GACpC,sDACH;UAAA8F,QAAA,EACE/C,0BAA0B,CAACmD,GAAG,CAAC,CAAC9C,QAAQ,EAAEiE,KAAK,kBAC9CjJ,OAAA;YAAiBiC,SAAS,EAAEL,SAAS,KAAK,aAAa,GACnD,wBAAwB,GACxB,YACH;YAAA8F,QAAA,EACE9F,SAAS,KAAK,aAAa;YAAA;YAC1B;YACA5B,OAAA,CAAAE,SAAA;cAAAwH,QAAA,gBACE1H,OAAA;gBAAKiC,SAAS,EAAC,mBAAmB;gBAAAyF,QAAA,gBAChC1H,OAAA;kBAAKiC,SAAS,EAAC,iBAAiB;kBAAAyF,QAAA,gBAC9B1H,OAAA,CAAChC,SAAS;oBAAAgK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACbnI,OAAA;oBAAA0H,QAAA,EAAOlH,WAAW,GAAG,oBAAoB,GAAG;kBAAY;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNnI,OAAA;kBAAKiC,SAAS,EAAC,kBAAkB;kBAAAyF,QAAA,EAC9B1C,QAAQ,CAACC;gBAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnI,OAAA;gBAAKiC,SAAS,EAAC,cAAc;gBAAAyF,QAAA,eAC3B1H,OAAA;kBAAKiC,SAAS,EAAC,eAAe;kBAAAyF,QAAA,gBAC5B1H,OAAA;oBAAMiC,SAAS,EAAC,kBAAkB;oBAAAyF,QAAA,EAAE1C,QAAQ,CAACd;kBAAO;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC3DnD,QAAQ,CAAC/C,SAAS,iBACjBjC,OAAA;oBAAMiC,SAAS,EAAC,gBAAgB;oBAAAyF,QAAA,EAC7BpG,cAAc,KAAK,SAAS,GAAI,SAAQ0D,QAAQ,CAAC/C,SAAU,EAAC,GAAI,QAAO+C,QAAQ,CAAC/C,SAAU;kBAAC;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CACP,EACAnD,QAAQ,CAACE,IAAI,iBACZlF,OAAA;oBAAMiC,SAAS,EAAC,uBAAuB;oBAAAyF,QAAA,EAAE1C,QAAQ,CAACE;kBAAI;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAC9D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnI,OAAA;gBAAKiC,SAAS,EAAC,cAAc;gBAAAyF,QAAA,EAC1B1C,QAAQ,CAACnC,WAAW,gBACnB7C,OAAA,CAAAE,SAAA;kBAAAwH,QAAA,gBACE1H,OAAA;oBACEiC,SAAS,EAAC,aAAa;oBACvB8F,OAAO,EAAEA,CAAA,KAAMV,qBAAqB,CAACrC,QAAQ,CAACnC,WAAW,CAAE;oBAAA6E,QAAA,gBAE3D1H,OAAA,CAAC7B,KAAK;sBAAA6J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAAC3H,WAAW,GAAG,KAAK,GAAG,MAAM;kBAAA;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACTnI,OAAA;oBACEiC,SAAS,EAAC,aAAa;oBACvB8F,OAAO,EAAEA,CAAA,KAAMvC,sBAAsB,CAACR,QAAQ,CAACnC,WAAW,CAAE;oBAAA6E,QAAA,gBAE5D1H,OAAA,CAAC9B,UAAU;sBAAA8J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAAC3H,WAAW,GAAG,OAAO,GAAG,UAAU;kBAAA;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA,eACT,CAAC,gBAEHnI,OAAA;kBAAMiC,SAAS,EAAC,aAAa;kBAAAyF,QAAA,EAAElH,WAAW,GAAG,aAAa,GAAG;gBAAe;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cACpF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,eACN,CAAC;YAAA;YAEH;YACAnI,OAAA,CAAAE,SAAA;cAAAwH,QAAA,gBACE1H,OAAA;gBAAKiC,SAAS,EAAC,mBAAmB;gBAAAyF,QAAA,gBAChC1H,OAAA;kBAAKiC,SAAS,EAAC,iBAAiB;kBAAAyF,QAAA,GAC7B9F,SAAS,KAAK,aAAa,iBAAI5B,OAAA,CAAChC,SAAS;oBAAAgK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC5CvG,SAAS,KAAK,OAAO,iBAAI5B,OAAA,CAACjC,MAAM;oBAAAiK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpCnI,OAAA;oBAAA0H,QAAA,EACG9F,SAAS,KAAK,aAAa,GAAIpB,WAAW,GAAG,SAAS,GAAG,MAAM,GAC9DA,WAAW,GAAG,QAAQ,GAAG;kBAAO;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNnI,OAAA;kBAAKiC,SAAS,EAAC,kBAAkB;kBAAAyF,QAAA,EAC9B1C,QAAQ,CAACC;gBAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,EACLnD,QAAQ,CAACE,IAAI,iBACZlF,OAAA;kBAAMiC,SAAS,EAAC,4BAA4B;kBAAAyF,QAAA,EAAE1C,QAAQ,CAACE;gBAAI;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACnE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENnI,OAAA;gBAAKiC,SAAS,EAAC,cAAc;gBAAAyF,QAAA,gBAC3B1H,OAAA;kBAAIiC,SAAS,EAAC,gBAAgB;kBAAAyF,QAAA,EAAE1C,QAAQ,CAACC;gBAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpDnI,OAAA;kBAAKiC,SAAS,EAAC,eAAe;kBAAAyF,QAAA,gBAC5B1H,OAAA;oBAAMiC,SAAS,EAAC,kBAAkB;oBAAAyF,QAAA,EAAE1C,QAAQ,CAACd;kBAAO;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC3DnD,QAAQ,CAAC/C,SAAS,iBACjBjC,OAAA;oBAAMiC,SAAS,EAAC,gBAAgB;oBAAAyF,QAAA,EAC7BpG,cAAc,KAAK,SAAS,GAAI,SAAQ0D,QAAQ,CAAC/C,SAAU,EAAC,GAAI,QAAO+C,QAAQ,CAAC/C,SAAU;kBAAC;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnI,OAAA;gBAAKiC,SAAS,EAAC,cAAc;gBAAAyF,QAAA,EAC1B1C,QAAQ,CAACnC,WAAW,gBACnB7C,OAAA,CAAAE,SAAA;kBAAAwH,QAAA,gBACE1H,OAAA;oBACEiC,SAAS,EAAC,sBAAsB;oBAChC8F,OAAO,EAAEA,CAAA,KAAMV,qBAAqB,CAACrC,QAAQ,CAACnC,WAAW,CAAE;oBAAA6E,QAAA,gBAE3D1H,OAAA,CAAC7B,KAAK;sBAAA6J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAAC3H,WAAW,GAAG,KAAK,GAAG,MAAM;kBAAA;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACTnI,OAAA;oBACEiC,SAAS,EAAC,oBAAoB;oBAC9B8F,OAAO,EAAEA,CAAA,KAAMvC,sBAAsB,CAACR,QAAQ,CAACnC,WAAW,CAAE;oBAAA6E,QAAA,gBAE5D1H,OAAA,CAAC9B,UAAU;sBAAA8J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAAC3H,WAAW,GAAG,OAAO,GAAG,UAAU;kBAAA;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA,eACT,CAAC,gBAEHnI,OAAA;kBAAMiC,SAAS,EAAC,aAAa;kBAAAyF,QAAA,EAAElH,WAAW,GAAG,aAAa,GAAG;gBAAe;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cACpF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,eACN;UACH,GAzGOc,KAAK;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0GV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENnI,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAyF,QAAA,gBAC1B1H,OAAA,CAAC/B,eAAe;YAACgE,SAAS,EAAC;UAAY;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CnI,OAAA;YAAA0H,QAAA,EAAI;UAAkB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BnI,OAAA;YAAA0H,QAAA,EAAG;UAA4D;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnEnI,OAAA;YAAGiC,SAAS,EAAC,YAAY;YAAAyF,QAAA,EAAC;UAA2C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAMNnI,OAAA,CAAClC,QAAQ;QACP6E,WAAW,EAAEA,WAAY;QACzBuG,UAAU,EAAEA,CAAA,KAAM;UAChBtG,cAAc,CAAC,KAAK,CAAC;UACrBE,cAAc,CAAC,EAAE,CAAC;QACpB,CAAE;QACFD,WAAW,EAAEA;MAAY;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/H,EAAA,CAz4BQD,aAAa;EAAA,QACHzC,WAAW,EACeG,WAAW,EACrCJ,WAAW;AAAA;AAAA0L,EAAA,GAHrBhJ,aAAa;AA24BtB,eAAeA,aAAa;AAAC,IAAAgJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}