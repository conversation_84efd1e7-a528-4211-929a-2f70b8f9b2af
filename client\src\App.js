import React, { Suspense, lazy } from "react";
import "./stylesheets/theme.css";
import "./stylesheets/alignments.css";
import "./stylesheets/textelements.css";
import "./stylesheets/form-elements.css";
import "./stylesheets/custom-components.css";
import "./stylesheets/layout.css";
import "./styles/modern.css";
import "./styles/animations.css";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import ProtectedRoute from "./components/ProtectedRoute";
import Loader from "./components/Loader";
import { useSelector } from "react-redux";
import { ThemeProvider } from "./contexts/ThemeContext";
import { LanguageProvider } from "./contexts/LanguageContext";
import { ErrorBoundary } from "./components/modern";
import AdminProtectedRoute from "./components/AdminProtectedRoute";

// Immediate load components (critical for initial render)
import Login from "./pages/common/Login";
import Register from "./pages/common/Register";
import Home from "./pages/common/Home";

// Lazy load components for better performance
const Quiz = lazy(() => import("./pages/user/Quiz"));
const QuizPlay = lazy(() => import("./pages/user/Quiz/QuizPlay"));
const QuizResult = lazy(() => import("./pages/user/Quiz/QuizResult"));
const Exams = lazy(() => import("./pages/admin/Exams"));
const AddEditExam = lazy(() => import("./pages/admin/Exams/AddEditExam"));
const Users = lazy(() => import("./pages/admin/Users"));
const AdminDashboard = lazy(() => import("./pages/admin/Dashboard"));

const WriteExam = lazy(() => import("./pages/user/WriteExam"));
const UserReports = lazy(() => import("./pages/user/UserReports"));
const AdminReports = lazy(() => import("./pages/admin/AdminReports"));
const StudyMaterial = lazy(() => import("./pages/user/StudyMaterial"));
const VideoLessons = lazy(() => import("./pages/user/VideoLessons"));
const Skills = lazy(() => import("./pages/user/Skills"));
const Ranking = lazy(() => import("./pages/user/Ranking"));
const RankingErrorBoundary = lazy(() => import("./components/RankingErrorBoundary"));
const Profile = lazy(() => import("./pages/common/Profile"));

const Forum = lazy(() => import("./pages/common/Forum"));
const Test = lazy(() => import("./pages/user/Test"));
const Subscription = lazy(() => import("./pages/user/Subscription"));

const Hub = lazy(() => import("./pages/user/Hub"));
const AdminStudyMaterials = lazy(() => import("./pages/admin/StudyMaterials"));
const AdminSkills = lazy(() => import("./pages/admin/Skills"));
const AdminVideos = lazy(() => import("./pages/admin/Videos"));
const AdminVideoLessons = lazy(() => import("./pages/admin/VideoLessons"));
const AdminProfile = lazy(() => import("./pages/admin/Profile"));
const AdminNotifications = lazy(() => import("./pages/admin/Notifications/AdminNotifications"));
const AdminForum = lazy(() => import("./pages/admin/Forum"));
const DebugAuth = lazy(() => import("./components/DebugAuth"));
const RankingDemo = lazy(() => import("./components/modern/RankingDemo"));
const MathTest = lazy(() => import("./components/MathTest"));

// Global error handler for CSS style errors and null reference errors
window.addEventListener('error', (event) => {
  if (event.message && (
    event.message.includes('Indexed property setter is not supported') ||
    event.message.includes('Cannot read properties of null') ||
    event.message.includes('Cannot read property \'style\'')
  )) {
    console.warn('DOM/Style Error caught and handled:', event.message);
    event.preventDefault();
    return false;
  }
});

// Handle unhandled promise rejections that might be related to style errors
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message && (
    event.reason.message.includes('Indexed property setter is not supported') ||
    event.reason.message.includes('Cannot read properties of null') ||
    event.reason.message.includes('Cannot read property \'style\'')
  )) {
    console.warn('DOM/Style Promise Rejection caught and handled:', event.reason.message);
    event.preventDefault();
  }
});
// Fast loading component for lazy routes
const FastLoader = () => (
  <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <div className="text-center">
      <div className="relative">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <div className="absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse"></div>
      </div>
      <p className="text-gray-600 font-medium">Loading page...</p>
      <p className="text-gray-400 text-sm mt-2">Please wait a moment</p>
    </div>
  </div>
);

function App() {
  const { loading } = useSelector((state) => state.loader);

  // Global mobile responsive fixes for ALL pages
  React.useEffect(() => {
    const globalMobileStyles = document.createElement('style');
    globalMobileStyles.textContent = `
      /* GLOBAL MOBILE RESPONSIVE FIXES FOR ALL PAGES */
      @media (max-width: 768px) {
        /* HIDE hamburger menu completely on mobile */
        .lg\\:hidden {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
        }

        .lg\\:hidden button {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
        }

        /* Desktop-like header layout for mobile on ALL pages */
        .nav-modern,
        header,
        .safe-header-animation {
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          height: 48px !important;
          min-height: 48px !important;
          max-height: 48px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: space-between !important;
          padding: 0 16px !important;
          z-index: 10000 !important;
          background: rgba(255, 255, 255, 0.98) !important;
          backdrop-filter: blur(15px) !important;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
          box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1) !important;
        }

        /* Header content layout for ALL pages */
        .nav-modern .flex,
        header .flex {
          width: 100% !important;
          display: flex !important;
          align-items: center !important;
          justify-content: space-between !important;
          height: 48px !important;
        }

        /* Logo section - NO space for hamburger menu */
        .nav-modern .flex > div:first-child,
        header .flex > div:first-child {
          flex: 1 !important;
          display: flex !important;
          align-items: center !important;
          margin-left: 0px !important;
        }

        /* BRAINWAVE text - force single line */
        .brainwave-heading-enhanced,
        .brainwave-heading,
        h1 {
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          font-size: 1.1rem !important;
          line-height: 1.2 !important;
        }

        /* Right section (bell + profile) */
        .nav-modern .flex > div:last-child,
        header .flex > div:last-child {
          display: flex !important;
          align-items: center !important;
          gap: 6px !important;
        }

        /* Bell icon size reduction - make it smaller */
        .notification-bell-button .w-5,
        .notification-bell-button .h-5 {
          width: 12px !important;
          height: 12px !important;
        }

        .notification-bell-button {
          padding: 4px !important;
          width: 24px !important;
          height: 24px !important;
        }

        /* Force sidebar text to be white on ALL pages */
        .sidebar,
        .mobile-sidebar,
        .modern-sidebar,
        [class*="sidebar"],
        .ant-menu,
        .ant-menu-item {
          background: rgba(0, 0, 0, 0.95) !important;
          color: #ffffff !important;
          z-index: 8888 !important;
        }

        .sidebar *,
        .mobile-sidebar *,
        .modern-sidebar *,
        [class*="sidebar"] *,
        .ant-menu *,
        .ant-menu-item *,
        .sidebar .menu-item,
        .mobile-sidebar .menu-item,
        .modern-sidebar .menu-item,
        .sidebar a,
        .mobile-sidebar a,
        .modern-sidebar a,
        .sidebar span,
        .mobile-sidebar span,
        .modern-sidebar span,
        .sidebar div,
        .mobile-sidebar div,
        .modern-sidebar div,
        .sidebar p,
        .mobile-sidebar p,
        .modern-sidebar p,
        .sidebar h1, .sidebar h2, .sidebar h3, .sidebar h4, .sidebar h5, .sidebar h6,
        .mobile-sidebar h1, .mobile-sidebar h2, .mobile-sidebar h3, .mobile-sidebar h4, .mobile-sidebar h5, .mobile-sidebar h6,
        .modern-sidebar h1, .modern-sidebar h2, .modern-sidebar h3, .modern-sidebar h4, .modern-sidebar h5, .modern-sidebar h6,
        .sidebar .text-gray-100,
        .sidebar .text-gray-200,
        .sidebar .text-gray-300,
        .sidebar .text-gray-400,
        .sidebar .text-gray-500,
        .sidebar .text-gray-600,
        .sidebar .text-gray-700,
        .sidebar .text-gray-800,
        .sidebar .text-gray-900,
        .mobile-sidebar .text-gray-100,
        .mobile-sidebar .text-gray-200,
        .mobile-sidebar .text-gray-300,
        .mobile-sidebar .text-gray-400,
        .mobile-sidebar .text-gray-500,
        .mobile-sidebar .text-gray-600,
        .mobile-sidebar .text-gray-700,
        .mobile-sidebar .text-gray-800,
        .mobile-sidebar .text-gray-900,
        .modern-sidebar .text-gray-100,
        .modern-sidebar .text-gray-200,
        .modern-sidebar .text-gray-300,
        .modern-sidebar .text-gray-400,
        .modern-sidebar .text-gray-500,
        .modern-sidebar .text-gray-600,
        .modern-sidebar .text-gray-700,
        .modern-sidebar .text-gray-800,
        .modern-sidebar .text-gray-900 {
          color: #ffffff !important;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
          font-weight: 500 !important;
        }

        /* Sidebar hover effects */
        .sidebar .menu-item:hover,
        .mobile-sidebar .menu-item:hover,
        .modern-sidebar .menu-item:hover,
        .sidebar a:hover,
        .mobile-sidebar a:hover,
        .modern-sidebar a:hover {
          color: #ffffff !important;
          background: rgba(255, 255, 255, 0.2) !important;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
        }

        /* Adjust ALL page content to not overlap with header */
        .min-h-screen,
        .study-material-modern,
        .container-modern,
        .layout,
        .layout-modern,
        main,
        .body,
        .video-lessons-container {
          margin-top: 48px !important;
          padding-top: 8px !important;
        }

        /* Remove gaps between header and content on ALL pages */
        .modern-header,
        .page-header,
        .content-header {
          margin-top: 0px !important;
          padding-top: 8px !important;
        }

        .tabs-container,
        .content-container,
        .material-grid,
        .pdf-grid {
          margin-top: 0px !important;
          padding-top: 8px !important;
        }

        /* Center ALL modals */
        .ant-modal,
        .modal,
        .pdf-modal,
        .study-modal,
        .material-modal,
        .quiz-modal,
        .marking-modal,
        .result-modal {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          top: 0 !important;
          padding-top: 0 !important;
        }

        .ant-modal-content,
        .modal-content,
        .pdf-modal-content,
        .study-modal-content,
        .material-modal-content,
        .quiz-modal-content,
        .marking-modal-content,
        .result-modal-content {
          margin: 0 auto !important;
          position: relative !important;
          top: auto !important;
          transform: none !important;
        }

        .ant-modal-wrap {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          min-height: 100vh !important;
        }
      }
    `;
    document.head.appendChild(globalMobileStyles);

    return () => {
      document.head.removeChild(globalMobileStyles);
    };
  }, []);

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <LanguageProvider>
          {loading && <Loader />}
        <BrowserRouter>
        <Routes>
          {/* Common Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/" element={<Home />} />
          <Route path="/ranking-demo" element={<RankingDemo />} />



          <Route path="/test" element={
            <Suspense fallback={<FastLoader />}>
              <Test />
            </Suspense>
          } />
          <Route
            path="/forum"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <Forum />
                </Suspense>
              </ProtectedRoute>
            }
          />

          {/* User Routes */}
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <Profile />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/profile"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <Profile />
                </Suspense>
              </ProtectedRoute>
            }
          />

          <Route
            path="/subscription"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <Subscription />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/subscription"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <Subscription />
                </Suspense>
              </ProtectedRoute>
            }
          />

          <Route
            path="/user/hub"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <Hub />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/quiz"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <Quiz />
                </Suspense>
              </ProtectedRoute>
            }
          />

          <Route
            path="/user/write-exam/:id"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <WriteExam />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/quiz/:id/result"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <QuizResult />
                </Suspense>
              </ProtectedRoute>
            }
          />

          {/* New Quiz Routes */}

          <Route
            path="/quiz/:id/play"
            element={
              <ProtectedRoute>
                <QuizPlay />
              </ProtectedRoute>
            }
          />

          {/* Math Test Route */}
          <Route
            path="/math-test"
            element={
              <Suspense fallback={<FastLoader />}>
                <MathTest />
              </Suspense>
            }
          />

          <Route
            path="/user/reports"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <UserReports />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/study-material"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <StudyMaterial />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/video-lessons"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <VideoLessons />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/skills"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <Skills />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/ranking"
            element={
              <ProtectedRoute>
                <Suspense fallback={<FastLoader />}>
                  <RankingErrorBoundary>
                    <Ranking />
                  </RankingErrorBoundary>
                </Suspense>
              </ProtectedRoute>
            }
          />


          {/* Admin Routes */}
          <Route
            path="/admin/dashboard"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminDashboard />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/users"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <Users />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/exams"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <Exams />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/exams/add"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AddEditExam />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/exams/edit/:id"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AddEditExam />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/study-materials"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminStudyMaterials />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/skills"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminSkills />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/videos"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminVideos />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/video-lessons"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminVideoLessons />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/reports"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminReports />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/notifications"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminNotifications />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/profile"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminProfile />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/forum"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminForum />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/debug"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <DebugAuth />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />
        </Routes>
      </BrowserRouter>
        </LanguageProvider>
    </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;