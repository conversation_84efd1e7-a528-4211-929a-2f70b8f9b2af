{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\VideoGrid.js\";\nimport React from 'react';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoGrid = ({\n  paginatedVideos,\n  currentVideoIndex,\n  handleShowVideo,\n  getThumbnailUrl,\n  getSubjectName,\n  selectedLevel,\n  isKiswahili,\n  setVideoRef,\n  setVideoError,\n  videoError,\n  setCurrentVideoIndex,\n  commentsExpanded,\n  setCommentsExpanded,\n  getCurrentVideoComments,\n  newComment,\n  setNewComment,\n  handleAddComment,\n  handleLikeComment,\n  handleDeleteComment,\n  formatTimeAgo,\n  user\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"videos-grid\",\n    children: paginatedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: paginatedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card\",\n            onClick: () => handleShowVideo(index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-thumbnail\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getThumbnailUrl(video),\n                alt: video.title,\n                className: \"thumbnail-image\",\n                loading: \"lazy\",\n                onError: e => {\n                  // Fallback logic for failed thumbnails\n                  if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                    // For YouTube videos, try different quality thumbnails\n                    let videoId = video.videoID;\n                    if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                      videoId = match ? match[1] : videoId;\n                    }\n                    const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                    const currentSrc = e.target.src;\n                    const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                    if (currentIndex < fallbacks.length - 1) {\n                      e.target.src = fallbacks[currentIndex + 1];\n                    }\n                  } else {\n                    e.target.src = '/api/placeholder/320/180';\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"play-overlay\",\n                children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                  className: \"play-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-duration\",\n                children: video.duration || \"Video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 21\n              }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"subtitle-badge\",\n                children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 25\n                }, this), \"CC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"video-title\",\n                children: video.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-subject\",\n                  children: getSubjectName(video.subject)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-class\",\n                  children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-tags\",\n                children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"topic-tag\",\n                  children: video.topic\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 39\n                }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"shared-tag\",\n                  children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 17\n          }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-video-player\",\n            children: [video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n              ref: ref => setVideoRef(ref),\n              controls: true,\n              autoPlay: true,\n              playsInline: true,\n              preload: \"metadata\",\n              width: \"100%\",\n              height: \"100%\",\n              poster: getThumbnailUrl(video),\n              style: {\n                width: '100%',\n                height: '100%',\n                backgroundColor: '#000',\n                objectFit: 'contain'\n              },\n              onError: e => {\n                setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n              },\n              onCanPlay: () => {\n                setVideoError(null);\n              },\n              crossOrigin: \"anonymous\",\n              children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                src: video.signedVideoUrl || video.videoUrl,\n                type: \"video/mp4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 25\n              }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, subIndex) => /*#__PURE__*/_jsxDEV(\"track\", {\n                kind: \"subtitles\",\n                src: subtitle.url,\n                srcLang: subtitle.language,\n                label: subtitle.languageName,\n                default: subtitle.isDefault || subIndex === 0\n              }, `${subtitle.language}-${subIndex}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 27\n              }, this)), \"Your browser does not support the video tag.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 23\n            }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n              src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n              title: video.title,\n              frameBorder: \"0\",\n              allowFullScreen: true,\n              style: {\n                width: '100%',\n                height: '100%',\n                border: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 23\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-error\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"error-icon\",\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Video Unavailable\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: videoError || \"This video cannot be played at the moment.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"youtube-video-actions-horizontal\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `youtube-action-btn-small ${commentsExpanded ? 'active' : ''}`,\n                onClick: () => setCommentsExpanded(!commentsExpanded),\n                children: [\"\\uD83D\\uDCAC Comments (\", getCurrentVideoComments().length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"youtube-action-btn-small\",\n                children: \"\\uD83D\\uDC4D Like\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"youtube-action-btn-small close-btn\",\n                onClick: () => setCurrentVideoIndex(null),\n                children: \"\\u2715 Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 13\n      }, this))\n    }, void 0, false) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n        className: \"empty-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"suggestion\",\n        children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_c = VideoGrid;\nexport default VideoGrid;\nvar _c;\n$RefreshReg$(_c, \"VideoGrid\");", "map": {"version": 3, "names": ["React", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoGrid", "paginatedVideos", "currentVideoIndex", "handleShowVideo", "getThumbnailUrl", "getSubjectName", "selectedLevel", "isKiswahili", "setVideoRef", "setVideoError", "videoError", "setCurrentVideoIndex", "commentsExpanded", "setCommentsExpanded", "getCurrentVideoComments", "newComment", "setNewComment", "handleAddComment", "handleLikeComment", "handleDeleteComment", "formatTimeAgo", "user", "className", "children", "length", "map", "video", "index", "onClick", "src", "alt", "title", "loading", "onError", "e", "videoID", "includes", "videoId", "match", "fallbacks", "currentSrc", "target", "currentIndex", "findIndex", "url", "split", "pop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "duration", "subtitles", "subject", "class", "topic", "sharedFromClass", "videoUrl", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "signedVideoUrl", "type", "subtitle", "subIndex", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/VideoGrid.js"], "sourcesContent": ["import React from 'react';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle } from 'react-icons/tb';\n\nconst VideoGrid = ({\n  paginatedVideos,\n  currentVideoIndex,\n  handleShowVideo,\n  getThumbnailUrl,\n  getSubjectName,\n  selectedLevel,\n  isKiswahili,\n  setVideoRef,\n  setVideoError,\n  videoError,\n  setCurrentVideoIndex,\n  commentsExpanded,\n  setCommentsExpanded,\n  getCurrentVideoComments,\n  newComment,\n  setNewComment,\n  handleAddComment,\n  handleLikeComment,\n  handleDeleteComment,\n  formatTimeAgo,\n  user\n}) => {\n  return (\n    <div className=\"videos-grid\">\n      {paginatedVideos.length > 0 ? (\n        <>\n          {paginatedVideos.map((video, index) => (\n            <React.Fragment key={index}>\n              <div className=\"video-item\">\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = video.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            '/api/placeholder/320/180'\n                          ];\n\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          }\n                        } else {\n                          e.target.src = '/api/placeholder/320/180';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration\">\n                      {video.duration || \"Video\"}\n                    </div>\n                    {video.subtitles && video.subtitles.length > 0 && (\n                      <div className=\"subtitle-badge\">\n                        <TbInfoCircle />\n                        CC\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"video-card-content\">\n                    <h3 className=\"video-title\">{video.title}</h3>\n                    <div className=\"video-meta\">\n                      <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                      <span className=\"video-class\">\n                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                          (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`) :\n                          `Form ${video.className || video.class}`}\n                      </span>\n                    </div>\n                    <div className=\"video-tags\">\n                      {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                        <span className=\"shared-tag\">\n                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}{selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                            (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`) :\n                            `Form ${video.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    {video.videoUrl ? (\n                      <video\n                        ref={(ref) => setVideoRef(ref)}\n                        controls\n                        autoPlay\n                        playsInline\n                        preload=\"metadata\"\n                        width=\"100%\"\n                        height=\"100%\"\n                        poster={getThumbnailUrl(video)}\n                        style={{\n                          width: '100%',\n                          height: '100%',\n                          backgroundColor: '#000',\n                          objectFit: 'contain'\n                        }}\n                        onError={(e) => {\n                          setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                        }}\n                        onCanPlay={() => {\n                          setVideoError(null);\n                        }}\n                        crossOrigin=\"anonymous\"\n                      >\n                        <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                        {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, subIndex) => (\n                          <track\n                            key={`${subtitle.language}-${subIndex}`}\n                            kind=\"subtitles\"\n                            src={subtitle.url}\n                            srcLang={subtitle.language}\n                            label={subtitle.languageName}\n                            default={subtitle.isDefault || subIndex === 0}\n                          />\n                        ))}\n                        Your browser does not support the video tag.\n                      </video>\n                    ) : video.videoID ? (\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        style={{ \n                          width: '100%', \n                          height: '100%', \n                          border: 'none'\n                        }}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                      </div>\n                    )}\n                    \n                    {/* Horizontal Action Buttons */}\n                    <div className=\"youtube-video-actions-horizontal\">\n                      <button\n                        className={`youtube-action-btn-small ${commentsExpanded ? 'active' : ''}`}\n                        onClick={() => setCommentsExpanded(!commentsExpanded)}\n                      >\n                        💬 Comments ({getCurrentVideoComments().length})\n                      </button>\n                      <button className=\"youtube-action-btn-small\">\n                        👍 Like\n                      </button>\n                      <button\n                        className=\"youtube-action-btn-small close-btn\"\n                        onClick={() => setCurrentVideoIndex(null)}\n                      >\n                        ✕ Close\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </React.Fragment>\n          ))}\n        </>\n      ) : (\n        <div className=\"empty-state\">\n          <FaGraduationCap className=\"empty-icon\" />\n          <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n          <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n          <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default VideoGrid;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,SAAS,GAAGA,CAAC;EACjBC,eAAe;EACfC,iBAAiB;EACjBC,eAAe;EACfC,eAAe;EACfC,cAAc;EACdC,aAAa;EACbC,WAAW;EACXC,WAAW;EACXC,aAAa;EACbC,UAAU;EACVC,oBAAoB;EACpBC,gBAAgB;EAChBC,mBAAmB;EACnBC,uBAAuB;EACvBC,UAAU;EACVC,aAAa;EACbC,gBAAgB;EAChBC,iBAAiB;EACjBC,mBAAmB;EACnBC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,oBACExB,OAAA;IAAKyB,SAAS,EAAC,aAAa;IAAAC,QAAA,EACzBtB,eAAe,CAACuB,MAAM,GAAG,CAAC,gBACzB3B,OAAA,CAAAE,SAAA;MAAAwB,QAAA,EACGtB,eAAe,CAACwB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAChC9B,OAAA,CAACL,KAAK,CAACM,QAAQ;QAAAyB,QAAA,eACb1B,OAAA;UAAKyB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1B,OAAA;YAAKyB,SAAS,EAAC,YAAY;YAACM,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAACwB,KAAK,CAAE;YAAAJ,QAAA,gBAChE1B,OAAA;cAAKyB,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC1B,OAAA;gBACEgC,GAAG,EAAEzB,eAAe,CAACsB,KAAK,CAAE;gBAC5BI,GAAG,EAAEJ,KAAK,CAACK,KAAM;gBACjBT,SAAS,EAAC,iBAAiB;gBAC3BU,OAAO,EAAC,MAAM;gBACdC,OAAO,EAAGC,CAAC,IAAK;kBACd;kBACA,IAAIR,KAAK,CAACS,OAAO,IAAI,CAACT,KAAK,CAACS,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;oBAC7D;oBACA,IAAIC,OAAO,GAAGX,KAAK,CAACS,OAAO;oBAC3B,IAAIE,OAAO,CAACD,QAAQ,CAAC,aAAa,CAAC,IAAIC,OAAO,CAACD,QAAQ,CAAC,UAAU,CAAC,EAAE;sBACnE,MAAME,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;sBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;oBACtC;oBAEA,MAAME,SAAS,GAAG,CACf,8BAA6BF,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;oBAED,MAAMG,UAAU,GAAGN,CAAC,CAACO,MAAM,CAACZ,GAAG;oBAC/B,MAAMa,YAAY,GAAGH,SAAS,CAACI,SAAS,CAACC,GAAG,IAAIJ,UAAU,CAACJ,QAAQ,CAACQ,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAE1F,IAAIJ,YAAY,GAAGH,SAAS,CAACf,MAAM,GAAG,CAAC,EAAE;sBACvCU,CAAC,CAACO,MAAM,CAACZ,GAAG,GAAGU,SAAS,CAACG,YAAY,GAAG,CAAC,CAAC;oBAC5C;kBACF,CAAC,MAAM;oBACLR,CAAC,CAACO,MAAM,CAACZ,GAAG,GAAG,0BAA0B;kBAC3C;gBACF;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFrD,OAAA;gBAAKyB,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B1B,OAAA,CAACJ,YAAY;kBAAC6B,SAAS,EAAC;gBAAW;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNrD,OAAA;gBAAKyB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC5BG,KAAK,CAACyB,QAAQ,IAAI;cAAO;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EACLxB,KAAK,CAAC0B,SAAS,IAAI1B,KAAK,CAAC0B,SAAS,CAAC5B,MAAM,GAAG,CAAC,iBAC5C3B,OAAA;gBAAKyB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B1B,OAAA,CAACF,YAAY;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,MAElB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNrD,OAAA;cAAKyB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjC1B,OAAA;gBAAIyB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEG,KAAK,CAACK;cAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CrD,OAAA;gBAAKyB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB1B,OAAA;kBAAMyB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAElB,cAAc,CAACqB,KAAK,CAAC2B,OAAO;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtErD,OAAA;kBAAMyB,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAC1BjB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClEC,WAAW,GAAI,aAAYmB,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC4B,KAAM,EAAC,GAAI,SAAQ5B,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC4B,KAAM,EAAC,GACvG,QAAO5B,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC4B,KAAM;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrD,OAAA;gBAAKyB,SAAS,EAAC,YAAY;gBAAAC,QAAA,GACxBG,KAAK,CAAC6B,KAAK,iBAAI1D,OAAA;kBAAMyB,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEG,KAAK,CAAC6B;gBAAK;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/DxB,KAAK,CAAC8B,eAAe,IAAI9B,KAAK,CAAC8B,eAAe,MAAM9B,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC4B,KAAK,CAAC,iBAClFzD,OAAA;kBAAMyB,SAAS,EAAC,YAAY;kBAAAC,QAAA,GACzBhB,WAAW,GAAG,qBAAqB,GAAG,cAAc,EAAED,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GACxHC,WAAW,GAAI,aAAYmB,KAAK,CAAC8B,eAAgB,EAAC,GAAI,SAAQ9B,KAAK,CAAC8B,eAAgB,EAAC,GACrF,QAAO9B,KAAK,CAAC8B,eAAgB,EAAC;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLhD,iBAAiB,KAAKyB,KAAK,iBAC1B9B,OAAA;YAAKyB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,GACjCG,KAAK,CAAC+B,QAAQ,gBACb5D,OAAA;cACE6D,GAAG,EAAGA,GAAG,IAAKlD,WAAW,CAACkD,GAAG,CAAE;cAC/BC,QAAQ;cACRC,QAAQ;cACRC,WAAW;cACXC,OAAO,EAAC,UAAU;cAClBC,KAAK,EAAC,MAAM;cACZC,MAAM,EAAC,MAAM;cACbC,MAAM,EAAE7D,eAAe,CAACsB,KAAK,CAAE;cAC/BwC,KAAK,EAAE;gBACLH,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAE,MAAM;gBACvBC,SAAS,EAAE;cACb,CAAE;cACFnC,OAAO,EAAGC,CAAC,IAAK;gBACdzB,aAAa,CAAE,yBAAwBiB,KAAK,CAACK,KAAM,mCAAkC,CAAC;cACxF,CAAE;cACFsC,SAAS,EAAEA,CAAA,KAAM;gBACf5D,aAAa,CAAC,IAAI,CAAC;cACrB,CAAE;cACF6D,WAAW,EAAC,WAAW;cAAA/C,QAAA,gBAEvB1B,OAAA;gBAAQgC,GAAG,EAAEH,KAAK,CAAC6C,cAAc,IAAI7C,KAAK,CAAC+B,QAAS;gBAACe,IAAI,EAAC;cAAW;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACvExB,KAAK,CAAC0B,SAAS,IAAI1B,KAAK,CAAC0B,SAAS,CAAC5B,MAAM,GAAG,CAAC,IAAIE,KAAK,CAAC0B,SAAS,CAAC3B,GAAG,CAAC,CAACgD,QAAQ,EAAEC,QAAQ,kBACvF7E,OAAA;gBAEE8E,IAAI,EAAC,WAAW;gBAChB9C,GAAG,EAAE4C,QAAQ,CAAC7B,GAAI;gBAClBgC,OAAO,EAAEH,QAAQ,CAACI,QAAS;gBAC3BC,KAAK,EAAEL,QAAQ,CAACM,YAAa;gBAC7BC,OAAO,EAAEP,QAAQ,CAACQ,SAAS,IAAIP,QAAQ,KAAK;cAAE,GALxC,GAAED,QAAQ,CAACI,QAAS,IAAGH,QAAS,EAAC;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMxC,CACF,CAAC,EAAC,8CAEL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,GACNxB,KAAK,CAACS,OAAO,gBACftC,OAAA;cACEgC,GAAG,EAAG,iCAAgCH,KAAK,CAACS,OAAQ,mBAAmB;cACvEJ,KAAK,EAAEL,KAAK,CAACK,KAAM;cACnBmD,WAAW,EAAC,GAAG;cACfC,eAAe;cACfjB,KAAK,EAAE;gBACLH,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdoB,MAAM,EAAE;cACV;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,gBAEVrD,OAAA;cAAKyB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1B,OAAA;gBAAKyB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpCrD,OAAA;gBAAA0B,QAAA,EAAI;cAAiB;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BrD,OAAA;gBAAA0B,QAAA,EAAIb,UAAU,IAAI;cAA4C;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CACN,eAGDrD,OAAA;cAAKyB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C1B,OAAA;gBACEyB,SAAS,EAAG,4BAA2BV,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;gBAC1EgB,OAAO,EAAEA,CAAA,KAAMf,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;gBAAAW,QAAA,GACvD,yBACc,EAACT,uBAAuB,CAAC,CAAC,CAACU,MAAM,EAAC,GACjD;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrD,OAAA;gBAAQyB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrD,OAAA;gBACEyB,SAAS,EAAC,oCAAoC;gBAC9CM,OAAO,EAAEA,CAAA,KAAMjB,oBAAoB,CAAC,IAAI,CAAE;gBAAAY,QAAA,EAC3C;cAED;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,GAvJavB,KAAK;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwJV,CACjB;IAAC,gBACF,CAAC,gBAEHrD,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B1B,OAAA,CAACH,eAAe;QAAC4B,SAAS,EAAC;MAAY;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CrD,OAAA;QAAA0B,QAAA,EAAKhB,WAAW,GAAG,6BAA6B,GAAG;MAAiB;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1ErD,OAAA;QAAA0B,QAAA,EAAIhB,WAAW,GAAG,kEAAkE,GAAG;MAA4D;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxJrD,OAAA;QAAGyB,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEhB,WAAW,GAAG,yCAAyC,GAAG;MAA6C;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpI;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACmC,EAAA,GAjMIrF,SAAS;AAmMf,eAAeA,SAAS;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}